// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_media_management_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AdminMediaManagementEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminMediaManagementEventCopyWith<$Res> {
  factory $AdminMediaManagementEventCopyWith(AdminMediaManagementEvent value,
          $Res Function(AdminMediaManagementEvent) then) =
      _$AdminMediaManagementEventCopyWithImpl<$Res, AdminMediaManagementEvent>;
}

/// @nodoc
class _$AdminMediaManagementEventCopyWithImpl<$Res,
        $Val extends AdminMediaManagementEvent>
    implements $AdminMediaManagementEventCopyWith<$Res> {
  _$AdminMediaManagementEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadPendingMediaImplCopyWith<$Res> {
  factory _$$LoadPendingMediaImplCopyWith(_$LoadPendingMediaImpl value,
          $Res Function(_$LoadPendingMediaImpl) then) =
      __$$LoadPendingMediaImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadPendingMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementEventCopyWithImpl<$Res,
        _$LoadPendingMediaImpl>
    implements _$$LoadPendingMediaImplCopyWith<$Res> {
  __$$LoadPendingMediaImplCopyWithImpl(_$LoadPendingMediaImpl _value,
      $Res Function(_$LoadPendingMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadPendingMediaImpl implements _LoadPendingMedia {
  const _$LoadPendingMediaImpl();

  @override
  String toString() {
    return 'AdminMediaManagementEvent.loadPendingMedia()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadPendingMediaImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) {
    return loadPendingMedia();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) {
    return loadPendingMedia?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) {
    if (loadPendingMedia != null) {
      return loadPendingMedia();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) {
    return loadPendingMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) {
    return loadPendingMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) {
    if (loadPendingMedia != null) {
      return loadPendingMedia(this);
    }
    return orElse();
  }
}

abstract class _LoadPendingMedia implements AdminMediaManagementEvent {
  const factory _LoadPendingMedia() = _$LoadPendingMediaImpl;
}

/// @nodoc
abstract class _$$ApproveMediaImplCopyWith<$Res> {
  factory _$$ApproveMediaImplCopyWith(
          _$ApproveMediaImpl value, $Res Function(_$ApproveMediaImpl) then) =
      __$$ApproveMediaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String mediaId, String adminId});
}

/// @nodoc
class __$$ApproveMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementEventCopyWithImpl<$Res, _$ApproveMediaImpl>
    implements _$$ApproveMediaImplCopyWith<$Res> {
  __$$ApproveMediaImplCopyWithImpl(
      _$ApproveMediaImpl _value, $Res Function(_$ApproveMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaId = null,
    Object? adminId = null,
  }) {
    return _then(_$ApproveMediaImpl(
      mediaId: null == mediaId
          ? _value.mediaId
          : mediaId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ApproveMediaImpl implements _ApproveMedia {
  const _$ApproveMediaImpl({required this.mediaId, required this.adminId});

  @override
  final String mediaId;
  @override
  final String adminId;

  @override
  String toString() {
    return 'AdminMediaManagementEvent.approveMedia(mediaId: $mediaId, adminId: $adminId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApproveMediaImpl &&
            (identical(other.mediaId, mediaId) || other.mediaId == mediaId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mediaId, adminId);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApproveMediaImplCopyWith<_$ApproveMediaImpl> get copyWith =>
      __$$ApproveMediaImplCopyWithImpl<_$ApproveMediaImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) {
    return approveMedia(mediaId, adminId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) {
    return approveMedia?.call(mediaId, adminId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) {
    if (approveMedia != null) {
      return approveMedia(mediaId, adminId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) {
    return approveMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) {
    return approveMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) {
    if (approveMedia != null) {
      return approveMedia(this);
    }
    return orElse();
  }
}

abstract class _ApproveMedia implements AdminMediaManagementEvent {
  const factory _ApproveMedia(
      {required final String mediaId,
      required final String adminId}) = _$ApproveMediaImpl;

  String get mediaId;
  String get adminId;

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApproveMediaImplCopyWith<_$ApproveMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RejectMediaImplCopyWith<$Res> {
  factory _$$RejectMediaImplCopyWith(
          _$RejectMediaImpl value, $Res Function(_$RejectMediaImpl) then) =
      __$$RejectMediaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String mediaId, String adminId, String reason});
}

/// @nodoc
class __$$RejectMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementEventCopyWithImpl<$Res, _$RejectMediaImpl>
    implements _$$RejectMediaImplCopyWith<$Res> {
  __$$RejectMediaImplCopyWithImpl(
      _$RejectMediaImpl _value, $Res Function(_$RejectMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaId = null,
    Object? adminId = null,
    Object? reason = null,
  }) {
    return _then(_$RejectMediaImpl(
      mediaId: null == mediaId
          ? _value.mediaId
          : mediaId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RejectMediaImpl implements _RejectMedia {
  const _$RejectMediaImpl(
      {required this.mediaId, required this.adminId, required this.reason});

  @override
  final String mediaId;
  @override
  final String adminId;
  @override
  final String reason;

  @override
  String toString() {
    return 'AdminMediaManagementEvent.rejectMedia(mediaId: $mediaId, adminId: $adminId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RejectMediaImpl &&
            (identical(other.mediaId, mediaId) || other.mediaId == mediaId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mediaId, adminId, reason);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RejectMediaImplCopyWith<_$RejectMediaImpl> get copyWith =>
      __$$RejectMediaImplCopyWithImpl<_$RejectMediaImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) {
    return rejectMedia(mediaId, adminId, reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) {
    return rejectMedia?.call(mediaId, adminId, reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) {
    if (rejectMedia != null) {
      return rejectMedia(mediaId, adminId, reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) {
    return rejectMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) {
    return rejectMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) {
    if (rejectMedia != null) {
      return rejectMedia(this);
    }
    return orElse();
  }
}

abstract class _RejectMedia implements AdminMediaManagementEvent {
  const factory _RejectMedia(
      {required final String mediaId,
      required final String adminId,
      required final String reason}) = _$RejectMediaImpl;

  String get mediaId;
  String get adminId;
  String get reason;

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RejectMediaImplCopyWith<_$RejectMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UploadMediaImplCopyWith<$Res> {
  factory _$$UploadMediaImplCopyWith(
          _$UploadMediaImpl value, $Res Function(_$UploadMediaImpl) then) =
      __$$UploadMediaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({File file, String title, String description, String adminId});
}

/// @nodoc
class __$$UploadMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementEventCopyWithImpl<$Res, _$UploadMediaImpl>
    implements _$$UploadMediaImplCopyWith<$Res> {
  __$$UploadMediaImplCopyWithImpl(
      _$UploadMediaImpl _value, $Res Function(_$UploadMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? file = null,
    Object? title = null,
    Object? description = null,
    Object? adminId = null,
  }) {
    return _then(_$UploadMediaImpl(
      file: null == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as File,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UploadMediaImpl implements _UploadMedia {
  const _$UploadMediaImpl(
      {required this.file,
      required this.title,
      required this.description,
      required this.adminId});

  @override
  final File file;
  @override
  final String title;
  @override
  final String description;
  @override
  final String adminId;

  @override
  String toString() {
    return 'AdminMediaManagementEvent.uploadMedia(file: $file, title: $title, description: $description, adminId: $adminId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UploadMediaImpl &&
            (identical(other.file, file) || other.file == file) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.adminId, adminId) || other.adminId == adminId));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, file, title, description, adminId);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UploadMediaImplCopyWith<_$UploadMediaImpl> get copyWith =>
      __$$UploadMediaImplCopyWithImpl<_$UploadMediaImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) {
    return uploadMedia(file, title, description, adminId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) {
    return uploadMedia?.call(file, title, description, adminId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) {
    if (uploadMedia != null) {
      return uploadMedia(file, title, description, adminId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) {
    return uploadMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) {
    return uploadMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) {
    if (uploadMedia != null) {
      return uploadMedia(this);
    }
    return orElse();
  }
}

abstract class _UploadMedia implements AdminMediaManagementEvent {
  const factory _UploadMedia(
      {required final File file,
      required final String title,
      required final String description,
      required final String adminId}) = _$UploadMediaImpl;

  File get file;
  String get title;
  String get description;
  String get adminId;

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UploadMediaImplCopyWith<_$UploadMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateMediaImplCopyWith<$Res> {
  factory _$$UpdateMediaImplCopyWith(
          _$UpdateMediaImpl value, $Res Function(_$UpdateMediaImpl) then) =
      __$$UpdateMediaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String mediaId, String title, String description, File? file});
}

/// @nodoc
class __$$UpdateMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementEventCopyWithImpl<$Res, _$UpdateMediaImpl>
    implements _$$UpdateMediaImplCopyWith<$Res> {
  __$$UpdateMediaImplCopyWithImpl(
      _$UpdateMediaImpl _value, $Res Function(_$UpdateMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaId = null,
    Object? title = null,
    Object? description = null,
    Object? file = freezed,
  }) {
    return _then(_$UpdateMediaImpl(
      mediaId: null == mediaId
          ? _value.mediaId
          : mediaId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      file: freezed == file
          ? _value.file
          : file // ignore: cast_nullable_to_non_nullable
              as File?,
    ));
  }
}

/// @nodoc

class _$UpdateMediaImpl implements _UpdateMedia {
  const _$UpdateMediaImpl(
      {required this.mediaId,
      required this.title,
      required this.description,
      this.file});

  @override
  final String mediaId;
  @override
  final String title;
  @override
  final String description;
  @override
  final File? file;

  @override
  String toString() {
    return 'AdminMediaManagementEvent.updateMedia(mediaId: $mediaId, title: $title, description: $description, file: $file)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateMediaImpl &&
            (identical(other.mediaId, mediaId) || other.mediaId == mediaId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.file, file) || other.file == file));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, mediaId, title, description, file);

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateMediaImplCopyWith<_$UpdateMediaImpl> get copyWith =>
      __$$UpdateMediaImplCopyWithImpl<_$UpdateMediaImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingMedia,
    required TResult Function(String mediaId, String adminId) approveMedia,
    required TResult Function(String mediaId, String adminId, String reason)
        rejectMedia,
    required TResult Function(
            File file, String title, String description, String adminId)
        uploadMedia,
    required TResult Function(
            String mediaId, String title, String description, File? file)
        updateMedia,
  }) {
    return updateMedia(mediaId, title, description, file);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingMedia,
    TResult? Function(String mediaId, String adminId)? approveMedia,
    TResult? Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult? Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult? Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
  }) {
    return updateMedia?.call(mediaId, title, description, file);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingMedia,
    TResult Function(String mediaId, String adminId)? approveMedia,
    TResult Function(String mediaId, String adminId, String reason)?
        rejectMedia,
    TResult Function(
            File file, String title, String description, String adminId)?
        uploadMedia,
    TResult Function(
            String mediaId, String title, String description, File? file)?
        updateMedia,
    required TResult orElse(),
  }) {
    if (updateMedia != null) {
      return updateMedia(mediaId, title, description, file);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPendingMedia value) loadPendingMedia,
    required TResult Function(_ApproveMedia value) approveMedia,
    required TResult Function(_RejectMedia value) rejectMedia,
    required TResult Function(_UploadMedia value) uploadMedia,
    required TResult Function(_UpdateMedia value) updateMedia,
  }) {
    return updateMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult? Function(_ApproveMedia value)? approveMedia,
    TResult? Function(_RejectMedia value)? rejectMedia,
    TResult? Function(_UploadMedia value)? uploadMedia,
    TResult? Function(_UpdateMedia value)? updateMedia,
  }) {
    return updateMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPendingMedia value)? loadPendingMedia,
    TResult Function(_ApproveMedia value)? approveMedia,
    TResult Function(_RejectMedia value)? rejectMedia,
    TResult Function(_UploadMedia value)? uploadMedia,
    TResult Function(_UpdateMedia value)? updateMedia,
    required TResult orElse(),
  }) {
    if (updateMedia != null) {
      return updateMedia(this);
    }
    return orElse();
  }
}

abstract class _UpdateMedia implements AdminMediaManagementEvent {
  const factory _UpdateMedia(
      {required final String mediaId,
      required final String title,
      required final String description,
      final File? file}) = _$UpdateMediaImpl;

  String get mediaId;
  String get title;
  String get description;
  File? get file;

  /// Create a copy of AdminMediaManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateMediaImplCopyWith<_$UpdateMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
