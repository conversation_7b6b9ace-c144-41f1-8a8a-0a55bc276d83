import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../shared/models/order/order_model.dart';
import '../../../../shared/services/order/order_service.dart';

import '../../widgets/common/loading_view.dart';
import '../../widgets/common/error_view.dart';
import '../../../../shared/ui_components/navigation/back_button_handler.dart';
import '../../buyer_routes.dart';
import '../../providers/auth_provider.dart';

// Provider to fetch orders with loading state
final ordersLoadingProvider =
    StreamProvider.autoDispose<List<OrderModel>>((ref) {
  final authState = ref.watch(authStateProvider);
  final userId = authState.user?.uid;

  if (userId == null) {
    return Stream.value([]);
  }

  // Use the shared OrderService to get a stream of orders
  final orderService = ref.watch(orderServiceProvider);

  // Use the getOrdersByBuyer method we added to OrderService
  return orderService.getOrdersByBuyer(userId);
});

class OrdersScreen extends ConsumerStatefulWidget {
  const OrdersScreen({super.key});

  @override
  ConsumerState<OrdersScreen> createState() => _OrdersScreenState();
}

class _OrdersScreenState extends ConsumerState<OrdersScreen> {
  @override
  void initState() {
    super.initState();
    // Refresh orders when the screen is first loaded
    _refreshOrders();
  }

  Future<void> _refreshOrders() async {
    if (mounted) {
      debugPrint('Refreshing orders...');
      // Add a small delay to ensure Firestore has the latest data
      await Future.delayed(const Duration(milliseconds: 500));
      // Invalidate the provider to refresh the data
      ref.invalidate(ordersLoadingProvider);
      debugPrint('Orders refresh initiated');
    }
  }

  // Get frequently purchased products from orders for "Buy again" section
  List<OrderItem> _getProductsFromOrders(List<OrderModel> orders) {
    // Create a map to track unique products by ID
    final Map<String, OrderItem> uniqueProducts = {};

    // Iterate through all orders and their items
    for (final order in orders) {
      if (order.status == OrderStatus.delivered) {
        for (final item in order.items) {
          // Add or update the product in our map
          uniqueProducts[item.productId] = item;
        }
      }
    }

    // Convert the map values to a list and limit to 10 items
    final productList = uniqueProducts.values.toList();
    if (productList.length > 10) {
      return productList.sublist(0, 10);
    }
    return productList;
  }

  @override
  Widget build(BuildContext context) {
    // Watch the orders loading provider
    final ordersAsync = ref.watch(ordersLoadingProvider);
    final theme = Theme.of(context);

    return BackButtonHandler(
      fallbackRoute: BuyerRoutes.home,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('My Orders'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, navigate to home
                context.go(BuyerRoutes.home);
              }
            },
          ),
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
        ),
        body: ordersAsync.when(
          data: (orders) {
            // Debug print to check if orders are being fetched
            debugPrint('Fetched ${orders.length} orders in the UI');
            for (final order in orders) {
              debugPrint('Order ID: ${order.id}, BuyerId: ${order.buyerId}, Status: ${order.status}, Total: ${order.total}');
            }
            if (orders.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_bag_outlined,
                      size: 64,
                      color: theme.colorScheme.secondary,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No orders yet',
                      style: theme.textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Your order history will appear here',
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => context.go(BuyerRoutes.home),
                      child: const Text('Start Shopping'),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: _refreshOrders,
              child: CustomScrollView(
                slivers: [
                  // Search bar
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 48,
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const SizedBox(width: 12),
                                  Icon(
                                    Icons.search,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Search all orders',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Container(
                            height: 48,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  'Filter',
                                  style: TextStyle(
                                    color: Colors.grey[800],
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 16,
                                  color: Colors.grey[800],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Buy again section
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        right: 16.0,
                        top: 8.0,
                        bottom: 8.0,
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Buy again',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              TextButton(
                                onPressed: () {
                                  // Navigate to buy again page
                                },
                                child: Text(
                                  'See more',
                                  style: TextStyle(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 120,
                            child: orders.isEmpty
                                ? Center(
                                    child: Text(
                                      'No products to show',
                                      style: TextStyle(color: Colors.grey[600]),
                                    ),
                                  )
                                : Builder(builder: (context) {
                                    final products =
                                        _getProductsFromOrders(orders);
                                    return products.isEmpty
                                        ? Center(
                                            child: Text(
                                              'No products to show',
                                              style: TextStyle(
                                                  color: Colors.grey[600]),
                                            ),
                                          )
                                        : ListView.builder(
                                            scrollDirection: Axis.horizontal,
                                            itemCount: products.length,
                                            itemBuilder: (context, index) {
                                              final product = products[index];
                                              return Container(
                                                width: 100,
                                                margin: const EdgeInsets.only(
                                                    right: 12),
                                                child: Column(
                                                  children: [
                                                    ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8),
                                                      child: product.imageUrl !=
                                                              null
                                                          ? Image.network(
                                                              product.imageUrl!,
                                                              height: 80,
                                                              width: 80,
                                                              fit: BoxFit.cover,
                                                              errorBuilder:
                                                                  (context,
                                                                      error,
                                                                      stackTrace) {
                                                                return Container(
                                                                  height: 80,
                                                                  width: 80,
                                                                  color: Colors
                                                                          .grey[
                                                                      300],
                                                                  child: const Icon(
                                                                      Icons
                                                                          .image_not_supported),
                                                                );
                                                              },
                                                            )
                                                          : Container(
                                                              height: 80,
                                                              width: 80,
                                                              color: Colors
                                                                  .grey[300],
                                                              child: const Icon(
                                                                  Icons.image),
                                                            ),
                                                    ),
                                                    const SizedBox(height: 4),
                                                    Text(
                                                      product.name
                                                          .split(' ')
                                                          .first,
                                                      style: theme
                                                          .textTheme.bodySmall,
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          );
                                  }),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Past three months label
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.only(
                        left: 16.0,
                        top: 16.0,
                        bottom: 8.0,
                      ),
                      child: Text(
                        'Past three months',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  // Orders list
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final order = orders[index];
                        return _buildOrderItem(context, order);
                      },
                      childCount: orders.length,
                    ),
                  ),
                ],
              ),
            );
          },
          loading: () => const LoadingView(),
          error: (error, stackTrace) {
            debugPrint('Error loading orders: $error');
            debugPrint('Stack trace: $stackTrace');
            return ErrorView(
              message: 'Failed to load orders. Please try again.',
              onRetry: _refreshOrders,
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, OrderModel order) {
    final theme = Theme.of(context);

    // Check if this is a shopping list order
    final isShoppingList = order.shoppingListId != null || order.orderType == 'shopping_list';

    // Get the first item image or use a placeholder
    String? imageUrl =
        order.items.isNotEmpty ? order.items.first.imageUrl : null;

    // Format delivery date
    String deliveryDate = '';
    if (order.status == OrderStatus.delivered) {
      deliveryDate = 'Delivered ${_formatDate(order.updatedAt)}';
    } else if (order.status == OrderStatus.cancelled) {
      deliveryDate = 'Cancelled';
    } else if (order.status == OrderStatus.inTransit ||
        order.status == OrderStatus.outForDelivery) {
      // Calculate a future date for "Arriving" (just for demo)
      final arrivalDate = DateTime.now().add(const Duration(days: 2));
      deliveryDate = 'Arriving ${DateFormat('EEEE').format(arrivalDate)}';
    } else {
      deliveryDate = 'Processing';
    }

    return InkWell(
      onTap: () {
        // Navigate to order details
        debugPrint('Navigating to order details with ID: ${order.id}');
        BuyerRoutes.navigateToOrderDetails(context, order.id);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image with shopping list indicator if needed
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: imageUrl != null
                      ? Image.network(
                          imageUrl,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildPlaceholderImage(order);
                          },
                        )
                      : _buildPlaceholderImage(order),
                ),
                // Shopping list badge
                if (isShoppingList)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.shopping_basket,
                        size: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 16),

            // Order details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order type indicator
                  if (isShoppingList)
                    Row(
                      children: [
                        Icon(
                          Icons.shopping_basket,
                          size: 14,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Shopping List',
                          style: TextStyle(
                            fontSize: 12,
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                  if (order.status == OrderStatus.cancelled)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Cancelled',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.error,
                          ),
                        ),
                        Text(
                          'If you were charged, a refund will be processed and credited to the original payment method within next 3-5 business days',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    )
                  else
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          order.items.isNotEmpty
                              ? order.items.first.name
                              : 'Product',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // Show item count for shopping lists
                        if (isShoppingList && order.items.length > 1)
                          Text(
                            '${order.items.length} items',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        Text(
                          deliveryDate,
                          style: TextStyle(
                            color: order.status == OrderStatus.inTransit ||
                                    order.status == OrderStatus.outForDelivery
                                ? theme.colorScheme.primary
                                : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Arrow icon
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(OrderModel order) {
    // Generate a color based on the order ID for variety
    final colorIndex = order.id.hashCode % Colors.primaries.length;
    final color = Colors.primaries[colorIndex];

    // Check if this is a shopping list order
    final isShoppingList = order.shoppingListId != null || order.orderType == 'shopping_list';

    // Use different icons for regular orders vs shopping lists
    final icon = isShoppingList ? Icons.shopping_basket : Icons.shopping_bag;

    return Container(
      width: 60,
      height: 60,
      color: Colors.grey[200],
      child: Center(
        child: Icon(
          icon,
          color: color,
          size: 30,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
