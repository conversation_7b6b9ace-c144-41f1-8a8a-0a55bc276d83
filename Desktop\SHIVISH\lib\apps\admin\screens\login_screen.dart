import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../admin_routes.dart';
import '../../../shared/screens/auth/enhanced_login_screen.dart';
import '../bloc/auth/auth_bloc.dart';

class AdminLoginScreen extends StatefulWidget {
  final VoidCallback? onLoginSuccess;
  final bool isAdmin;

  const AdminLoginScreen({
    super.key,
    this.onLoginSuccess,
    this.isAdmin = true,
  });

  @override
  State<AdminLoginScreen> createState() => _AdminLoginScreenState();
}

class _AdminLoginScreenState extends State<AdminLoginScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _signIn() async {
    // Skip form validation since we're using the EnhancedLoginScreen
    // which handles its own validation

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('Admin login: Attempting to sign in with entered credentials');
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Use the AuthBloc to sign in
      context.read<AuthBloc>().add(SignInEvent(
            email: email,
            password: password,
          ));

      // Note: Navigation will be handled by the BlocListener in the build method
    } catch (e) {
      debugPrint('Admin login error: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Login failed: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  void _onRegisterPressed() {
    context.go(AdminRoutes.register);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get theme colors
    final primaryColor = theme.colorScheme.primary;
    final secondaryColor = theme.colorScheme.secondary;

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoadingState) {
          setState(() {
            _isLoading = true;
          });
        } else if (state is AuthAuthenticatedState) {
          setState(() {
            _isLoading = false;
          });
          if (widget.onLoginSuccess != null) {
            debugPrint('Admin login: Calling onLoginSuccess callback');
            widget.onLoginSuccess!();
          } else {
            debugPrint('Admin login: Navigating to home screen');
            context.go(AdminRoutes.home);
          }
        } else if (state is AuthErrorState) {
          setState(() {
            _isLoading = false;
            _errorMessage = state.message;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Login failed: ${state.message}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        } else if (state is AuthUnauthenticatedState) {
          setState(() {
            _isLoading = false;
          });
        }
      },
      child: EnhancedLoginScreen(
        flavor: 'admin',
        appName: 'Shivish Admin',
        primaryColor: primaryColor,
        secondaryColor: secondaryColor,
        onLoginPressed: (email, password) {
          _emailController.text = email;
          _passwordController.text = password;
          _signIn();
        },
        onRegisterPressed: _onRegisterPressed,
        isLoading: _isLoading,
        errorMessage: _errorMessage,
      ),
    );
  }
}
