#!/bin/sh

# Name of the resource to copy
RESOURCE_NAME="GoogleService-Info.plist"

# Get configuration
CONFIGURATION=$CONFIGURATION

# Source and destination paths
RESOURCES_PATH="${PROJECT_DIR}/config"
DESTINATION_PATH="${BUILT_PRODUCTS_DIR}/${productName}.app"

# Choose the right source file based on configuration
if [ "$CONFIGURATION" == "Debug" ] || [ "$CONFIGURATION" == "Debug-dev" ]; then
  SOURCE_FILE="${RESOURCES_PATH}/dev/${RESOURCE_NAME}"
else
  SOURCE_FILE="${RESOURCES_PATH}/prod/${RESOURCE_NAME}"
fi

# Copy the file
echo "Copying ${RESOURCE_NAME} for ${CONFIGURATION} configuration"
echo "From: ${SOURCE_FILE}"
echo "To: ${DESTINATION_PATH}/${RESOURCE_NAME}"

cp "${SOURCE_FILE}" "${DESTINATION_PATH}/${RESOURCE_NAME}" 