import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/voice_command_training/voice_command_training_bloc.dart';
import 'package:shivish/apps/admin/bloc/voice_command_training/voice_command_training_event.dart';
import 'package:shivish/apps/admin/bloc/voice_command_training/voice_command_training_state.dart';
import 'package:shivish/shared/models/voice_command_training.dart';
import 'package:shivish/shared/ui_components/dialogs/voice_command_training_form_dialog.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';

class VoiceCommandTrainingManagementScreen extends StatelessWidget {
  const VoiceCommandTrainingManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Voice Command Training'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<VoiceCommandTrainingBloc, VoiceCommandTrainingState>(
        builder: (context, state) {
          return state.when(
            initial: () => const Center(child: Text('No voice commands found')),
            loading: () => const LoadingIndicator(),
            loaded: (trainings) => _buildVoiceCommandList(context, trainings),
            error: (message) => ErrorMessage(message: message),
          );
        },
      ),
    );
  }

  Widget _buildVoiceCommandList(
    BuildContext context,
    List<VoiceCommandTraining> trainings,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<VoiceCommandTrainingBloc>().add(
              const VoiceCommandTrainingEvent.loadVoiceCommandTrainings(),
            );
      },
      child: ListView.builder(
        itemCount: trainings.length,
        itemBuilder: (context, index) {
          final training = trainings[index];
          return ListTile(
            title: Text(training.command),
            subtitle: Text(training.description),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                StatusBadge(
                  isActive: training.isEnabled,
                  onToggle: () =>
                      _changeStatus(context, training, training.isEnabled),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditDialog(context, training),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _showDeleteDialog(context, training),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _showCreateDialog(BuildContext context) async {
    final result = await showDialog<VoiceCommandTraining>(
      context: context,
      builder: (context) => const VoiceCommandTrainingFormDialog(),
    );

    if (result != null) {
      context.read<VoiceCommandTrainingBloc>().add(
            VoiceCommandTrainingEvent.createVoiceCommandTraining(result),
          );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                SuccessMessage(message: 'Voice command created successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _showEditDialog(
    BuildContext context,
    VoiceCommandTraining training,
  ) async {
    final result = await showDialog<VoiceCommandTraining>(
      context: context,
      builder: (context) => VoiceCommandTrainingFormDialog(training: training),
    );

    if (result != null) {
      context.read<VoiceCommandTrainingBloc>().add(
            VoiceCommandTrainingEvent.updateVoiceCommandTraining(result),
          );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                SuccessMessage(message: 'Voice command updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _showDeleteDialog(
    BuildContext context,
    VoiceCommandTraining training,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Delete Voice Command',
        message: 'Are you sure you want to delete this voice command?',
        confirmText: 'Delete',
        cancelText: 'Cancel',
      ),
    );

    if (result == true) {
      context.read<VoiceCommandTrainingBloc>().add(
            VoiceCommandTrainingEvent.deleteVoiceCommandTraining(training.id),
          );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                SuccessMessage(message: 'Voice command deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _changeStatus(
    BuildContext context,
    VoiceCommandTraining training,
    bool isEnabled,
  ) {
    context.read<VoiceCommandTrainingBloc>().add(
          VoiceCommandTrainingEvent.updateVoiceCommandTrainingStatus(
            training.id,
            isEnabled,
          ),
        );
  }
}
