// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ai_settings_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AISettingsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AISettingsEventCopyWith<$Res> {
  factory $AISettingsEventCopyWith(
          AISettingsEvent value, $Res Function(AISettingsEvent) then) =
      _$AISettingsEventCopyWithImpl<$Res, AISettingsEvent>;
}

/// @nodoc
class _$AISettingsEventCopyWithImpl<$Res, $Val extends AISettingsEvent>
    implements $AISettingsEventCopyWith<$Res> {
  _$AISettingsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadAISettingsImplCopyWith<$Res> {
  factory _$$LoadAISettingsImplCopyWith(_$LoadAISettingsImpl value,
          $Res Function(_$LoadAISettingsImpl) then) =
      __$$LoadAISettingsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadAISettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res, _$LoadAISettingsImpl>
    implements _$$LoadAISettingsImplCopyWith<$Res> {
  __$$LoadAISettingsImplCopyWithImpl(
      _$LoadAISettingsImpl _value, $Res Function(_$LoadAISettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadAISettingsImpl implements LoadAISettings {
  const _$LoadAISettingsImpl();

  @override
  String toString() {
    return 'AISettingsEvent.loadAISettings()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadAISettingsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return loadAISettings();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return loadAISettings?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (loadAISettings != null) {
      return loadAISettings();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return loadAISettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return loadAISettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (loadAISettings != null) {
      return loadAISettings(this);
    }
    return orElse();
  }
}

abstract class LoadAISettings implements AISettingsEvent {
  const factory LoadAISettings() = _$LoadAISettingsImpl;
}

/// @nodoc
abstract class _$$CreateAISettingsImplCopyWith<$Res> {
  factory _$$CreateAISettingsImplCopyWith(_$CreateAISettingsImpl value,
          $Res Function(_$CreateAISettingsImpl) then) =
      __$$CreateAISettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AISettings settings});

  $AISettingsCopyWith<$Res> get settings;
}

/// @nodoc
class __$$CreateAISettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res, _$CreateAISettingsImpl>
    implements _$$CreateAISettingsImplCopyWith<$Res> {
  __$$CreateAISettingsImplCopyWithImpl(_$CreateAISettingsImpl _value,
      $Res Function(_$CreateAISettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
  }) {
    return _then(_$CreateAISettingsImpl(
      null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as AISettings,
    ));
  }

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AISettingsCopyWith<$Res> get settings {
    return $AISettingsCopyWith<$Res>(_value.settings, (value) {
      return _then(_value.copyWith(settings: value));
    });
  }
}

/// @nodoc

class _$CreateAISettingsImpl implements CreateAISettings {
  const _$CreateAISettingsImpl(this.settings);

  @override
  final AISettings settings;

  @override
  String toString() {
    return 'AISettingsEvent.createAISettings(settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateAISettingsImpl &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateAISettingsImplCopyWith<_$CreateAISettingsImpl> get copyWith =>
      __$$CreateAISettingsImplCopyWithImpl<_$CreateAISettingsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return createAISettings(settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return createAISettings?.call(settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (createAISettings != null) {
      return createAISettings(settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return createAISettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return createAISettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (createAISettings != null) {
      return createAISettings(this);
    }
    return orElse();
  }
}

abstract class CreateAISettings implements AISettingsEvent {
  const factory CreateAISettings(final AISettings settings) =
      _$CreateAISettingsImpl;

  AISettings get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateAISettingsImplCopyWith<_$CreateAISettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateAISettingsImplCopyWith<$Res> {
  factory _$$UpdateAISettingsImplCopyWith(_$UpdateAISettingsImpl value,
          $Res Function(_$UpdateAISettingsImpl) then) =
      __$$UpdateAISettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AISettings settings});

  $AISettingsCopyWith<$Res> get settings;
}

/// @nodoc
class __$$UpdateAISettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res, _$UpdateAISettingsImpl>
    implements _$$UpdateAISettingsImplCopyWith<$Res> {
  __$$UpdateAISettingsImplCopyWithImpl(_$UpdateAISettingsImpl _value,
      $Res Function(_$UpdateAISettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
  }) {
    return _then(_$UpdateAISettingsImpl(
      null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as AISettings,
    ));
  }

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AISettingsCopyWith<$Res> get settings {
    return $AISettingsCopyWith<$Res>(_value.settings, (value) {
      return _then(_value.copyWith(settings: value));
    });
  }
}

/// @nodoc

class _$UpdateAISettingsImpl implements UpdateAISettings {
  const _$UpdateAISettingsImpl(this.settings);

  @override
  final AISettings settings;

  @override
  String toString() {
    return 'AISettingsEvent.updateAISettings(settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateAISettingsImpl &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateAISettingsImplCopyWith<_$UpdateAISettingsImpl> get copyWith =>
      __$$UpdateAISettingsImplCopyWithImpl<_$UpdateAISettingsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateAISettings(settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateAISettings?.call(settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateAISettings != null) {
      return updateAISettings(settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateAISettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateAISettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateAISettings != null) {
      return updateAISettings(this);
    }
    return orElse();
  }
}

abstract class UpdateAISettings implements AISettingsEvent {
  const factory UpdateAISettings(final AISettings settings) =
      _$UpdateAISettingsImpl;

  AISettings get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateAISettingsImplCopyWith<_$UpdateAISettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteAISettingsImplCopyWith<$Res> {
  factory _$$DeleteAISettingsImplCopyWith(_$DeleteAISettingsImpl value,
          $Res Function(_$DeleteAISettingsImpl) then) =
      __$$DeleteAISettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteAISettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res, _$DeleteAISettingsImpl>
    implements _$$DeleteAISettingsImplCopyWith<$Res> {
  __$$DeleteAISettingsImplCopyWithImpl(_$DeleteAISettingsImpl _value,
      $Res Function(_$DeleteAISettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteAISettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteAISettingsImpl implements DeleteAISettings {
  const _$DeleteAISettingsImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'AISettingsEvent.deleteAISettings(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteAISettingsImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteAISettingsImplCopyWith<_$DeleteAISettingsImpl> get copyWith =>
      __$$DeleteAISettingsImplCopyWithImpl<_$DeleteAISettingsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return deleteAISettings(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return deleteAISettings?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (deleteAISettings != null) {
      return deleteAISettings(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return deleteAISettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return deleteAISettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (deleteAISettings != null) {
      return deleteAISettings(this);
    }
    return orElse();
  }
}

abstract class DeleteAISettings implements AISettingsEvent {
  const factory DeleteAISettings(final String id) = _$DeleteAISettingsImpl;

  String get id;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteAISettingsImplCopyWith<_$DeleteAISettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVoiceCommandSettingsImplCopyWith<$Res> {
  factory _$$UpdateVoiceCommandSettingsImplCopyWith(
          _$UpdateVoiceCommandSettingsImpl value,
          $Res Function(_$UpdateVoiceCommandSettingsImpl) then) =
      __$$UpdateVoiceCommandSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateVoiceCommandSettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res,
        _$UpdateVoiceCommandSettingsImpl>
    implements _$$UpdateVoiceCommandSettingsImplCopyWith<$Res> {
  __$$UpdateVoiceCommandSettingsImplCopyWithImpl(
      _$UpdateVoiceCommandSettingsImpl _value,
      $Res Function(_$UpdateVoiceCommandSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateVoiceCommandSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateVoiceCommandSettingsImpl implements UpdateVoiceCommandSettings {
  const _$UpdateVoiceCommandSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'AISettingsEvent.updateVoiceCommandSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVoiceCommandSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVoiceCommandSettingsImplCopyWith<_$UpdateVoiceCommandSettingsImpl>
      get copyWith => __$$UpdateVoiceCommandSettingsImplCopyWithImpl<
          _$UpdateVoiceCommandSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceCommandSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceCommandSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandSettings != null) {
      return updateVoiceCommandSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceCommandSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceCommandSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandSettings != null) {
      return updateVoiceCommandSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateVoiceCommandSettings implements AISettingsEvent {
  const factory UpdateVoiceCommandSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateVoiceCommandSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateVoiceCommandSettingsImplCopyWith<_$UpdateVoiceCommandSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateChatbotSettingsImplCopyWith<$Res> {
  factory _$$UpdateChatbotSettingsImplCopyWith(
          _$UpdateChatbotSettingsImpl value,
          $Res Function(_$UpdateChatbotSettingsImpl) then) =
      __$$UpdateChatbotSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateChatbotSettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res, _$UpdateChatbotSettingsImpl>
    implements _$$UpdateChatbotSettingsImplCopyWith<$Res> {
  __$$UpdateChatbotSettingsImplCopyWithImpl(_$UpdateChatbotSettingsImpl _value,
      $Res Function(_$UpdateChatbotSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateChatbotSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateChatbotSettingsImpl implements UpdateChatbotSettings {
  const _$UpdateChatbotSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'AISettingsEvent.updateChatbotSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateChatbotSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateChatbotSettingsImplCopyWith<_$UpdateChatbotSettingsImpl>
      get copyWith => __$$UpdateChatbotSettingsImplCopyWithImpl<
          _$UpdateChatbotSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateChatbotSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateChatbotSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateChatbotSettings != null) {
      return updateChatbotSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateChatbotSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateChatbotSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateChatbotSettings != null) {
      return updateChatbotSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateChatbotSettings implements AISettingsEvent {
  const factory UpdateChatbotSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateChatbotSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateChatbotSettingsImplCopyWith<_$UpdateChatbotSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateLanguageModelSettingsImplCopyWith<$Res> {
  factory _$$UpdateLanguageModelSettingsImplCopyWith(
          _$UpdateLanguageModelSettingsImpl value,
          $Res Function(_$UpdateLanguageModelSettingsImpl) then) =
      __$$UpdateLanguageModelSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateLanguageModelSettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res,
        _$UpdateLanguageModelSettingsImpl>
    implements _$$UpdateLanguageModelSettingsImplCopyWith<$Res> {
  __$$UpdateLanguageModelSettingsImplCopyWithImpl(
      _$UpdateLanguageModelSettingsImpl _value,
      $Res Function(_$UpdateLanguageModelSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateLanguageModelSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateLanguageModelSettingsImpl implements UpdateLanguageModelSettings {
  const _$UpdateLanguageModelSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'AISettingsEvent.updateLanguageModelSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateLanguageModelSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateLanguageModelSettingsImplCopyWith<_$UpdateLanguageModelSettingsImpl>
      get copyWith => __$$UpdateLanguageModelSettingsImplCopyWithImpl<
          _$UpdateLanguageModelSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateLanguageModelSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateLanguageModelSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateLanguageModelSettings != null) {
      return updateLanguageModelSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateLanguageModelSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateLanguageModelSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateLanguageModelSettings != null) {
      return updateLanguageModelSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateLanguageModelSettings implements AISettingsEvent {
  const factory UpdateLanguageModelSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateLanguageModelSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateLanguageModelSettingsImplCopyWith<_$UpdateLanguageModelSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateResponseCustomizationImplCopyWith<$Res> {
  factory _$$UpdateResponseCustomizationImplCopyWith(
          _$UpdateResponseCustomizationImpl value,
          $Res Function(_$UpdateResponseCustomizationImpl) then) =
      __$$UpdateResponseCustomizationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateResponseCustomizationImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res,
        _$UpdateResponseCustomizationImpl>
    implements _$$UpdateResponseCustomizationImplCopyWith<$Res> {
  __$$UpdateResponseCustomizationImplCopyWithImpl(
      _$UpdateResponseCustomizationImpl _value,
      $Res Function(_$UpdateResponseCustomizationImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateResponseCustomizationImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateResponseCustomizationImpl implements UpdateResponseCustomization {
  const _$UpdateResponseCustomizationImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'AISettingsEvent.updateResponseCustomization(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateResponseCustomizationImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateResponseCustomizationImplCopyWith<_$UpdateResponseCustomizationImpl>
      get copyWith => __$$UpdateResponseCustomizationImplCopyWithImpl<
          _$UpdateResponseCustomizationImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateResponseCustomization(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateResponseCustomization?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateResponseCustomization != null) {
      return updateResponseCustomization(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateResponseCustomization(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateResponseCustomization?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateResponseCustomization != null) {
      return updateResponseCustomization(this);
    }
    return orElse();
  }
}

abstract class UpdateResponseCustomization implements AISettingsEvent {
  const factory UpdateResponseCustomization(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateResponseCustomizationImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateResponseCustomizationImplCopyWith<_$UpdateResponseCustomizationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVoiceRecognitionSettingsImplCopyWith<$Res> {
  factory _$$UpdateVoiceRecognitionSettingsImplCopyWith(
          _$UpdateVoiceRecognitionSettingsImpl value,
          $Res Function(_$UpdateVoiceRecognitionSettingsImpl) then) =
      __$$UpdateVoiceRecognitionSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateVoiceRecognitionSettingsImplCopyWithImpl<$Res>
    extends _$AISettingsEventCopyWithImpl<$Res,
        _$UpdateVoiceRecognitionSettingsImpl>
    implements _$$UpdateVoiceRecognitionSettingsImplCopyWith<$Res> {
  __$$UpdateVoiceRecognitionSettingsImplCopyWithImpl(
      _$UpdateVoiceRecognitionSettingsImpl _value,
      $Res Function(_$UpdateVoiceRecognitionSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateVoiceRecognitionSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateVoiceRecognitionSettingsImpl
    implements UpdateVoiceRecognitionSettings {
  const _$UpdateVoiceRecognitionSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'AISettingsEvent.updateVoiceRecognitionSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVoiceRecognitionSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVoiceRecognitionSettingsImplCopyWith<
          _$UpdateVoiceRecognitionSettingsImpl>
      get copyWith => __$$UpdateVoiceRecognitionSettingsImplCopyWithImpl<
          _$UpdateVoiceRecognitionSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadAISettings,
    required TResult Function(AISettings settings) createAISettings,
    required TResult Function(AISettings settings) updateAISettings,
    required TResult Function(String id) deleteAISettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceCommandSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateChatbotSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateLanguageModelSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateResponseCustomization,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceRecognitionSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadAISettings,
    TResult? Function(AISettings settings)? createAISettings,
    TResult? Function(AISettings settings)? updateAISettings,
    TResult? Function(String id)? deleteAISettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceRecognitionSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadAISettings,
    TResult Function(AISettings settings)? createAISettings,
    TResult Function(AISettings settings)? updateAISettings,
    TResult Function(String id)? deleteAISettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceCommandSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateChatbotSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateLanguageModelSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateResponseCustomization,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateVoiceRecognitionSettings != null) {
      return updateVoiceRecognitionSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAISettings value) loadAISettings,
    required TResult Function(CreateAISettings value) createAISettings,
    required TResult Function(UpdateAISettings value) updateAISettings,
    required TResult Function(DeleteAISettings value) deleteAISettings,
    required TResult Function(UpdateVoiceCommandSettings value)
        updateVoiceCommandSettings,
    required TResult Function(UpdateChatbotSettings value)
        updateChatbotSettings,
    required TResult Function(UpdateLanguageModelSettings value)
        updateLanguageModelSettings,
    required TResult Function(UpdateResponseCustomization value)
        updateResponseCustomization,
    required TResult Function(UpdateVoiceRecognitionSettings value)
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceRecognitionSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAISettings value)? loadAISettings,
    TResult? Function(CreateAISettings value)? createAISettings,
    TResult? Function(UpdateAISettings value)? updateAISettings,
    TResult? Function(DeleteAISettings value)? deleteAISettings,
    TResult? Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult? Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult? Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult? Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult? Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
  }) {
    return updateVoiceRecognitionSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAISettings value)? loadAISettings,
    TResult Function(CreateAISettings value)? createAISettings,
    TResult Function(UpdateAISettings value)? updateAISettings,
    TResult Function(DeleteAISettings value)? deleteAISettings,
    TResult Function(UpdateVoiceCommandSettings value)?
        updateVoiceCommandSettings,
    TResult Function(UpdateChatbotSettings value)? updateChatbotSettings,
    TResult Function(UpdateLanguageModelSettings value)?
        updateLanguageModelSettings,
    TResult Function(UpdateResponseCustomization value)?
        updateResponseCustomization,
    TResult Function(UpdateVoiceRecognitionSettings value)?
        updateVoiceRecognitionSettings,
    required TResult orElse(),
  }) {
    if (updateVoiceRecognitionSettings != null) {
      return updateVoiceRecognitionSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateVoiceRecognitionSettings implements AISettingsEvent {
  const factory UpdateVoiceRecognitionSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateVoiceRecognitionSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of AISettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateVoiceRecognitionSettingsImplCopyWith<
          _$UpdateVoiceRecognitionSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
