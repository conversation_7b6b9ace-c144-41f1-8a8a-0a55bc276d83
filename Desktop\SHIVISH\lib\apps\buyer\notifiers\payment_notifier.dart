import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/payment/card_model.dart';
import 'package:shivish/shared/models/payment/payment_model.dart';
import 'package:shivish/shared/services/payment/payment_service.dart';
import '../states/payment_state.dart';
import 'package:firebase_auth/firebase_auth.dart';

class PaymentNotifier extends StateNotifier<PaymentState> {
  final PaymentService _paymentService;
  StreamSubscription<List<PaymentModel>>? _paymentSubscription;
  StreamSubscription<List<CardModel>>? _cardSubscription;

  PaymentNotifier({
    required PaymentService paymentService,
  })  : _paymentService = paymentService,
        super(const PaymentState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      await _listenToUserPayments();
      await _listenToUserCards();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> createPayment({
    required String orderId,
    required String merchantId,
    required PaymentType type,
    required PaymentMethod method,
    required PaymentGateway gateway,
    required double amount,
    required double taxAmount,
    required double feeAmount,
    CardModel? card,
  }) async {
    state = state.copyWith(isProcessing: true, error: null);
    try {
      final payment = PaymentModel(
        id: '', // Will be set by Firestore
        paymentNumber: orderId,
        orderId: orderId,
        customerId: '', // Will be set by service
        merchantId: merchantId,
        type: type,
        status: PaymentStatus.pending,
        method: method,
        gateway: gateway,
        amount: amount,
        taxAmount: taxAmount,
        feeAmount: feeAmount,
        totalAmount: amount + taxAmount + feeAmount,
        card: card,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _paymentService.createPayment(payment);
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> updatePayment(String paymentId, PaymentStatus status) async {
    state = state.copyWith(isProcessing: true, error: null);
    try {
      final payment = await _paymentService.getPayment(paymentId);
      if (payment != null) {
        final updatedPayment = payment.copyWith(
          status: status,
          updatedAt: DateTime.now(),
        );
        await _paymentService.updatePayment(updatedPayment);
      }
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> getPaymentById(String paymentId) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final payment = await _paymentService.getPayment(paymentId);
      state = state.copyWith(
        isLoading: false,
        selectedPayment: payment,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> _listenToUserPayments() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    _paymentSubscription?.cancel();
    _paymentSubscription = _paymentService.getPaymentsByUser(user.uid).listen(
          (payments) => state = state.copyWith(payments: payments),
          onError: (error) => state = state.copyWith(error: error.toString()),
        );
  }

  Future<void> _listenToUserCards() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    _cardSubscription?.cancel();
    _cardSubscription = _paymentService.listenToUserCards().listen(
          (cards) => state = state.copyWith(cards: cards),
          onError: (error) => state = state.copyWith(error: error.toString()),
        );
  }

  Future<void> saveCard(CardModel card) async {
    state = state.copyWith(isProcessing: true, error: null);
    try {
      await _paymentService.saveCard(card);
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> deleteCard(String cardId) async {
    state = state.copyWith(isProcessing: true, error: null);
    try {
      await _paymentService.deleteCard(cardId);
      state = state.copyWith(isProcessing: false);
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  void setSelectedCard(CardModel? card) {
    state = state.copyWith(selectedCard: card);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const PaymentState();
  }

  @override
  void dispose() {
    _paymentSubscription?.cancel();
    _cardSubscription?.cancel();
    super.dispose();
  }
}
