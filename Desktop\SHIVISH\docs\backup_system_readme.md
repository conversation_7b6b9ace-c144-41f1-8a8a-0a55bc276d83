# Hybrid Backup System

A comprehensive backup and restore solution for Flutter applications, supporting both local storage and cloud backup.

## Features

- **Local Backup**: Store backups on the device using Hive.
- **Cloud Backup**: Store backups in Google Drive.
- **Automatic Backup**: Schedule backups with configurable frequency.
- **Progress Tracking**: Real-time progress tracking with UI updates.
- **Conflict Resolution**: Detect and resolve conflicts between local and cloud data.
- **Encryption**: Encrypt backup data for security.
- **Compression**: Compress backup data to save space.
- **Validation**: Validate backup integrity.
- **Retention Policies**: Configure how long to keep old backups.
- **Notifications**: Notify users of backup status.
- **Analytics**: Track backup statistics and analytics.
- **Export/Import**: Export and import backups.
- **Emergency Backup**: Create emergency backups before potentially destructive operations.
- **Recovery**: Recover from backup failures or corruption.

## Installation

Add the following dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.0.15
  flutter_secure_storage: ^8.0.0
  encrypt: ^5.0.1
  google_sign_in: ^6.1.4
  googleapis: ^11.2.0
  firebase_auth: ^4.6.3
  firebase_core: ^2.14.0
  shared_preferences: ^2.2.0
  provider: ^6.0.5
  flutter_local_notifications: ^14.1.1
  intl: ^0.18.1
  share_plus: ^7.0.2
  file_picker: ^5.3.2
```

## Quick Start

1. Initialize the backup system:

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  runApp(
    MultiProvider(
      providers: BackupAPI.getProviders(),
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize the backup system
    BackupAPI.initialize(context);
    
    return MaterialApp(
      title: 'My App',
      home: MyHomePage(),
    );
  }
}
```

2. Add the backup widget to your app:

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/widgets/backup/backup_widget.dart';

class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My App'),
      ),
      body: Center(
        child: BackupWidget(),
      ),
    );
  }
}
```

## Documentation

For detailed documentation, see [Backup System Documentation](backup_system.md).

## Examples

### Create a Backup

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';

Future<void> createBackup(BuildContext context) async {
  final success = await BackupAPI.createBackup(context);
  
  if (success) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Backup created successfully')),
    );
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to create backup')),
    );
  }
}
```

### Restore a Backup

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/models/backup_metadata.dart';

Future<void> restoreBackup(BuildContext context, BackupMetadata backup) async {
  final success = await BackupAPI.restoreBackup(context, backup);
  
  if (success) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Backup restored successfully')),
    );
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to restore backup')),
    );
  }
}
```

### Update Backup Configuration

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/models/backup_config.dart';

void updateBackupConfig(BuildContext context) {
  final config = BackupConfig(
    enableAutoBackup: true,
    enableCloudBackup: true,
    enableLocalBackup: true,
    enableEncryption: true,
    enableCompression: true,
    enableValidation: true,
    backupFrequency: 'daily',
    retentionDays: 30,
    localStoragePath: 'backups',
  );
  
  BackupAPI.updateBackupConfig(context, config);
}
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
