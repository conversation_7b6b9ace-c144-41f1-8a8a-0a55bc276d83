import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:io';

part 'admin_media_management_event.freezed.dart';

@freezed
class AdminMediaManagementEvent with _$AdminMediaManagementEvent {
  const factory AdminMediaManagementEvent.loadPendingMedia() =
      _LoadPendingMedia;
  const factory AdminMediaManagementEvent.approveMedia({
    required String mediaId,
    required String adminId,
  }) = _ApproveMedia;
  const factory AdminMediaManagementEvent.rejectMedia({
    required String mediaId,
    required String adminId,
    required String reason,
  }) = _RejectMedia;
  const factory AdminMediaManagementEvent.uploadMedia({
    required File file,
    required String title,
    required String description,
    required String adminId,
  }) = _UploadMedia;
  const factory AdminMediaManagementEvent.updateMedia({
    required String mediaId,
    required String title,
    required String description,
    File? file,
  }) = _UpdateMedia;
}
