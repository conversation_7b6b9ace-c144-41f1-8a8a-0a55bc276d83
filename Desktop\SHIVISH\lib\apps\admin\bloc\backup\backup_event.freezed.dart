// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'backup_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BackupEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BackupEventCopyWith<$Res> {
  factory $BackupEventCopyWith(
          BackupEvent value, $Res Function(BackupEvent) then) =
      _$BackupEventCopyWithImpl<$Res, BackupEvent>;
}

/// @nodoc
class _$BackupEventCopyWithImpl<$Res, $Val extends BackupEvent>
    implements $BackupEventCopyWith<$Res> {
  _$BackupEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadBackupsImplCopyWith<$Res> {
  factory _$$LoadBackupsImplCopyWith(
          _$LoadBackupsImpl value, $Res Function(_$LoadBackupsImpl) then) =
      __$$LoadBackupsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadBackupsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$LoadBackupsImpl>
    implements _$$LoadBackupsImplCopyWith<$Res> {
  __$$LoadBackupsImplCopyWithImpl(
      _$LoadBackupsImpl _value, $Res Function(_$LoadBackupsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadBackupsImpl implements LoadBackups {
  const _$LoadBackupsImpl();

  @override
  String toString() {
    return 'BackupEvent.loadBackups()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadBackupsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return loadBackups();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return loadBackups?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (loadBackups != null) {
      return loadBackups();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return loadBackups(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return loadBackups?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (loadBackups != null) {
      return loadBackups(this);
    }
    return orElse();
  }
}

abstract class LoadBackups implements BackupEvent {
  const factory LoadBackups() = _$LoadBackupsImpl;
}

/// @nodoc
abstract class _$$CreateBackupImplCopyWith<$Res> {
  factory _$$CreateBackupImplCopyWith(
          _$CreateBackupImpl value, $Res Function(_$CreateBackupImpl) then) =
      __$$CreateBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Backup backup});

  $BackupCopyWith<$Res> get backup;
}

/// @nodoc
class __$$CreateBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$CreateBackupImpl>
    implements _$$CreateBackupImplCopyWith<$Res> {
  __$$CreateBackupImplCopyWithImpl(
      _$CreateBackupImpl _value, $Res Function(_$CreateBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backup = null,
  }) {
    return _then(_$CreateBackupImpl(
      null == backup
          ? _value.backup
          : backup // ignore: cast_nullable_to_non_nullable
              as Backup,
    ));
  }

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackupCopyWith<$Res> get backup {
    return $BackupCopyWith<$Res>(_value.backup, (value) {
      return _then(_value.copyWith(backup: value));
    });
  }
}

/// @nodoc

class _$CreateBackupImpl implements CreateBackup {
  const _$CreateBackupImpl(this.backup);

  @override
  final Backup backup;

  @override
  String toString() {
    return 'BackupEvent.createBackup(backup: $backup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateBackupImpl &&
            (identical(other.backup, backup) || other.backup == backup));
  }

  @override
  int get hashCode => Object.hash(runtimeType, backup);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateBackupImplCopyWith<_$CreateBackupImpl> get copyWith =>
      __$$CreateBackupImplCopyWithImpl<_$CreateBackupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return createBackup(backup);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return createBackup?.call(backup);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (createBackup != null) {
      return createBackup(backup);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return createBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return createBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (createBackup != null) {
      return createBackup(this);
    }
    return orElse();
  }
}

abstract class CreateBackup implements BackupEvent {
  const factory CreateBackup(final Backup backup) = _$CreateBackupImpl;

  Backup get backup;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateBackupImplCopyWith<_$CreateBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateBackupImplCopyWith<$Res> {
  factory _$$UpdateBackupImplCopyWith(
          _$UpdateBackupImpl value, $Res Function(_$UpdateBackupImpl) then) =
      __$$UpdateBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Backup backup});

  $BackupCopyWith<$Res> get backup;
}

/// @nodoc
class __$$UpdateBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateBackupImpl>
    implements _$$UpdateBackupImplCopyWith<$Res> {
  __$$UpdateBackupImplCopyWithImpl(
      _$UpdateBackupImpl _value, $Res Function(_$UpdateBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backup = null,
  }) {
    return _then(_$UpdateBackupImpl(
      null == backup
          ? _value.backup
          : backup // ignore: cast_nullable_to_non_nullable
              as Backup,
    ));
  }

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackupCopyWith<$Res> get backup {
    return $BackupCopyWith<$Res>(_value.backup, (value) {
      return _then(_value.copyWith(backup: value));
    });
  }
}

/// @nodoc

class _$UpdateBackupImpl implements UpdateBackup {
  const _$UpdateBackupImpl(this.backup);

  @override
  final Backup backup;

  @override
  String toString() {
    return 'BackupEvent.updateBackup(backup: $backup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateBackupImpl &&
            (identical(other.backup, backup) || other.backup == backup));
  }

  @override
  int get hashCode => Object.hash(runtimeType, backup);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateBackupImplCopyWith<_$UpdateBackupImpl> get copyWith =>
      __$$UpdateBackupImplCopyWithImpl<_$UpdateBackupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateBackup(backup);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateBackup?.call(backup);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateBackup != null) {
      return updateBackup(backup);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateBackup != null) {
      return updateBackup(this);
    }
    return orElse();
  }
}

abstract class UpdateBackup implements BackupEvent {
  const factory UpdateBackup(final Backup backup) = _$UpdateBackupImpl;

  Backup get backup;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateBackupImplCopyWith<_$UpdateBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteBackupImplCopyWith<$Res> {
  factory _$$DeleteBackupImplCopyWith(
          _$DeleteBackupImpl value, $Res Function(_$DeleteBackupImpl) then) =
      __$$DeleteBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$DeleteBackupImpl>
    implements _$$DeleteBackupImplCopyWith<$Res> {
  __$$DeleteBackupImplCopyWithImpl(
      _$DeleteBackupImpl _value, $Res Function(_$DeleteBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteBackupImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteBackupImpl implements DeleteBackup {
  const _$DeleteBackupImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'BackupEvent.deleteBackup(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteBackupImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteBackupImplCopyWith<_$DeleteBackupImpl> get copyWith =>
      __$$DeleteBackupImplCopyWithImpl<_$DeleteBackupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return deleteBackup(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return deleteBackup?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (deleteBackup != null) {
      return deleteBackup(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return deleteBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return deleteBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (deleteBackup != null) {
      return deleteBackup(this);
    }
    return orElse();
  }
}

abstract class DeleteBackup implements BackupEvent {
  const factory DeleteBackup(final String id) = _$DeleteBackupImpl;

  String get id;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteBackupImplCopyWith<_$DeleteBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RestoreBackupImplCopyWith<$Res> {
  factory _$$RestoreBackupImplCopyWith(
          _$RestoreBackupImpl value, $Res Function(_$RestoreBackupImpl) then) =
      __$$RestoreBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$RestoreBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$RestoreBackupImpl>
    implements _$$RestoreBackupImplCopyWith<$Res> {
  __$$RestoreBackupImplCopyWithImpl(
      _$RestoreBackupImpl _value, $Res Function(_$RestoreBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$RestoreBackupImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RestoreBackupImpl implements RestoreBackup {
  const _$RestoreBackupImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'BackupEvent.restoreBackup(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RestoreBackupImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RestoreBackupImplCopyWith<_$RestoreBackupImpl> get copyWith =>
      __$$RestoreBackupImplCopyWithImpl<_$RestoreBackupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return restoreBackup(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return restoreBackup?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (restoreBackup != null) {
      return restoreBackup(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return restoreBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return restoreBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (restoreBackup != null) {
      return restoreBackup(this);
    }
    return orElse();
  }
}

abstract class RestoreBackup implements BackupEvent {
  const factory RestoreBackup(final String id) = _$RestoreBackupImpl;

  String get id;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RestoreBackupImplCopyWith<_$RestoreBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidateBackupImplCopyWith<$Res> {
  factory _$$ValidateBackupImplCopyWith(_$ValidateBackupImpl value,
          $Res Function(_$ValidateBackupImpl) then) =
      __$$ValidateBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$ValidateBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$ValidateBackupImpl>
    implements _$$ValidateBackupImplCopyWith<$Res> {
  __$$ValidateBackupImplCopyWithImpl(
      _$ValidateBackupImpl _value, $Res Function(_$ValidateBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$ValidateBackupImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ValidateBackupImpl implements ValidateBackup {
  const _$ValidateBackupImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'BackupEvent.validateBackup(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidateBackupImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidateBackupImplCopyWith<_$ValidateBackupImpl> get copyWith =>
      __$$ValidateBackupImplCopyWithImpl<_$ValidateBackupImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return validateBackup(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return validateBackup?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (validateBackup != null) {
      return validateBackup(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return validateBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return validateBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (validateBackup != null) {
      return validateBackup(this);
    }
    return orElse();
  }
}

abstract class ValidateBackup implements BackupEvent {
  const factory ValidateBackup(final String id) = _$ValidateBackupImpl;

  String get id;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidateBackupImplCopyWith<_$ValidateBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ScheduleBackupImplCopyWith<$Res> {
  factory _$$ScheduleBackupImplCopyWith(_$ScheduleBackupImpl value,
          $Res Function(_$ScheduleBackupImpl) then) =
      __$$ScheduleBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Backup backup});

  $BackupCopyWith<$Res> get backup;
}

/// @nodoc
class __$$ScheduleBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$ScheduleBackupImpl>
    implements _$$ScheduleBackupImplCopyWith<$Res> {
  __$$ScheduleBackupImplCopyWithImpl(
      _$ScheduleBackupImpl _value, $Res Function(_$ScheduleBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? backup = null,
  }) {
    return _then(_$ScheduleBackupImpl(
      null == backup
          ? _value.backup
          : backup // ignore: cast_nullable_to_non_nullable
              as Backup,
    ));
  }

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BackupCopyWith<$Res> get backup {
    return $BackupCopyWith<$Res>(_value.backup, (value) {
      return _then(_value.copyWith(backup: value));
    });
  }
}

/// @nodoc

class _$ScheduleBackupImpl implements ScheduleBackup {
  const _$ScheduleBackupImpl(this.backup);

  @override
  final Backup backup;

  @override
  String toString() {
    return 'BackupEvent.scheduleBackup(backup: $backup)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleBackupImpl &&
            (identical(other.backup, backup) || other.backup == backup));
  }

  @override
  int get hashCode => Object.hash(runtimeType, backup);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleBackupImplCopyWith<_$ScheduleBackupImpl> get copyWith =>
      __$$ScheduleBackupImplCopyWithImpl<_$ScheduleBackupImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return scheduleBackup(backup);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return scheduleBackup?.call(backup);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (scheduleBackup != null) {
      return scheduleBackup(backup);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return scheduleBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return scheduleBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (scheduleBackup != null) {
      return scheduleBackup(this);
    }
    return orElse();
  }
}

abstract class ScheduleBackup implements BackupEvent {
  const factory ScheduleBackup(final Backup backup) = _$ScheduleBackupImpl;

  Backup get backup;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleBackupImplCopyWith<_$ScheduleBackupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelScheduledBackupImplCopyWith<$Res> {
  factory _$$CancelScheduledBackupImplCopyWith(
          _$CancelScheduledBackupImpl value,
          $Res Function(_$CancelScheduledBackupImpl) then) =
      __$$CancelScheduledBackupImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$CancelScheduledBackupImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$CancelScheduledBackupImpl>
    implements _$$CancelScheduledBackupImplCopyWith<$Res> {
  __$$CancelScheduledBackupImplCopyWithImpl(_$CancelScheduledBackupImpl _value,
      $Res Function(_$CancelScheduledBackupImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$CancelScheduledBackupImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CancelScheduledBackupImpl implements CancelScheduledBackup {
  const _$CancelScheduledBackupImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'BackupEvent.cancelScheduledBackup(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancelScheduledBackupImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancelScheduledBackupImplCopyWith<_$CancelScheduledBackupImpl>
      get copyWith => __$$CancelScheduledBackupImplCopyWithImpl<
          _$CancelScheduledBackupImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return cancelScheduledBackup(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return cancelScheduledBackup?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (cancelScheduledBackup != null) {
      return cancelScheduledBackup(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return cancelScheduledBackup(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return cancelScheduledBackup?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (cancelScheduledBackup != null) {
      return cancelScheduledBackup(this);
    }
    return orElse();
  }
}

abstract class CancelScheduledBackup implements BackupEvent {
  const factory CancelScheduledBackup(final String id) =
      _$CancelScheduledBackupImpl;

  String get id;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancelScheduledBackupImplCopyWith<_$CancelScheduledBackupImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateRetentionSettingsImplCopyWith<$Res> {
  factory _$$UpdateRetentionSettingsImplCopyWith(
          _$UpdateRetentionSettingsImpl value,
          $Res Function(_$UpdateRetentionSettingsImpl) then) =
      __$$UpdateRetentionSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, int days});
}

/// @nodoc
class __$$UpdateRetentionSettingsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateRetentionSettingsImpl>
    implements _$$UpdateRetentionSettingsImplCopyWith<$Res> {
  __$$UpdateRetentionSettingsImplCopyWithImpl(
      _$UpdateRetentionSettingsImpl _value,
      $Res Function(_$UpdateRetentionSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? days = null,
  }) {
    return _then(_$UpdateRetentionSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == days
          ? _value.days
          : days // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$UpdateRetentionSettingsImpl implements UpdateRetentionSettings {
  const _$UpdateRetentionSettingsImpl(this.id, this.days);

  @override
  final String id;
  @override
  final int days;

  @override
  String toString() {
    return 'BackupEvent.updateRetentionSettings(id: $id, days: $days)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateRetentionSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.days, days) || other.days == days));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, days);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateRetentionSettingsImplCopyWith<_$UpdateRetentionSettingsImpl>
      get copyWith => __$$UpdateRetentionSettingsImplCopyWithImpl<
          _$UpdateRetentionSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateRetentionSettings(id, days);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateRetentionSettings?.call(id, days);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateRetentionSettings != null) {
      return updateRetentionSettings(id, days);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateRetentionSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateRetentionSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateRetentionSettings != null) {
      return updateRetentionSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateRetentionSettings implements BackupEvent {
  const factory UpdateRetentionSettings(final String id, final int days) =
      _$UpdateRetentionSettingsImpl;

  String get id;
  int get days;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateRetentionSettingsImplCopyWith<_$UpdateRetentionSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCompressionSettingsImplCopyWith<$Res> {
  factory _$$UpdateCompressionSettingsImplCopyWith(
          _$UpdateCompressionSettingsImpl value,
          $Res Function(_$UpdateCompressionSettingsImpl) then) =
      __$$UpdateCompressionSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateCompressionSettingsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateCompressionSettingsImpl>
    implements _$$UpdateCompressionSettingsImplCopyWith<$Res> {
  __$$UpdateCompressionSettingsImplCopyWithImpl(
      _$UpdateCompressionSettingsImpl _value,
      $Res Function(_$UpdateCompressionSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateCompressionSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateCompressionSettingsImpl implements UpdateCompressionSettings {
  const _$UpdateCompressionSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'BackupEvent.updateCompressionSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCompressionSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCompressionSettingsImplCopyWith<_$UpdateCompressionSettingsImpl>
      get copyWith => __$$UpdateCompressionSettingsImplCopyWithImpl<
          _$UpdateCompressionSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateCompressionSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateCompressionSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateCompressionSettings != null) {
      return updateCompressionSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateCompressionSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateCompressionSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateCompressionSettings != null) {
      return updateCompressionSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateCompressionSettings implements BackupEvent {
  const factory UpdateCompressionSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateCompressionSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCompressionSettingsImplCopyWith<_$UpdateCompressionSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateValidationSettingsImplCopyWith<$Res> {
  factory _$$UpdateValidationSettingsImplCopyWith(
          _$UpdateValidationSettingsImpl value,
          $Res Function(_$UpdateValidationSettingsImpl) then) =
      __$$UpdateValidationSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateValidationSettingsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateValidationSettingsImpl>
    implements _$$UpdateValidationSettingsImplCopyWith<$Res> {
  __$$UpdateValidationSettingsImplCopyWithImpl(
      _$UpdateValidationSettingsImpl _value,
      $Res Function(_$UpdateValidationSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateValidationSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateValidationSettingsImpl implements UpdateValidationSettings {
  const _$UpdateValidationSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'BackupEvent.updateValidationSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateValidationSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateValidationSettingsImplCopyWith<_$UpdateValidationSettingsImpl>
      get copyWith => __$$UpdateValidationSettingsImplCopyWithImpl<
          _$UpdateValidationSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateValidationSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateValidationSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateValidationSettings != null) {
      return updateValidationSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateValidationSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateValidationSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateValidationSettings != null) {
      return updateValidationSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateValidationSettings implements BackupEvent {
  const factory UpdateValidationSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateValidationSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateValidationSettingsImplCopyWith<_$UpdateValidationSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateNotificationSettingsImplCopyWith<$Res> {
  factory _$$UpdateNotificationSettingsImplCopyWith(
          _$UpdateNotificationSettingsImpl value,
          $Res Function(_$UpdateNotificationSettingsImpl) then) =
      __$$UpdateNotificationSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateNotificationSettingsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateNotificationSettingsImpl>
    implements _$$UpdateNotificationSettingsImplCopyWith<$Res> {
  __$$UpdateNotificationSettingsImplCopyWithImpl(
      _$UpdateNotificationSettingsImpl _value,
      $Res Function(_$UpdateNotificationSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateNotificationSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateNotificationSettingsImpl implements UpdateNotificationSettings {
  const _$UpdateNotificationSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'BackupEvent.updateNotificationSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateNotificationSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateNotificationSettingsImplCopyWith<_$UpdateNotificationSettingsImpl>
      get copyWith => __$$UpdateNotificationSettingsImplCopyWithImpl<
          _$UpdateNotificationSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateNotificationSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateNotificationSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateNotificationSettings != null) {
      return updateNotificationSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateNotificationSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateNotificationSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateNotificationSettings != null) {
      return updateNotificationSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateNotificationSettings implements BackupEvent {
  const factory UpdateNotificationSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateNotificationSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateNotificationSettingsImplCopyWith<_$UpdateNotificationSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateMonitoringSettingsImplCopyWith<$Res> {
  factory _$$UpdateMonitoringSettingsImplCopyWith(
          _$UpdateMonitoringSettingsImpl value,
          $Res Function(_$UpdateMonitoringSettingsImpl) then) =
      __$$UpdateMonitoringSettingsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> settings});
}

/// @nodoc
class __$$UpdateMonitoringSettingsImplCopyWithImpl<$Res>
    extends _$BackupEventCopyWithImpl<$Res, _$UpdateMonitoringSettingsImpl>
    implements _$$UpdateMonitoringSettingsImplCopyWith<$Res> {
  __$$UpdateMonitoringSettingsImplCopyWithImpl(
      _$UpdateMonitoringSettingsImpl _value,
      $Res Function(_$UpdateMonitoringSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? settings = null,
  }) {
    return _then(_$UpdateMonitoringSettingsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == settings
          ? _value._settings
          : settings // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateMonitoringSettingsImpl implements UpdateMonitoringSettings {
  const _$UpdateMonitoringSettingsImpl(
      this.id, final Map<String, dynamic> settings)
      : _settings = settings;

  @override
  final String id;
  final Map<String, dynamic> _settings;
  @override
  Map<String, dynamic> get settings {
    if (_settings is EqualUnmodifiableMapView) return _settings;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_settings);
  }

  @override
  String toString() {
    return 'BackupEvent.updateMonitoringSettings(id: $id, settings: $settings)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateMonitoringSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._settings, _settings));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_settings));

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateMonitoringSettingsImplCopyWith<_$UpdateMonitoringSettingsImpl>
      get copyWith => __$$UpdateMonitoringSettingsImplCopyWithImpl<
          _$UpdateMonitoringSettingsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadBackups,
    required TResult Function(Backup backup) createBackup,
    required TResult Function(Backup backup) updateBackup,
    required TResult Function(String id) deleteBackup,
    required TResult Function(String id) restoreBackup,
    required TResult Function(String id) validateBackup,
    required TResult Function(Backup backup) scheduleBackup,
    required TResult Function(String id) cancelScheduledBackup,
    required TResult Function(String id, int days) updateRetentionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateCompressionSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateValidationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateNotificationSettings,
    required TResult Function(String id, Map<String, dynamic> settings)
        updateMonitoringSettings,
  }) {
    return updateMonitoringSettings(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadBackups,
    TResult? Function(Backup backup)? createBackup,
    TResult? Function(Backup backup)? updateBackup,
    TResult? Function(String id)? deleteBackup,
    TResult? Function(String id)? restoreBackup,
    TResult? Function(String id)? validateBackup,
    TResult? Function(Backup backup)? scheduleBackup,
    TResult? Function(String id)? cancelScheduledBackup,
    TResult? Function(String id, int days)? updateRetentionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult? Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
  }) {
    return updateMonitoringSettings?.call(id, settings);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadBackups,
    TResult Function(Backup backup)? createBackup,
    TResult Function(Backup backup)? updateBackup,
    TResult Function(String id)? deleteBackup,
    TResult Function(String id)? restoreBackup,
    TResult Function(String id)? validateBackup,
    TResult Function(Backup backup)? scheduleBackup,
    TResult Function(String id)? cancelScheduledBackup,
    TResult Function(String id, int days)? updateRetentionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateCompressionSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateValidationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateNotificationSettings,
    TResult Function(String id, Map<String, dynamic> settings)?
        updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateMonitoringSettings != null) {
      return updateMonitoringSettings(id, settings);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadBackups value) loadBackups,
    required TResult Function(CreateBackup value) createBackup,
    required TResult Function(UpdateBackup value) updateBackup,
    required TResult Function(DeleteBackup value) deleteBackup,
    required TResult Function(RestoreBackup value) restoreBackup,
    required TResult Function(ValidateBackup value) validateBackup,
    required TResult Function(ScheduleBackup value) scheduleBackup,
    required TResult Function(CancelScheduledBackup value)
        cancelScheduledBackup,
    required TResult Function(UpdateRetentionSettings value)
        updateRetentionSettings,
    required TResult Function(UpdateCompressionSettings value)
        updateCompressionSettings,
    required TResult Function(UpdateValidationSettings value)
        updateValidationSettings,
    required TResult Function(UpdateNotificationSettings value)
        updateNotificationSettings,
    required TResult Function(UpdateMonitoringSettings value)
        updateMonitoringSettings,
  }) {
    return updateMonitoringSettings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadBackups value)? loadBackups,
    TResult? Function(CreateBackup value)? createBackup,
    TResult? Function(UpdateBackup value)? updateBackup,
    TResult? Function(DeleteBackup value)? deleteBackup,
    TResult? Function(RestoreBackup value)? restoreBackup,
    TResult? Function(ValidateBackup value)? validateBackup,
    TResult? Function(ScheduleBackup value)? scheduleBackup,
    TResult? Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult? Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult? Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult? Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult? Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult? Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
  }) {
    return updateMonitoringSettings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadBackups value)? loadBackups,
    TResult Function(CreateBackup value)? createBackup,
    TResult Function(UpdateBackup value)? updateBackup,
    TResult Function(DeleteBackup value)? deleteBackup,
    TResult Function(RestoreBackup value)? restoreBackup,
    TResult Function(ValidateBackup value)? validateBackup,
    TResult Function(ScheduleBackup value)? scheduleBackup,
    TResult Function(CancelScheduledBackup value)? cancelScheduledBackup,
    TResult Function(UpdateRetentionSettings value)? updateRetentionSettings,
    TResult Function(UpdateCompressionSettings value)?
        updateCompressionSettings,
    TResult Function(UpdateValidationSettings value)? updateValidationSettings,
    TResult Function(UpdateNotificationSettings value)?
        updateNotificationSettings,
    TResult Function(UpdateMonitoringSettings value)? updateMonitoringSettings,
    required TResult orElse(),
  }) {
    if (updateMonitoringSettings != null) {
      return updateMonitoringSettings(this);
    }
    return orElse();
  }
}

abstract class UpdateMonitoringSettings implements BackupEvent {
  const factory UpdateMonitoringSettings(
          final String id, final Map<String, dynamic> settings) =
      _$UpdateMonitoringSettingsImpl;

  String get id;
  Map<String, dynamic> get settings;

  /// Create a copy of BackupEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateMonitoringSettingsImplCopyWith<_$UpdateMonitoringSettingsImpl>
      get copyWith => throw _privateConstructorUsedError;
}
