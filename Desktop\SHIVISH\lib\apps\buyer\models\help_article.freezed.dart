// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'help_article.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HelpArticle _$HelpArticleFromJson(Map<String, dynamic> json) {
  return _HelpArticle.fromJson(json);
}

/// @nodoc
mixin _$HelpArticle {
  String get id => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String get question => throw _privateConstructorUsedError;
  String get answer => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;
  String get date => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get updates => throw _privateConstructorUsedError;

  /// Serializes this HelpArticle to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HelpArticle
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HelpArticleCopyWith<HelpArticle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HelpArticleCopyWith<$Res> {
  factory $HelpArticleCopyWith(
          HelpArticle value, $Res Function(HelpArticle) then) =
      _$HelpArticleCopyWithImpl<$Res, HelpArticle>;
  @useResult
  $Res call(
      {String id,
      String title,
      String question,
      String answer,
      String status,
      String date,
      String? description,
      List<Map<String, dynamic>>? updates});
}

/// @nodoc
class _$HelpArticleCopyWithImpl<$Res, $Val extends HelpArticle>
    implements $HelpArticleCopyWith<$Res> {
  _$HelpArticleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HelpArticle
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? question = null,
    Object? answer = null,
    Object? status = null,
    Object? date = null,
    Object? description = freezed,
    Object? updates = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      updates: freezed == updates
          ? _value.updates
          : updates // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HelpArticleImplCopyWith<$Res>
    implements $HelpArticleCopyWith<$Res> {
  factory _$$HelpArticleImplCopyWith(
          _$HelpArticleImpl value, $Res Function(_$HelpArticleImpl) then) =
      __$$HelpArticleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String title,
      String question,
      String answer,
      String status,
      String date,
      String? description,
      List<Map<String, dynamic>>? updates});
}

/// @nodoc
class __$$HelpArticleImplCopyWithImpl<$Res>
    extends _$HelpArticleCopyWithImpl<$Res, _$HelpArticleImpl>
    implements _$$HelpArticleImplCopyWith<$Res> {
  __$$HelpArticleImplCopyWithImpl(
      _$HelpArticleImpl _value, $Res Function(_$HelpArticleImpl) _then)
      : super(_value, _then);

  /// Create a copy of HelpArticle
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? question = null,
    Object? answer = null,
    Object? status = null,
    Object? date = null,
    Object? description = freezed,
    Object? updates = freezed,
  }) {
    return _then(_$HelpArticleImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      question: null == question
          ? _value.question
          : question // ignore: cast_nullable_to_non_nullable
              as String,
      answer: null == answer
          ? _value.answer
          : answer // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      updates: freezed == updates
          ? _value._updates
          : updates // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HelpArticleImpl implements _HelpArticle {
  const _$HelpArticleImpl(
      {required this.id,
      required this.title,
      required this.question,
      required this.answer,
      required this.status,
      required this.date,
      this.description,
      final List<Map<String, dynamic>>? updates})
      : _updates = updates;

  factory _$HelpArticleImpl.fromJson(Map<String, dynamic> json) =>
      _$$HelpArticleImplFromJson(json);

  @override
  final String id;
  @override
  final String title;
  @override
  final String question;
  @override
  final String answer;
  @override
  final String status;
  @override
  final String date;
  @override
  final String? description;
  final List<Map<String, dynamic>>? _updates;
  @override
  List<Map<String, dynamic>>? get updates {
    final value = _updates;
    if (value == null) return null;
    if (_updates is EqualUnmodifiableListView) return _updates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'HelpArticle(id: $id, title: $title, question: $question, answer: $answer, status: $status, date: $date, description: $description, updates: $updates)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HelpArticleImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.question, question) ||
                other.question == question) &&
            (identical(other.answer, answer) || other.answer == answer) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._updates, _updates));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, title, question, answer,
      status, date, description, const DeepCollectionEquality().hash(_updates));

  /// Create a copy of HelpArticle
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HelpArticleImplCopyWith<_$HelpArticleImpl> get copyWith =>
      __$$HelpArticleImplCopyWithImpl<_$HelpArticleImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HelpArticleImplToJson(
      this,
    );
  }
}

abstract class _HelpArticle implements HelpArticle {
  const factory _HelpArticle(
      {required final String id,
      required final String title,
      required final String question,
      required final String answer,
      required final String status,
      required final String date,
      final String? description,
      final List<Map<String, dynamic>>? updates}) = _$HelpArticleImpl;

  factory _HelpArticle.fromJson(Map<String, dynamic> json) =
      _$HelpArticleImpl.fromJson;

  @override
  String get id;
  @override
  String get title;
  @override
  String get question;
  @override
  String get answer;
  @override
  String get status;
  @override
  String get date;
  @override
  String? get description;
  @override
  List<Map<String, dynamic>>? get updates;

  /// Create a copy of HelpArticle
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HelpArticleImplCopyWith<_$HelpArticleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
