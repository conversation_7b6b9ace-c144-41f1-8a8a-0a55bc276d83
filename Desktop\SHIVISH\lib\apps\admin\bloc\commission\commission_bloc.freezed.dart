// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'commission_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CommissionEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissionEventCopyWith<$Res> {
  factory $CommissionEventCopyWith(
          CommissionEvent value, $Res Function(CommissionEvent) then) =
      _$CommissionEventCopyWithImpl<$Res, CommissionEvent>;
}

/// @nodoc
class _$CommissionEventCopyWithImpl<$Res, $Val extends CommissionEvent>
    implements $CommissionEventCopyWith<$Res> {
  _$CommissionEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadCommissionsImplCopyWith<$Res> {
  factory _$$LoadCommissionsImplCopyWith(_$LoadCommissionsImpl value,
          $Res Function(_$LoadCommissionsImpl) then) =
      __$$LoadCommissionsImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {int? limit,
      DocumentSnapshot<Object?>? startAfter,
      Map<String, dynamic>? filters});
}

/// @nodoc
class __$$LoadCommissionsImplCopyWithImpl<$Res>
    extends _$CommissionEventCopyWithImpl<$Res, _$LoadCommissionsImpl>
    implements _$$LoadCommissionsImplCopyWith<$Res> {
  __$$LoadCommissionsImplCopyWithImpl(
      _$LoadCommissionsImpl _value, $Res Function(_$LoadCommissionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? limit = freezed,
    Object? startAfter = freezed,
    Object? filters = freezed,
  }) {
    return _then(_$LoadCommissionsImpl(
      limit: freezed == limit
          ? _value.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      startAfter: freezed == startAfter
          ? _value.startAfter
          : startAfter // ignore: cast_nullable_to_non_nullable
              as DocumentSnapshot<Object?>?,
      filters: freezed == filters
          ? _value._filters
          : filters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc

class _$LoadCommissionsImpl implements LoadCommissions {
  const _$LoadCommissionsImpl(
      {this.limit, this.startAfter, final Map<String, dynamic>? filters})
      : _filters = filters;

  @override
  final int? limit;
  @override
  final DocumentSnapshot<Object?>? startAfter;
  final Map<String, dynamic>? _filters;
  @override
  Map<String, dynamic>? get filters {
    final value = _filters;
    if (value == null) return null;
    if (_filters is EqualUnmodifiableMapView) return _filters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'CommissionEvent.loadCommissions(limit: $limit, startAfter: $startAfter, filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadCommissionsImpl &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.startAfter, startAfter) ||
                other.startAfter == startAfter) &&
            const DeepCollectionEquality().equals(other._filters, _filters));
  }

  @override
  int get hashCode => Object.hash(runtimeType, limit, startAfter,
      const DeepCollectionEquality().hash(_filters));

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadCommissionsImplCopyWith<_$LoadCommissionsImpl> get copyWith =>
      __$$LoadCommissionsImplCopyWithImpl<_$LoadCommissionsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) {
    return loadCommissions(limit, startAfter, filters);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) {
    return loadCommissions?.call(limit, startAfter, filters);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) {
    if (loadCommissions != null) {
      return loadCommissions(limit, startAfter, filters);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) {
    return loadCommissions(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) {
    return loadCommissions?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) {
    if (loadCommissions != null) {
      return loadCommissions(this);
    }
    return orElse();
  }
}

abstract class LoadCommissions implements CommissionEvent {
  const factory LoadCommissions(
      {final int? limit,
      final DocumentSnapshot<Object?>? startAfter,
      final Map<String, dynamic>? filters}) = _$LoadCommissionsImpl;

  int? get limit;
  DocumentSnapshot<Object?>? get startAfter;
  Map<String, dynamic>? get filters;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadCommissionsImplCopyWith<_$LoadCommissionsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateCommissionImplCopyWith<$Res> {
  factory _$$CreateCommissionImplCopyWith(_$CreateCommissionImpl value,
          $Res Function(_$CreateCommissionImpl) then) =
      __$$CreateCommissionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Commission commission});

  $CommissionCopyWith<$Res> get commission;
}

/// @nodoc
class __$$CreateCommissionImplCopyWithImpl<$Res>
    extends _$CommissionEventCopyWithImpl<$Res, _$CreateCommissionImpl>
    implements _$$CreateCommissionImplCopyWith<$Res> {
  __$$CreateCommissionImplCopyWithImpl(_$CreateCommissionImpl _value,
      $Res Function(_$CreateCommissionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commission = null,
  }) {
    return _then(_$CreateCommissionImpl(
      null == commission
          ? _value.commission
          : commission // ignore: cast_nullable_to_non_nullable
              as Commission,
    ));
  }

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CommissionCopyWith<$Res> get commission {
    return $CommissionCopyWith<$Res>(_value.commission, (value) {
      return _then(_value.copyWith(commission: value));
    });
  }
}

/// @nodoc

class _$CreateCommissionImpl implements CreateCommission {
  const _$CreateCommissionImpl(this.commission);

  @override
  final Commission commission;

  @override
  String toString() {
    return 'CommissionEvent.createCommission(commission: $commission)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateCommissionImpl &&
            (identical(other.commission, commission) ||
                other.commission == commission));
  }

  @override
  int get hashCode => Object.hash(runtimeType, commission);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateCommissionImplCopyWith<_$CreateCommissionImpl> get copyWith =>
      __$$CreateCommissionImplCopyWithImpl<_$CreateCommissionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) {
    return createCommission(commission);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) {
    return createCommission?.call(commission);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) {
    if (createCommission != null) {
      return createCommission(commission);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) {
    return createCommission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) {
    return createCommission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) {
    if (createCommission != null) {
      return createCommission(this);
    }
    return orElse();
  }
}

abstract class CreateCommission implements CommissionEvent {
  const factory CreateCommission(final Commission commission) =
      _$CreateCommissionImpl;

  Commission get commission;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateCommissionImplCopyWith<_$CreateCommissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateCommissionImplCopyWith<$Res> {
  factory _$$UpdateCommissionImplCopyWith(_$UpdateCommissionImpl value,
          $Res Function(_$UpdateCommissionImpl) then) =
      __$$UpdateCommissionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Commission commission});

  $CommissionCopyWith<$Res> get commission;
}

/// @nodoc
class __$$UpdateCommissionImplCopyWithImpl<$Res>
    extends _$CommissionEventCopyWithImpl<$Res, _$UpdateCommissionImpl>
    implements _$$UpdateCommissionImplCopyWith<$Res> {
  __$$UpdateCommissionImplCopyWithImpl(_$UpdateCommissionImpl _value,
      $Res Function(_$UpdateCommissionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commission = null,
  }) {
    return _then(_$UpdateCommissionImpl(
      null == commission
          ? _value.commission
          : commission // ignore: cast_nullable_to_non_nullable
              as Commission,
    ));
  }

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CommissionCopyWith<$Res> get commission {
    return $CommissionCopyWith<$Res>(_value.commission, (value) {
      return _then(_value.copyWith(commission: value));
    });
  }
}

/// @nodoc

class _$UpdateCommissionImpl implements UpdateCommission {
  const _$UpdateCommissionImpl(this.commission);

  @override
  final Commission commission;

  @override
  String toString() {
    return 'CommissionEvent.updateCommission(commission: $commission)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateCommissionImpl &&
            (identical(other.commission, commission) ||
                other.commission == commission));
  }

  @override
  int get hashCode => Object.hash(runtimeType, commission);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateCommissionImplCopyWith<_$UpdateCommissionImpl> get copyWith =>
      __$$UpdateCommissionImplCopyWithImpl<_$UpdateCommissionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) {
    return updateCommission(commission);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) {
    return updateCommission?.call(commission);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) {
    if (updateCommission != null) {
      return updateCommission(commission);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) {
    return updateCommission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) {
    return updateCommission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) {
    if (updateCommission != null) {
      return updateCommission(this);
    }
    return orElse();
  }
}

abstract class UpdateCommission implements CommissionEvent {
  const factory UpdateCommission(final Commission commission) =
      _$UpdateCommissionImpl;

  Commission get commission;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateCommissionImplCopyWith<_$UpdateCommissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteCommissionImplCopyWith<$Res> {
  factory _$$DeleteCommissionImplCopyWith(_$DeleteCommissionImpl value,
          $Res Function(_$DeleteCommissionImpl) then) =
      __$$DeleteCommissionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteCommissionImplCopyWithImpl<$Res>
    extends _$CommissionEventCopyWithImpl<$Res, _$DeleteCommissionImpl>
    implements _$$DeleteCommissionImplCopyWith<$Res> {
  __$$DeleteCommissionImplCopyWithImpl(_$DeleteCommissionImpl _value,
      $Res Function(_$DeleteCommissionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteCommissionImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteCommissionImpl implements DeleteCommission {
  const _$DeleteCommissionImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'CommissionEvent.deleteCommission(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteCommissionImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteCommissionImplCopyWith<_$DeleteCommissionImpl> get copyWith =>
      __$$DeleteCommissionImplCopyWithImpl<_$DeleteCommissionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) {
    return deleteCommission(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) {
    return deleteCommission?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) {
    if (deleteCommission != null) {
      return deleteCommission(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) {
    return deleteCommission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) {
    return deleteCommission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) {
    if (deleteCommission != null) {
      return deleteCommission(this);
    }
    return orElse();
  }
}

abstract class DeleteCommission implements CommissionEvent {
  const factory DeleteCommission(final String id) = _$DeleteCommissionImpl;

  String get id;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteCommissionImplCopyWith<_$DeleteCommissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CalculateCommissionImplCopyWith<$Res> {
  factory _$$CalculateCommissionImplCopyWith(_$CalculateCommissionImpl value,
          $Res Function(_$CalculateCommissionImpl) then) =
      __$$CalculateCommissionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String categoryId, double amount});
}

/// @nodoc
class __$$CalculateCommissionImplCopyWithImpl<$Res>
    extends _$CommissionEventCopyWithImpl<$Res, _$CalculateCommissionImpl>
    implements _$$CalculateCommissionImplCopyWith<$Res> {
  __$$CalculateCommissionImplCopyWithImpl(_$CalculateCommissionImpl _value,
      $Res Function(_$CalculateCommissionImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? categoryId = null,
    Object? amount = null,
  }) {
    return _then(_$CalculateCommissionImpl(
      null == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String,
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$CalculateCommissionImpl implements CalculateCommission {
  const _$CalculateCommissionImpl(this.categoryId, this.amount);

  @override
  final String categoryId;
  @override
  final double amount;

  @override
  String toString() {
    return 'CommissionEvent.calculateCommission(categoryId: $categoryId, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateCommissionImpl &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, categoryId, amount);

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalculateCommissionImplCopyWith<_$CalculateCommissionImpl> get copyWith =>
      __$$CalculateCommissionImplCopyWithImpl<_$CalculateCommissionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)
        loadCommissions,
    required TResult Function(Commission commission) createCommission,
    required TResult Function(Commission commission) updateCommission,
    required TResult Function(String id) deleteCommission,
    required TResult Function(String categoryId, double amount)
        calculateCommission,
  }) {
    return calculateCommission(categoryId, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult? Function(Commission commission)? createCommission,
    TResult? Function(Commission commission)? updateCommission,
    TResult? Function(String id)? deleteCommission,
    TResult? Function(String categoryId, double amount)? calculateCommission,
  }) {
    return calculateCommission?.call(categoryId, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int? limit, DocumentSnapshot<Object?>? startAfter,
            Map<String, dynamic>? filters)?
        loadCommissions,
    TResult Function(Commission commission)? createCommission,
    TResult Function(Commission commission)? updateCommission,
    TResult Function(String id)? deleteCommission,
    TResult Function(String categoryId, double amount)? calculateCommission,
    required TResult orElse(),
  }) {
    if (calculateCommission != null) {
      return calculateCommission(categoryId, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadCommissions value) loadCommissions,
    required TResult Function(CreateCommission value) createCommission,
    required TResult Function(UpdateCommission value) updateCommission,
    required TResult Function(DeleteCommission value) deleteCommission,
    required TResult Function(CalculateCommission value) calculateCommission,
  }) {
    return calculateCommission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadCommissions value)? loadCommissions,
    TResult? Function(CreateCommission value)? createCommission,
    TResult? Function(UpdateCommission value)? updateCommission,
    TResult? Function(DeleteCommission value)? deleteCommission,
    TResult? Function(CalculateCommission value)? calculateCommission,
  }) {
    return calculateCommission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadCommissions value)? loadCommissions,
    TResult Function(CreateCommission value)? createCommission,
    TResult Function(UpdateCommission value)? updateCommission,
    TResult Function(DeleteCommission value)? deleteCommission,
    TResult Function(CalculateCommission value)? calculateCommission,
    required TResult orElse(),
  }) {
    if (calculateCommission != null) {
      return calculateCommission(this);
    }
    return orElse();
  }
}

abstract class CalculateCommission implements CommissionEvent {
  const factory CalculateCommission(
      final String categoryId, final double amount) = _$CalculateCommissionImpl;

  String get categoryId;
  double get amount;

  /// Create a copy of CommissionEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalculateCommissionImplCopyWith<_$CalculateCommissionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$CommissionState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommissionStateCopyWith<$Res> {
  factory $CommissionStateCopyWith(
          CommissionState value, $Res Function(CommissionState) then) =
      _$CommissionStateCopyWithImpl<$Res, CommissionState>;
}

/// @nodoc
class _$CommissionStateCopyWithImpl<$Res, $Val extends CommissionState>
    implements $CommissionStateCopyWith<$Res> {
  _$CommissionStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$CommissionInitialImplCopyWith<$Res> {
  factory _$$CommissionInitialImplCopyWith(_$CommissionInitialImpl value,
          $Res Function(_$CommissionInitialImpl) then) =
      __$$CommissionInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CommissionInitialImplCopyWithImpl<$Res>
    extends _$CommissionStateCopyWithImpl<$Res, _$CommissionInitialImpl>
    implements _$$CommissionInitialImplCopyWith<$Res> {
  __$$CommissionInitialImplCopyWithImpl(_$CommissionInitialImpl _value,
      $Res Function(_$CommissionInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CommissionInitialImpl implements CommissionInitial {
  const _$CommissionInitialImpl();

  @override
  String toString() {
    return 'CommissionState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CommissionInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class CommissionInitial implements CommissionState {
  const factory CommissionInitial() = _$CommissionInitialImpl;
}

/// @nodoc
abstract class _$$CommissionLoadingImplCopyWith<$Res> {
  factory _$$CommissionLoadingImplCopyWith(_$CommissionLoadingImpl value,
          $Res Function(_$CommissionLoadingImpl) then) =
      __$$CommissionLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$CommissionLoadingImplCopyWithImpl<$Res>
    extends _$CommissionStateCopyWithImpl<$Res, _$CommissionLoadingImpl>
    implements _$$CommissionLoadingImplCopyWith<$Res> {
  __$$CommissionLoadingImplCopyWithImpl(_$CommissionLoadingImpl _value,
      $Res Function(_$CommissionLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$CommissionLoadingImpl implements CommissionLoading {
  const _$CommissionLoadingImpl();

  @override
  String toString() {
    return 'CommissionState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$CommissionLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class CommissionLoading implements CommissionState {
  const factory CommissionLoading() = _$CommissionLoadingImpl;
}

/// @nodoc
abstract class _$$CommissionLoadedImplCopyWith<$Res> {
  factory _$$CommissionLoadedImplCopyWith(_$CommissionLoadedImpl value,
          $Res Function(_$CommissionLoadedImpl) then) =
      __$$CommissionLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Commission> commissions});
}

/// @nodoc
class __$$CommissionLoadedImplCopyWithImpl<$Res>
    extends _$CommissionStateCopyWithImpl<$Res, _$CommissionLoadedImpl>
    implements _$$CommissionLoadedImplCopyWith<$Res> {
  __$$CommissionLoadedImplCopyWithImpl(_$CommissionLoadedImpl _value,
      $Res Function(_$CommissionLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? commissions = null,
  }) {
    return _then(_$CommissionLoadedImpl(
      null == commissions
          ? _value._commissions
          : commissions // ignore: cast_nullable_to_non_nullable
              as List<Commission>,
    ));
  }
}

/// @nodoc

class _$CommissionLoadedImpl implements CommissionLoaded {
  const _$CommissionLoadedImpl(final List<Commission> commissions)
      : _commissions = commissions;

  final List<Commission> _commissions;
  @override
  List<Commission> get commissions {
    if (_commissions is EqualUnmodifiableListView) return _commissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_commissions);
  }

  @override
  String toString() {
    return 'CommissionState.loaded(commissions: $commissions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissionLoadedImpl &&
            const DeepCollectionEquality()
                .equals(other._commissions, _commissions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_commissions));

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissionLoadedImplCopyWith<_$CommissionLoadedImpl> get copyWith =>
      __$$CommissionLoadedImplCopyWithImpl<_$CommissionLoadedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) {
    return loaded(commissions);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(commissions);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(commissions);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class CommissionLoaded implements CommissionState {
  const factory CommissionLoaded(final List<Commission> commissions) =
      _$CommissionLoadedImpl;

  List<Commission> get commissions;

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissionLoadedImplCopyWith<_$CommissionLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CommissionCalculatedImplCopyWith<$Res> {
  factory _$$CommissionCalculatedImplCopyWith(_$CommissionCalculatedImpl value,
          $Res Function(_$CommissionCalculatedImpl) then) =
      __$$CommissionCalculatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({double amount});
}

/// @nodoc
class __$$CommissionCalculatedImplCopyWithImpl<$Res>
    extends _$CommissionStateCopyWithImpl<$Res, _$CommissionCalculatedImpl>
    implements _$$CommissionCalculatedImplCopyWith<$Res> {
  __$$CommissionCalculatedImplCopyWithImpl(_$CommissionCalculatedImpl _value,
      $Res Function(_$CommissionCalculatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amount = null,
  }) {
    return _then(_$CommissionCalculatedImpl(
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$CommissionCalculatedImpl implements CommissionCalculated {
  const _$CommissionCalculatedImpl(this.amount);

  @override
  final double amount;

  @override
  String toString() {
    return 'CommissionState.calculated(amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissionCalculatedImpl &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, amount);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissionCalculatedImplCopyWith<_$CommissionCalculatedImpl>
      get copyWith =>
          __$$CommissionCalculatedImplCopyWithImpl<_$CommissionCalculatedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) {
    return calculated(amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) {
    return calculated?.call(amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (calculated != null) {
      return calculated(amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) {
    return calculated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) {
    return calculated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) {
    if (calculated != null) {
      return calculated(this);
    }
    return orElse();
  }
}

abstract class CommissionCalculated implements CommissionState {
  const factory CommissionCalculated(final double amount) =
      _$CommissionCalculatedImpl;

  double get amount;

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissionCalculatedImplCopyWith<_$CommissionCalculatedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CommissionErrorImplCopyWith<$Res> {
  factory _$$CommissionErrorImplCopyWith(_$CommissionErrorImpl value,
          $Res Function(_$CommissionErrorImpl) then) =
      __$$CommissionErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$CommissionErrorImplCopyWithImpl<$Res>
    extends _$CommissionStateCopyWithImpl<$Res, _$CommissionErrorImpl>
    implements _$$CommissionErrorImplCopyWith<$Res> {
  __$$CommissionErrorImplCopyWithImpl(
      _$CommissionErrorImpl _value, $Res Function(_$CommissionErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$CommissionErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CommissionErrorImpl implements CommissionError {
  const _$CommissionErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'CommissionState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommissionErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommissionErrorImplCopyWith<_$CommissionErrorImpl> get copyWith =>
      __$$CommissionErrorImplCopyWithImpl<_$CommissionErrorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Commission> commissions) loaded,
    required TResult Function(double amount) calculated,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Commission> commissions)? loaded,
    TResult? Function(double amount)? calculated,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Commission> commissions)? loaded,
    TResult Function(double amount)? calculated,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CommissionInitial value) initial,
    required TResult Function(CommissionLoading value) loading,
    required TResult Function(CommissionLoaded value) loaded,
    required TResult Function(CommissionCalculated value) calculated,
    required TResult Function(CommissionError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CommissionInitial value)? initial,
    TResult? Function(CommissionLoading value)? loading,
    TResult? Function(CommissionLoaded value)? loaded,
    TResult? Function(CommissionCalculated value)? calculated,
    TResult? Function(CommissionError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CommissionInitial value)? initial,
    TResult Function(CommissionLoading value)? loading,
    TResult Function(CommissionLoaded value)? loaded,
    TResult Function(CommissionCalculated value)? calculated,
    TResult Function(CommissionError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class CommissionError implements CommissionState {
  const factory CommissionError(final String message) = _$CommissionErrorImpl;

  String get message;

  /// Create a copy of CommissionState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommissionErrorImplCopyWith<_$CommissionErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
