import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../shared/services/delivery/ecom_express_service.dart';
import '../../../shared/utils/logger.dart';

/// Provider for the webhook handler service
final webhookHandlerServiceProvider = Provider<WebhookHandlerService>((ref) {
  final ecomExpressService = ref.watch(ecomExpressServiceProvider);
  return WebhookHandlerService(
    firestore: FirebaseFirestore.instance,
    ecomExpressService: ecomExpressService,
  );
});

/// Service for handling webhooks from various delivery providers
class WebhookHandlerService {
  final FirebaseFirestore _firestore;
  final EcomExpressService _ecomExpressService;
  final _logger = getLogger('WebhookHandlerService');
  
  WebhookHandlerService({
    required FirebaseFirestore firestore,
    required EcomExpressService ecomExpressService,
  }) : _firestore = firestore,
       _ecomExpressService = ecomExpressService;
  
  /// Handle webhook from Ecom Express
  Future<bool> handleEcomExpressWebhook(
    String payload,
    Map<String, String> headers,
  ) async {
    try {
      // Log webhook receipt
      _logger.info('Received Ecom Express webhook');
      
      // Parse payload
      final Map<String, dynamic> data = jsonDecode(payload);
      
      // Get signature from headers
      final signature = headers['x-ecomexpress-signature'] ?? '';
      
      // Process webhook notification
      final result = await _ecomExpressService.processWebhookNotification(data, signature);
      
      // Log webhook processing result
      if (result) {
        _logger.info('Successfully processed Ecom Express webhook');
      } else {
        _logger.warning('Failed to process Ecom Express webhook');
      }
      
      // Store webhook for audit purposes
      await _storeWebhook('ecom_express', data, headers, result);
      
      return result;
    } catch (e) {
      _logger.severe('Error handling Ecom Express webhook: $e');
      
      // Store failed webhook
      await _storeWebhook(
        'ecom_express',
        {'error': e.toString()},
        headers,
        false,
      );
      
      return false;
    }
  }
  
  /// Store webhook data for audit purposes
  Future<void> _storeWebhook(
    String provider,
    Map<String, dynamic> data,
    Map<String, String> headers,
    bool processed,
  ) async {
    try {
      await _firestore.collection('webhooks').add({
        'provider': provider,
        'data': data,
        'headers': headers,
        'processed': processed,
        'receivedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      _logger.warning('Error storing webhook data: $e');
    }
  }
}
