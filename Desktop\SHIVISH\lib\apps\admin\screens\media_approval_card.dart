import 'package:flutter/material.dart';
import 'package:shivish/shared/models/media/media_model.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';
import 'package:shivish/shared/ui_components/dialogs/text_input_dialog.dart';

class MediaApprovalCard extends StatelessWidget {
  final MediaModel media;
  final VoidCallback onApprove;
  final Function(String) onReject;
  final VoidCallback onEdit;

  const MediaApprovalCard({
    super.key,
    required this.media,
    required this.onApprove,
    required this.onReject,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    media.title,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Icon(
                  media.type == MediaType.image
                      ? Icons.image
                      : Icons.video_library,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(media.description),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showRejectDialog(context),
                  icon: const Icon(Icons.close),
                  label: const Text('Reject'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _showApproveConfirmation(context),
                  icon: const Icon(Icons.check),
                  label: const Text('Approve'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showApproveConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Approve Media',
        message: 'Are you sure you want to approve this media?',
        confirmText: 'Approve',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed == true) {
      onApprove();
    }
  }

  Future<void> _showRejectDialog(BuildContext context) async {
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => const TextInputDialog(
        title: 'Reject Media',
        content: 'Please provide a reason for rejection:',
        hintText: 'Enter rejection reason',
        confirmText: 'Reject',
        cancelText: 'Cancel',
      ),
    );

    if (reason != null && reason.isNotEmpty) {
      onReject(reason);
    }
  }
}
