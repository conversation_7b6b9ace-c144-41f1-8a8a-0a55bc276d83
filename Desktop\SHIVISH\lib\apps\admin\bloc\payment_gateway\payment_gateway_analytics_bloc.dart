import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/payment_gateway.dart';
import 'package:shivish/shared/services/payment_gateway/payment_gateway_analytics_service.dart';

part 'payment_gateway_analytics_bloc.freezed.dart';
part 'payment_gateway_analytics_event.dart';
part 'payment_gateway_analytics_state.dart';

class PaymentGatewayAnalyticsBloc
    extends Bloc<PaymentGatewayAnalyticsEvent, PaymentGatewayAnalyticsState> {
  final PaymentGatewayAnalyticsService _analyticsService;

  PaymentGatewayAnalyticsBloc(this._analyticsService)
      : super(const PaymentGatewayAnalyticsState.initial()) {
    on<LoadAnalytics>(_onLoadAnalytics);
    on<UpdateDateRange>(_onUpdateDateRange);
  }

  Future<void> _onLoadAnalytics(
    LoadAnalytics event,
    Emitter<PaymentGatewayAnalyticsState> emit,
  ) async {
    try {
      emit(const PaymentGatewayAnalyticsState.loading());

      final summary = await _analyticsService.getAnalyticsSummary(
        event.gateway,
        event.dateRange,
      );
      final dailyCounts = await _analyticsService.getDailyTransactionCounts(
        event.gateway,
        event.dateRange,
      );
      final statusDistribution = await _analyticsService.getStatusDistribution(
        event.gateway,
        event.dateRange,
      );

      emit(PaymentGatewayAnalyticsState.loaded(
        gateway: event.gateway,
        summary: summary,
        dailyCounts: dailyCounts,
        statusDistribution: statusDistribution,
      ));
    } catch (e) {
      emit(PaymentGatewayAnalyticsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateDateRange(
    UpdateDateRange event,
    Emitter<PaymentGatewayAnalyticsState> emit,
  ) async {
    if (state is Loaded) {
      final currentState = state as Loaded;
      add(LoadAnalytics(
        gateway: currentState.gateway,
        dateRange: event.dateRange,
      ));
    }
  }
}
