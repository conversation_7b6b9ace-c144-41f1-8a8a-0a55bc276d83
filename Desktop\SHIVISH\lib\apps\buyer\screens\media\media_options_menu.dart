import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/media_provider.dart';
import '../../providers/auth_provider.dart';
import '../../../../shared/services/media/media_service_provider.dart';

class MediaOptionsMenu extends ConsumerWidget {
  const MediaOptionsMenu({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final mediaState = ref.watch(mediaProvider);
    final mediaService = ref.read(mediaServiceProvider);
    final user = ref.read(authStateProvider).user;

    Future<void> handleDownload() async {
      try {
        await mediaService.downloadMedia(mediaState.id);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Download started')),
          );
          Navigator.pop(context);
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to download: ${e.toString()}'),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      }
    }

    Future<void> handleShare() async {
      try {
        await mediaService.shareMedia(mediaState.id);
        if (context.mounted) {
          Navigator.pop(context);
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to share: ${e.toString()}'),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      }
    }

    Future<void> handleAddToPlaylist() async {
      try {
        // Get available playlists from the media provider
        final playlists = ref.read(mediaProvider).playlistOptions ?? [];

        if (context.mounted) {
          final selectedPlaylistId = await showDialog<String>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Select Playlist'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Add to Favorites option
                  ListTile(
                    leading: const Icon(Icons.favorite),
                    title: const Text('Add to Favorites'),
                    onTap: () => Navigator.pop(context, 'favorites'),
                  ),
                  // Add to Recently Played option
                  ListTile(
                    leading: const Icon(Icons.history),
                    title: const Text('Add to Recently Played'),
                    onTap: () => Navigator.pop(context, 'recent'),
                  ),
                  // Add to Queue option
                  ListTile(
                    leading: const Icon(Icons.queue_music),
                    title: const Text('Add to Queue'),
                    onTap: () => Navigator.pop(context, 'queue'),
                  ),
                  // Custom playlists
                  ...playlists.map((playlist) => ListTile(
                        leading: const Icon(Icons.playlist_play),
                        title: Text(playlist['name'] ?? 'Untitled Playlist'),
                        subtitle:
                            Text('${playlist['items']?.length ?? 0} items'),
                        onTap: () => Navigator.pop(context, playlist['id']),
                      )),
                  // Create new playlist option
                  ListTile(
                    leading: const Icon(Icons.add),
                    title: const Text('Create New Playlist'),
                    onTap: () => Navigator.pop(context, 'new'),
                  ),
                ],
              ),
            ),
          );

          if (selectedPlaylistId != null) {
            if (selectedPlaylistId == 'new') {
              // Show create playlist dialog
              final nameController = TextEditingController();
              if (context.mounted) {
                final name = await showDialog<String>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Create New Playlist'),
                    content: TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Playlist Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () =>
                            Navigator.pop(context, nameController.text),
                        child: const Text('Create'),
                      ),
                    ],
                  ),
                );

                if (name != null && name.isNotEmpty) {
                  // Create new playlist and add item
                  await ref.read(mediaProvider.notifier).createPlaylist(name);
                  await ref
                      .read(mediaProvider.notifier)
                      .addToPlaylist(mediaState.id, name);
                }
              }
            } else if (selectedPlaylistId == 'favorites') {
              // Add to favorites
              await ref.read(mediaProvider.notifier).toggleFavorite();
            } else if (selectedPlaylistId == 'recent') {
              // Add to recently played
              await ref
                  .read(mediaProvider.notifier)
                  .addToRecentlyPlayed(mediaState.id);
            } else if (selectedPlaylistId == 'queue') {
              // Add to queue
              await ref.read(mediaProvider.notifier).addToQueue(mediaState.id);
            } else {
              // Add to selected playlist
              await ref
                  .read(mediaProvider.notifier)
                  .addToPlaylist(mediaState.id, selectedPlaylistId);
            }

            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Added to playlist')),
              );
              Navigator.pop(context);
            }
          }
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to add to playlist: ${e.toString()}'),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      }
    }

    Future<void> handleShowInfo() async {
      try {
        final info = await mediaService.getMediaInfo(mediaState.id);
        if (context.mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Media Info'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Title: ${info['title']}'),
                  Text('Artist: ${info['artist']}'),
                  Text('Type: ${info['type']}'),
                  Text('Size: ${(info['size'] as int) ~/ 1024} KB'),
                  Text('Content Type: ${info['contentType']}'),
                  Text('Created: ${info['createdAt']}'),
                  Text('Updated: ${info['updatedAt']}'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to get info: ${e.toString()}'),
              backgroundColor: theme.colorScheme.error,
            ),
          );
        }
      }
    }

    Future<void> handleReport() async {
      if (user == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please sign in to report media')),
        );
        return;
      }

      final reasonController = TextEditingController();
      if (context.mounted) {
        final reason = await showDialog<String>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Report Media'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Please describe the issue:'),
                const SizedBox(height: 8),
                TextField(
                  controller: reasonController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    hintText: 'Enter reason for report',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, reasonController.text),
                child: const Text('Submit'),
              ),
            ],
          ),
        );

        if (reason != null && reason.isNotEmpty) {
          try {
            await mediaService.reportMedia(mediaState.id, reason, user.uid);
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Report submitted')),
              );
              Navigator.pop(context);
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to submit report: ${e.toString()}'),
                  backgroundColor: theme.colorScheme.error,
                ),
              );
            }
          }
        }
      }
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ListTile(
          leading: const Icon(Icons.download),
          title: const Text('Download'),
          onTap: handleDownload,
        ),
        ListTile(
          leading: const Icon(Icons.share),
          title: const Text('Share'),
          onTap: handleShare,
        ),
        ListTile(
          leading: const Icon(Icons.playlist_add),
          title: const Text('Add to Playlist'),
          onTap: handleAddToPlaylist,
        ),
        ListTile(
          leading: const Icon(Icons.info),
          title: const Text('Media Info'),
          onTap: handleShowInfo,
        ),
        ListTile(
          leading: const Icon(Icons.report),
          title: const Text('Report'),
          onTap: handleReport,
        ),
      ],
    );
  }
}
