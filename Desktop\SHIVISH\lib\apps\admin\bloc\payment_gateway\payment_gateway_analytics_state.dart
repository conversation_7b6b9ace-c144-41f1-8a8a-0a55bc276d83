part of 'payment_gateway_analytics_bloc.dart';

@freezed
class PaymentGatewayAnalyticsState with _$PaymentGatewayAnalyticsState {
  const factory PaymentGatewayAnalyticsState.initial() = Initial;
  const factory PaymentGatewayAnalyticsState.loading() = Loading;
  const factory PaymentGatewayAnalyticsState.loaded({
    required PaymentGateway gateway,
    required Map<String, dynamic> summary,
    required List<Map<String, dynamic>> dailyCounts,
    required List<Map<String, dynamic>> statusDistribution,
  }) = Loaded;
  const factory PaymentGatewayAnalyticsState.error(String message) = Error;
}
