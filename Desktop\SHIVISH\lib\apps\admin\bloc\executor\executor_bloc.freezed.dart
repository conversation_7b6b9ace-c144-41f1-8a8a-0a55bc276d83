// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'executor_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ExecutorEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExecutorEventCopyWith<$Res> {
  factory $ExecutorEventCopyWith(
          ExecutorEvent value, $Res Function(ExecutorEvent) then) =
      _$ExecutorEventCopyWithImpl<$Res, ExecutorEvent>;
}

/// @nodoc
class _$ExecutorEventCopyWithImpl<$Res, $Val extends ExecutorEvent>
    implements $ExecutorEventCopyWith<$Res> {
  _$ExecutorEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadExecutorsImplCopyWith<$Res> {
  factory _$$LoadExecutorsImplCopyWith(
          _$LoadExecutorsImpl value, $Res Function(_$LoadExecutorsImpl) then) =
      __$$LoadExecutorsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadExecutorsImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$LoadExecutorsImpl>
    implements _$$LoadExecutorsImplCopyWith<$Res> {
  __$$LoadExecutorsImplCopyWithImpl(
      _$LoadExecutorsImpl _value, $Res Function(_$LoadExecutorsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadExecutorsImpl implements LoadExecutors {
  const _$LoadExecutorsImpl();

  @override
  String toString() {
    return 'ExecutorEvent.loadExecutors()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadExecutorsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadExecutors();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadExecutors?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadExecutors != null) {
      return loadExecutors();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadExecutors(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadExecutors?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadExecutors != null) {
      return loadExecutors(this);
    }
    return orElse();
  }
}

abstract class LoadExecutors implements ExecutorEvent {
  const factory LoadExecutors() = _$LoadExecutorsImpl;
}

/// @nodoc
abstract class _$$LoadMoreExecutorsImplCopyWith<$Res> {
  factory _$$LoadMoreExecutorsImplCopyWith(_$LoadMoreExecutorsImpl value,
          $Res Function(_$LoadMoreExecutorsImpl) then) =
      __$$LoadMoreExecutorsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreExecutorsImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$LoadMoreExecutorsImpl>
    implements _$$LoadMoreExecutorsImplCopyWith<$Res> {
  __$$LoadMoreExecutorsImplCopyWithImpl(_$LoadMoreExecutorsImpl _value,
      $Res Function(_$LoadMoreExecutorsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreExecutorsImpl implements LoadMoreExecutors {
  const _$LoadMoreExecutorsImpl();

  @override
  String toString() {
    return 'ExecutorEvent.loadMoreExecutors()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreExecutorsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadMoreExecutors();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadMoreExecutors?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMoreExecutors != null) {
      return loadMoreExecutors();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadMoreExecutors(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadMoreExecutors?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMoreExecutors != null) {
      return loadMoreExecutors(this);
    }
    return orElse();
  }
}

abstract class LoadMoreExecutors implements ExecutorEvent {
  const factory LoadMoreExecutors() = _$LoadMoreExecutorsImpl;
}

/// @nodoc
abstract class _$$CreateExecutorImplCopyWith<$Res> {
  factory _$$CreateExecutorImplCopyWith(_$CreateExecutorImpl value,
          $Res Function(_$CreateExecutorImpl) then) =
      __$$CreateExecutorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, dynamic> data});
}

/// @nodoc
class __$$CreateExecutorImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$CreateExecutorImpl>
    implements _$$CreateExecutorImplCopyWith<$Res> {
  __$$CreateExecutorImplCopyWithImpl(
      _$CreateExecutorImpl _value, $Res Function(_$CreateExecutorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$CreateExecutorImpl(
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$CreateExecutorImpl implements CreateExecutor {
  const _$CreateExecutorImpl({required final Map<String, dynamic> data})
      : _data = data;

  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  String toString() {
    return 'ExecutorEvent.createExecutor(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateExecutorImpl &&
            const DeepCollectionEquality().equals(other._data, _data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_data));

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateExecutorImplCopyWith<_$CreateExecutorImpl> get copyWith =>
      __$$CreateExecutorImplCopyWithImpl<_$CreateExecutorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return createExecutor(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return createExecutor?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (createExecutor != null) {
      return createExecutor(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return createExecutor(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return createExecutor?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (createExecutor != null) {
      return createExecutor(this);
    }
    return orElse();
  }
}

abstract class CreateExecutor implements ExecutorEvent {
  const factory CreateExecutor({required final Map<String, dynamic> data}) =
      _$CreateExecutorImpl;

  Map<String, dynamic> get data;

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateExecutorImplCopyWith<_$CreateExecutorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateExecutorStatusImplCopyWith<$Res> {
  factory _$$UpdateExecutorStatusImplCopyWith(_$UpdateExecutorStatusImpl value,
          $Res Function(_$UpdateExecutorStatusImpl) then) =
      __$$UpdateExecutorStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String status});
}

/// @nodoc
class __$$UpdateExecutorStatusImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$UpdateExecutorStatusImpl>
    implements _$$UpdateExecutorStatusImplCopyWith<$Res> {
  __$$UpdateExecutorStatusImplCopyWithImpl(_$UpdateExecutorStatusImpl _value,
      $Res Function(_$UpdateExecutorStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? status = null,
  }) {
    return _then(_$UpdateExecutorStatusImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateExecutorStatusImpl implements UpdateExecutorStatus {
  const _$UpdateExecutorStatusImpl({required this.id, required this.status});

  @override
  final String id;
  @override
  final String status;

  @override
  String toString() {
    return 'ExecutorEvent.updateExecutorStatus(id: $id, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateExecutorStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.status, status) || other.status == status));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, status);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateExecutorStatusImplCopyWith<_$UpdateExecutorStatusImpl>
      get copyWith =>
          __$$UpdateExecutorStatusImplCopyWithImpl<_$UpdateExecutorStatusImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updateExecutorStatus(id, status);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updateExecutorStatus?.call(id, status);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateExecutorStatus != null) {
      return updateExecutorStatus(id, status);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updateExecutorStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updateExecutorStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateExecutorStatus != null) {
      return updateExecutorStatus(this);
    }
    return orElse();
  }
}

abstract class UpdateExecutorStatus implements ExecutorEvent {
  const factory UpdateExecutorStatus(
      {required final String id,
      required final String status}) = _$UpdateExecutorStatusImpl;

  String get id;
  String get status;

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateExecutorStatusImplCopyWith<_$UpdateExecutorStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateExecutorPerformanceImplCopyWith<$Res> {
  factory _$$UpdateExecutorPerformanceImplCopyWith(
          _$UpdateExecutorPerformanceImpl value,
          $Res Function(_$UpdateExecutorPerformanceImpl) then) =
      __$$UpdateExecutorPerformanceImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> metrics});
}

/// @nodoc
class __$$UpdateExecutorPerformanceImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$UpdateExecutorPerformanceImpl>
    implements _$$UpdateExecutorPerformanceImplCopyWith<$Res> {
  __$$UpdateExecutorPerformanceImplCopyWithImpl(
      _$UpdateExecutorPerformanceImpl _value,
      $Res Function(_$UpdateExecutorPerformanceImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? metrics = null,
  }) {
    return _then(_$UpdateExecutorPerformanceImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      metrics: null == metrics
          ? _value._metrics
          : metrics // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateExecutorPerformanceImpl implements UpdateExecutorPerformance {
  const _$UpdateExecutorPerformanceImpl(
      {required this.id, required final Map<String, dynamic> metrics})
      : _metrics = metrics;

  @override
  final String id;
  final Map<String, dynamic> _metrics;
  @override
  Map<String, dynamic> get metrics {
    if (_metrics is EqualUnmodifiableMapView) return _metrics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metrics);
  }

  @override
  String toString() {
    return 'ExecutorEvent.updateExecutorPerformance(id: $id, metrics: $metrics)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateExecutorPerformanceImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._metrics, _metrics));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_metrics));

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateExecutorPerformanceImplCopyWith<_$UpdateExecutorPerformanceImpl>
      get copyWith => __$$UpdateExecutorPerformanceImplCopyWithImpl<
          _$UpdateExecutorPerformanceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updateExecutorPerformance(id, metrics);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updateExecutorPerformance?.call(id, metrics);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateExecutorPerformance != null) {
      return updateExecutorPerformance(id, metrics);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updateExecutorPerformance(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updateExecutorPerformance?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateExecutorPerformance != null) {
      return updateExecutorPerformance(this);
    }
    return orElse();
  }
}

abstract class UpdateExecutorPerformance implements ExecutorEvent {
  const factory UpdateExecutorPerformance(
          {required final String id,
          required final Map<String, dynamic> metrics}) =
      _$UpdateExecutorPerformanceImpl;

  String get id;
  Map<String, dynamic> get metrics;

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateExecutorPerformanceImplCopyWith<_$UpdateExecutorPerformanceImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteExecutorImplCopyWith<$Res> {
  factory _$$DeleteExecutorImplCopyWith(_$DeleteExecutorImpl value,
          $Res Function(_$DeleteExecutorImpl) then) =
      __$$DeleteExecutorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteExecutorImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$DeleteExecutorImpl>
    implements _$$DeleteExecutorImplCopyWith<$Res> {
  __$$DeleteExecutorImplCopyWithImpl(
      _$DeleteExecutorImpl _value, $Res Function(_$DeleteExecutorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteExecutorImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteExecutorImpl implements DeleteExecutor {
  const _$DeleteExecutorImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'ExecutorEvent.deleteExecutor(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteExecutorImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteExecutorImplCopyWith<_$DeleteExecutorImpl> get copyWith =>
      __$$DeleteExecutorImplCopyWithImpl<_$DeleteExecutorImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return deleteExecutor(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return deleteExecutor?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deleteExecutor != null) {
      return deleteExecutor(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return deleteExecutor(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return deleteExecutor?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deleteExecutor != null) {
      return deleteExecutor(this);
    }
    return orElse();
  }
}

abstract class DeleteExecutor implements ExecutorEvent {
  const factory DeleteExecutor({required final String id}) =
      _$DeleteExecutorImpl;

  String get id;

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteExecutorImplCopyWith<_$DeleteExecutorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StartRealtimeUpdatesImplCopyWith(_$StartRealtimeUpdatesImpl value,
          $Res Function(_$StartRealtimeUpdatesImpl) then) =
      __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$StartRealtimeUpdatesImpl>
    implements _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  __$$StartRealtimeUpdatesImplCopyWithImpl(_$StartRealtimeUpdatesImpl _value,
      $Res Function(_$StartRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartRealtimeUpdatesImpl implements StartRealtimeUpdates {
  const _$StartRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'ExecutorEvent.startRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class StartRealtimeUpdates implements ExecutorEvent {
  const factory StartRealtimeUpdates() = _$StartRealtimeUpdatesImpl;
}

/// @nodoc
abstract class _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StopRealtimeUpdatesImplCopyWith(_$StopRealtimeUpdatesImpl value,
          $Res Function(_$StopRealtimeUpdatesImpl) then) =
      __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$ExecutorEventCopyWithImpl<$Res, _$StopRealtimeUpdatesImpl>
    implements _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  __$$StopRealtimeUpdatesImplCopyWithImpl(_$StopRealtimeUpdatesImpl _value,
      $Res Function(_$StopRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopRealtimeUpdatesImpl implements StopRealtimeUpdates {
  const _$StopRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'ExecutorEvent.stopRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StopRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadExecutors,
    required TResult Function() loadMoreExecutors,
    required TResult Function(Map<String, dynamic> data) createExecutor,
    required TResult Function(String id, String status) updateExecutorStatus,
    required TResult Function(String id, Map<String, dynamic> metrics)
        updateExecutorPerformance,
    required TResult Function(String id) deleteExecutor,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadExecutors,
    TResult? Function()? loadMoreExecutors,
    TResult? Function(Map<String, dynamic> data)? createExecutor,
    TResult? Function(String id, String status)? updateExecutorStatus,
    TResult? Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult? Function(String id)? deleteExecutor,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadExecutors,
    TResult Function()? loadMoreExecutors,
    TResult Function(Map<String, dynamic> data)? createExecutor,
    TResult Function(String id, String status)? updateExecutorStatus,
    TResult Function(String id, Map<String, dynamic> metrics)?
        updateExecutorPerformance,
    TResult Function(String id)? deleteExecutor,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadExecutors value) loadExecutors,
    required TResult Function(LoadMoreExecutors value) loadMoreExecutors,
    required TResult Function(CreateExecutor value) createExecutor,
    required TResult Function(UpdateExecutorStatus value) updateExecutorStatus,
    required TResult Function(UpdateExecutorPerformance value)
        updateExecutorPerformance,
    required TResult Function(DeleteExecutor value) deleteExecutor,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadExecutors value)? loadExecutors,
    TResult? Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult? Function(CreateExecutor value)? createExecutor,
    TResult? Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult? Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult? Function(DeleteExecutor value)? deleteExecutor,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadExecutors value)? loadExecutors,
    TResult Function(LoadMoreExecutors value)? loadMoreExecutors,
    TResult Function(CreateExecutor value)? createExecutor,
    TResult Function(UpdateExecutorStatus value)? updateExecutorStatus,
    TResult Function(UpdateExecutorPerformance value)?
        updateExecutorPerformance,
    TResult Function(DeleteExecutor value)? deleteExecutor,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class StopRealtimeUpdates implements ExecutorEvent {
  const factory StopRealtimeUpdates() = _$StopRealtimeUpdatesImpl;
}

/// @nodoc
mixin _$ExecutorState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Executor> executors) loaded,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Executor> executors)? loaded,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Executor> executors)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExecutorStateCopyWith<$Res> {
  factory $ExecutorStateCopyWith(
          ExecutorState value, $Res Function(ExecutorState) then) =
      _$ExecutorStateCopyWithImpl<$Res, ExecutorState>;
}

/// @nodoc
class _$ExecutorStateCopyWithImpl<$Res, $Val extends ExecutorState>
    implements $ExecutorStateCopyWith<$Res> {
  _$ExecutorStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$ExecutorStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'ExecutorState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Executor> executors) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Executor> executors)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Executor> executors)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements ExecutorState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$ExecutorStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'ExecutorState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Executor> executors) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Executor> executors)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Executor> executors)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements ExecutorState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Executor> executors});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$ExecutorStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? executors = null,
  }) {
    return _then(_$LoadedImpl(
      null == executors
          ? _value._executors
          : executors // ignore: cast_nullable_to_non_nullable
              as List<Executor>,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements Loaded {
  const _$LoadedImpl(final List<Executor> executors) : _executors = executors;

  final List<Executor> _executors;
  @override
  List<Executor> get executors {
    if (_executors is EqualUnmodifiableListView) return _executors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_executors);
  }

  @override
  String toString() {
    return 'ExecutorState.loaded(executors: $executors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality()
                .equals(other._executors, _executors));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_executors));

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Executor> executors) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(executors);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Executor> executors)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(executors);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Executor> executors)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(executors);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class Loaded implements ExecutorState {
  const factory Loaded(final List<Executor> executors) = _$LoadedImpl;

  List<Executor> get executors;

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$ExecutorStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ExecutorState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Executor> executors) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Executor> executors)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Executor> executors)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class Error implements ExecutorState {
  const factory Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of ExecutorState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
