import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/ai_settings.dart';

part 'ai_settings_state.freezed.dart';

@freezed
class AISettingsState with _$AISettingsState {
  const factory AISettingsState.initial() = Initial;
  const factory AISettingsState.loading() = Loading;
  const factory AISettingsState.loaded(List<AISettings> settings) = Loaded;
  const factory AISettingsState.error(String message) = Error;
}
