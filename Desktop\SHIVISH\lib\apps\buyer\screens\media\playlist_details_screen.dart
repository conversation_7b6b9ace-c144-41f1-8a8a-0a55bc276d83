import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import '../../providers/media_provider.dart';

class PlaylistDetailsScreen extends ConsumerWidget {
  final String playlistId;

  const PlaylistDetailsScreen({
    super.key,
    required this.playlistId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaState = ref.watch(mediaProvider);
    final playlist = mediaState.playlist.firstWhere(
      (item) => item['id'] == playlistId,
      orElse: () => {},
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(playlist['name'] ?? 'Playlist Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showPlaylistOptions(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildPlaylistHeader(context, ref, playlist),
          Expanded(
            child: _buildPlaylistItems(context, ref, playlist),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddItemsDialog(context, ref),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildPlaylistHeader(
      BuildContext context, WidgetRef ref, Map<String, dynamic> playlist) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      playlist['name'] ?? 'Untitled Playlist',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Text(
                      '${playlist['items']?.length ?? 0} items',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _showEditPlaylistDialog(context, ref),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton.icon(
                onPressed: () => ref
                    .read(mediaProvider.notifier)
                    .playAllItems(playlist['items'] ?? []),
                icon: const Icon(Icons.play_arrow),
                label: const Text('Play All'),
              ),
              ElevatedButton.icon(
                onPressed: () =>
                    ref.read(mediaProvider.notifier).shufflePlaylist(),
                icon: const Icon(Icons.shuffle),
                label: const Text('Shuffle'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlaylistItems(
      BuildContext context, WidgetRef ref, Map<String, dynamic> playlist) {
    final items = playlist['items'] as List<Map<String, dynamic>>? ?? [];

    if (items.isEmpty) {
      return Center(
        child: Text(
          'No items in playlist',
          style: Theme.of(context).textTheme.titleMedium,
        ),
      );
    }

    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return ListTile(
          leading: Icon(
            item['type'] == 'Audio' ? Icons.audio_file : Icons.video_file,
            color: Theme.of(context).primaryColor,
          ),
          title: Text(item['title'] ?? 'Untitled'),
          subtitle: Text(item['artist'] ?? 'Unknown Artist'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  item['isFavorite'] == true
                      ? Icons.favorite
                      : Icons.favorite_border,
                  color: item['isFavorite'] == true ? Colors.red : null,
                ),
                onPressed: () => ref
                    .read(mediaProvider.notifier)
                    .togglePlaylistItemFavorite(item['id']),
              ),
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: () => _showItemOptions(context, ref, item),
              ),
            ],
          ),
          onTap: () => ref
              .read(mediaProvider.notifier)
              .navigateToMediaPlayer(item['id']),
        );
      },
    );
  }

  Future<void> _showPlaylistOptions(BuildContext context, WidgetRef ref) async {
    await ref.read(mediaProvider.notifier).showPlaylistOptions(playlistId);

    final options = ref.read(mediaProvider).playlistOptions ?? [];
    if (options.isEmpty) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: options
            .map((option) => ListTile(
                  title: Text(option['label']),
                  onTap: () {
                    Navigator.pop(context);
                    _handlePlaylistOption(context, ref, option['id']);
                  },
                ))
            .toList(),
      ),
    );
  }

  void _handlePlaylistOption(
      BuildContext context, WidgetRef ref, String optionId) {
    switch (optionId) {
      case 'edit':
        _showEditPlaylistDialog(context, ref);
        break;
      case 'share':
        _sharePlaylist(context, ref);
        break;
      case 'delete':
        _showDeleteConfirmation(context, ref);
        break;
    }
  }

  Future<void> _showEditPlaylistDialog(
      BuildContext context, WidgetRef ref) async {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Playlist'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                hintText: 'Enter playlist name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter playlist description',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(mediaProvider.notifier).editPlaylistDetails(
                playlistId,
                {
                  'name': nameController.text,
                  'description': descriptionController.text,
                },
              );
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddItemsDialog(BuildContext context, WidgetRef ref) async {
    final mediaState = ref.watch(mediaProvider);
    final availableItems = mediaState.playlist
        .where((item) => item['type'] != 'playlist')
        .toList();

    if (availableItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No media items available to add')),
      );
      return;
    }

    final selectedItems = <String, bool>{};
    for (var item in availableItems) {
      selectedItems[item['id']] = false;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Items to Playlist'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableItems.length,
              itemBuilder: (context, index) {
                final item = availableItems[index];
                return CheckboxListTile(
                  title: Text(item['title'] ?? 'Untitled'),
                  subtitle: Text(item['artist'] ?? 'Unknown Artist'),
                  value: selectedItems[item['id']] ?? false,
                  onChanged: (bool? value) {
                    setState(() {
                      selectedItems[item['id']] = value ?? false;
                    });
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final selectedIds = selectedItems.entries
                    .where((entry) => entry.value)
                    .map((entry) => entry.key)
                    .toList();

                if (selectedIds.isNotEmpty) {
                  ref.read(mediaProvider.notifier).addItemsToPlaylistByIds(
                        playlistId,
                        selectedIds,
                      );
                  Navigator.pop(context);
                }
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showItemOptions(
      BuildContext context, WidgetRef ref, Map<String, dynamic> item) async {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Remove from playlist'),
            onTap: () {
              ref.read(mediaProvider.notifier).removeFromPlaylist(
                    playlistId,
                    item['id'],
                  );
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Add to queue'),
            onTap: () {
              ref.read(mediaProvider.notifier).addToQueue(item['id']);
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(
      BuildContext context, WidgetRef ref) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Playlist'),
        content: const Text('Are you sure you want to delete this playlist?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(mediaProvider.notifier).deletePlaylist(playlistId);
              Navigator.pop(context);
              Navigator.pop(context); // Return to playlist list
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _sharePlaylist(BuildContext context, WidgetRef ref) async {
    final mediaState = ref.watch(mediaProvider);
    final playlist = mediaState.playlist.firstWhere(
      (item) => item['id'] == playlistId,
      orElse: () => {},
    );

    if (playlist.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Playlist not found')),
      );
      return;
    }

    final items = playlist['items'] as List<Map<String, dynamic>>? ?? [];
    final itemCount = items.length;

    final shareText = '''
${playlist['name'] ?? 'Untitled Playlist'}
${playlist['description'] ?? ''}

$itemCount items in this playlist
''';

    try {
      await Share.share(shareText);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to share playlist: $e')),
        );
      }
    }
  }
}
