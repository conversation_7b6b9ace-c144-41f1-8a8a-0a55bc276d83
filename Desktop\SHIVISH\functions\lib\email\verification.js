"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var _a, _b;
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendVerificationEmail = void 0;
const functions = __importStar(require("firebase-functions"));
const nodemailer = __importStar(require("nodemailer"));
// Configure the email transport using environment variables
// These should be set in the Firebase Functions configuration
// firebase functions:config:set gmail.email="<EMAIL>" gmail.password="your-app-password"
const gmailEmail = (_a = functions.config().gmail) === null || _a === void 0 ? void 0 : _a.email;
const gmailPassword = (_b = functions.config().gmail) === null || _b === void 0 ? void 0 : _b.password;
// Validate email configuration
if (!gmailEmail || !gmailPassword) {
    functions.logger.error('Email configuration is missing. Please set gmail.email and gmail.password in Firebase Functions config.');
}
// Create mail transport
const mailTransport = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: gmailEmail,
        pass: gmailPassword,
    },
    // Add security options
    secure: true,
    tls: {
        // Reject unauthorized connections
        rejectUnauthorized: true
    }
});
// Send verification email with OTP
exports.sendVerificationEmail = functions.https.onCall(async (data, context) => {
    // Validate input data
    const { email, otp, type } = data;
    if (!email) {
        throw new functions.https.HttpsError('invalid-argument', 'Email is required');
    }
    if (!otp) {
        throw new functions.https.HttpsError('invalid-argument', 'OTP is required');
    }
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid email format');
    }
    try {
        // Log sending attempt (without exposing the OTP in production)
        functions.logger.info(`Sending verification email to ${email}`);
        // Create email content
        const mailOptions = {
            from: `Saviour App <${gmailEmail}>`,
            to: email,
        };
        // Set subject and text based on type
        if (type === 'verification') {
            mailOptions.subject = 'Verify your email for Saviour App';
            mailOptions.html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4CAF50; padding: 20px; text-align: center; color: white;">
            <h1>Saviour App</h1>
          </div>
          <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
            <h2>Email Verification</h2>
            <p>Thank you for registering with Saviour App. Please use the following verification code to complete your registration:</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
              ${otp}
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>If you did not request this verification, please ignore this email.</p>
            <p>Thank you,<br>The Saviour App Team</p>
          </div>
          <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px; color: #666;">
            <p>This is an automated email. Please do not reply to this message.</p>
          </div>
        </div>
      `;
        }
        else {
            mailOptions.subject = 'Your OTP for Saviour App';
            mailOptions.html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #4CAF50; padding: 20px; text-align: center; color: white;">
            <h1>Saviour App</h1>
          </div>
          <div style="padding: 20px; border: 1px solid #ddd; border-top: none;">
            <h2>Your One-Time Password</h2>
            <p>You requested an OTP for Saviour App. Please use the following code:</p>
            <div style="background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;">
              ${otp}
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>If you did not request this OTP, please ignore this email.</p>
            <p>Thank you,<br>The Saviour App Team</p>
          </div>
          <div style="background-color: #f5f5f5; padding: 10px; text-align: center; font-size: 12px; color: #666;">
            <p>This is an automated email. Please do not reply to this message.</p>
          </div>
        </div>
      `;
        }
        // Send the email
        await mailTransport.sendMail(mailOptions);
        // Log success (without exposing the OTP)
        functions.logger.info(`Successfully sent verification email to ${email}`);
        // Return success
        return { success: true };
    }
    catch (error) {
        // Log error details for debugging
        functions.logger.error('Error sending email:', error);
        // Return a user-friendly error message
        throw new functions.https.HttpsError('internal', 'Failed to send verification email. Please try again later.');
    }
});
//# sourceMappingURL=verification.js.map