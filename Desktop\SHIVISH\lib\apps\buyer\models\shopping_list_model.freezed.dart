// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shopping_list_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ShoppingListModel _$ShoppingListModelFromJson(Map<String, dynamic> json) {
  return _ShoppingListModel.fromJson(json);
}

/// @nodoc
mixin _$ShoppingListModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get itemCount => throw _privateConstructorUsedError;
  double get totalPrice => throw _privateConstructorUsedError;
  bool get isShared => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  List<String> get sharedWith => throw _privateConstructorUsedError;
  List<ShoppingListItemModel> get items => throw _privateConstructorUsedError;

  /// Serializes this ShoppingListModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShoppingListModelCopyWith<ShoppingListModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShoppingListModelCopyWith<$Res> {
  factory $ShoppingListModelCopyWith(
          ShoppingListModel value, $Res Function(ShoppingListModel) then) =
      _$ShoppingListModelCopyWithImpl<$Res, ShoppingListModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      int itemCount,
      double totalPrice,
      bool isShared,
      DateTime? createdAt,
      DateTime? updatedAt,
      String createdBy,
      List<String> sharedWith,
      List<ShoppingListItemModel> items});
}

/// @nodoc
class _$ShoppingListModelCopyWithImpl<$Res, $Val extends ShoppingListModel>
    implements $ShoppingListModelCopyWith<$Res> {
  _$ShoppingListModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? itemCount = null,
    Object? totalPrice = null,
    Object? isShared = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = null,
    Object? sharedWith = null,
    Object? items = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalPrice: null == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isShared: null == isShared
          ? _value.isShared
          : isShared // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      sharedWith: null == sharedWith
          ? _value.sharedWith
          : sharedWith // ignore: cast_nullable_to_non_nullable
              as List<String>,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShoppingListItemModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShoppingListModelImplCopyWith<$Res>
    implements $ShoppingListModelCopyWith<$Res> {
  factory _$$ShoppingListModelImplCopyWith(_$ShoppingListModelImpl value,
          $Res Function(_$ShoppingListModelImpl) then) =
      __$$ShoppingListModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      int itemCount,
      double totalPrice,
      bool isShared,
      DateTime? createdAt,
      DateTime? updatedAt,
      String createdBy,
      List<String> sharedWith,
      List<ShoppingListItemModel> items});
}

/// @nodoc
class __$$ShoppingListModelImplCopyWithImpl<$Res>
    extends _$ShoppingListModelCopyWithImpl<$Res, _$ShoppingListModelImpl>
    implements _$$ShoppingListModelImplCopyWith<$Res> {
  __$$ShoppingListModelImplCopyWithImpl(_$ShoppingListModelImpl _value,
      $Res Function(_$ShoppingListModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? itemCount = null,
    Object? totalPrice = null,
    Object? isShared = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? createdBy = null,
    Object? sharedWith = null,
    Object? items = null,
  }) {
    return _then(_$ShoppingListModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      itemCount: null == itemCount
          ? _value.itemCount
          : itemCount // ignore: cast_nullable_to_non_nullable
              as int,
      totalPrice: null == totalPrice
          ? _value.totalPrice
          : totalPrice // ignore: cast_nullable_to_non_nullable
              as double,
      isShared: null == isShared
          ? _value.isShared
          : isShared // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      sharedWith: null == sharedWith
          ? _value._sharedWith
          : sharedWith // ignore: cast_nullable_to_non_nullable
              as List<String>,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShoppingListItemModel>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShoppingListModelImpl implements _ShoppingListModel {
  const _$ShoppingListModelImpl(
      {required this.id,
      required this.name,
      this.description,
      this.itemCount = 0,
      this.totalPrice = 0.0,
      this.isShared = false,
      this.createdAt,
      this.updatedAt,
      required this.createdBy,
      final List<String> sharedWith = const [],
      final List<ShoppingListItemModel> items = const []})
      : _sharedWith = sharedWith,
        _items = items;

  factory _$ShoppingListModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShoppingListModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  @JsonKey()
  final int itemCount;
  @override
  @JsonKey()
  final double totalPrice;
  @override
  @JsonKey()
  final bool isShared;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final String createdBy;
  final List<String> _sharedWith;
  @override
  @JsonKey()
  List<String> get sharedWith {
    if (_sharedWith is EqualUnmodifiableListView) return _sharedWith;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sharedWith);
  }

  final List<ShoppingListItemModel> _items;
  @override
  @JsonKey()
  List<ShoppingListItemModel> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  String toString() {
    return 'ShoppingListModel(id: $id, name: $name, description: $description, itemCount: $itemCount, totalPrice: $totalPrice, isShared: $isShared, createdAt: $createdAt, updatedAt: $updatedAt, createdBy: $createdBy, sharedWith: $sharedWith, items: $items)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShoppingListModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.itemCount, itemCount) ||
                other.itemCount == itemCount) &&
            (identical(other.totalPrice, totalPrice) ||
                other.totalPrice == totalPrice) &&
            (identical(other.isShared, isShared) ||
                other.isShared == isShared) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            const DeepCollectionEquality()
                .equals(other._sharedWith, _sharedWith) &&
            const DeepCollectionEquality().equals(other._items, _items));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      description,
      itemCount,
      totalPrice,
      isShared,
      createdAt,
      updatedAt,
      createdBy,
      const DeepCollectionEquality().hash(_sharedWith),
      const DeepCollectionEquality().hash(_items));

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShoppingListModelImplCopyWith<_$ShoppingListModelImpl> get copyWith =>
      __$$ShoppingListModelImplCopyWithImpl<_$ShoppingListModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShoppingListModelImplToJson(
      this,
    );
  }
}

abstract class _ShoppingListModel implements ShoppingListModel {
  const factory _ShoppingListModel(
      {required final String id,
      required final String name,
      final String? description,
      final int itemCount,
      final double totalPrice,
      final bool isShared,
      final DateTime? createdAt,
      final DateTime? updatedAt,
      required final String createdBy,
      final List<String> sharedWith,
      final List<ShoppingListItemModel> items}) = _$ShoppingListModelImpl;

  factory _ShoppingListModel.fromJson(Map<String, dynamic> json) =
      _$ShoppingListModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  int get itemCount;
  @override
  double get totalPrice;
  @override
  bool get isShared;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;
  @override
  String get createdBy;
  @override
  List<String> get sharedWith;
  @override
  List<ShoppingListItemModel> get items;

  /// Create a copy of ShoppingListModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShoppingListModelImplCopyWith<_$ShoppingListModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ShoppingListItemModel _$ShoppingListItemModelFromJson(
    Map<String, dynamic> json) {
  return _ShoppingListItemModel.fromJson(json);
}

/// @nodoc
mixin _$ShoppingListItemModel {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  bool get isChecked => throw _privateConstructorUsedError;
  DateTime? get createdAt => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ShoppingListItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ShoppingListItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ShoppingListItemModelCopyWith<ShoppingListItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ShoppingListItemModelCopyWith<$Res> {
  factory $ShoppingListItemModelCopyWith(ShoppingListItemModel value,
          $Res Function(ShoppingListItemModel) then) =
      _$ShoppingListItemModelCopyWithImpl<$Res, ShoppingListItemModel>;
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      int quantity,
      double price,
      bool isChecked,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$ShoppingListItemModelCopyWithImpl<$Res,
        $Val extends ShoppingListItemModel>
    implements $ShoppingListItemModelCopyWith<$Res> {
  _$ShoppingListItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ShoppingListItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? quantity = null,
    Object? price = null,
    Object? isChecked = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ShoppingListItemModelImplCopyWith<$Res>
    implements $ShoppingListItemModelCopyWith<$Res> {
  factory _$$ShoppingListItemModelImplCopyWith(
          _$ShoppingListItemModelImpl value,
          $Res Function(_$ShoppingListItemModelImpl) then) =
      __$$ShoppingListItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String? description,
      int quantity,
      double price,
      bool isChecked,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$ShoppingListItemModelImplCopyWithImpl<$Res>
    extends _$ShoppingListItemModelCopyWithImpl<$Res,
        _$ShoppingListItemModelImpl>
    implements _$$ShoppingListItemModelImplCopyWith<$Res> {
  __$$ShoppingListItemModelImplCopyWithImpl(_$ShoppingListItemModelImpl _value,
      $Res Function(_$ShoppingListItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ShoppingListItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = freezed,
    Object? quantity = null,
    Object? price = null,
    Object? isChecked = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$ShoppingListItemModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      isChecked: null == isChecked
          ? _value.isChecked
          : isChecked // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ShoppingListItemModelImpl implements _ShoppingListItemModel {
  const _$ShoppingListItemModelImpl(
      {required this.id,
      required this.name,
      this.description,
      this.quantity = 1,
      this.price = 0.0,
      this.isChecked = false,
      this.createdAt,
      this.updatedAt});

  factory _$ShoppingListItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ShoppingListItemModelImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String? description;
  @override
  @JsonKey()
  final int quantity;
  @override
  @JsonKey()
  final double price;
  @override
  @JsonKey()
  final bool isChecked;
  @override
  final DateTime? createdAt;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'ShoppingListItemModel(id: $id, name: $name, description: $description, quantity: $quantity, price: $price, isChecked: $isChecked, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ShoppingListItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.isChecked, isChecked) ||
                other.isChecked == isChecked) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, description, quantity,
      price, isChecked, createdAt, updatedAt);

  /// Create a copy of ShoppingListItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ShoppingListItemModelImplCopyWith<_$ShoppingListItemModelImpl>
      get copyWith => __$$ShoppingListItemModelImplCopyWithImpl<
          _$ShoppingListItemModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ShoppingListItemModelImplToJson(
      this,
    );
  }
}

abstract class _ShoppingListItemModel implements ShoppingListItemModel {
  const factory _ShoppingListItemModel(
      {required final String id,
      required final String name,
      final String? description,
      final int quantity,
      final double price,
      final bool isChecked,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$ShoppingListItemModelImpl;

  factory _ShoppingListItemModel.fromJson(Map<String, dynamic> json) =
      _$ShoppingListItemModelImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String? get description;
  @override
  int get quantity;
  @override
  double get price;
  @override
  bool get isChecked;
  @override
  DateTime? get createdAt;
  @override
  DateTime? get updatedAt;

  /// Create a copy of ShoppingListItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ShoppingListItemModelImplCopyWith<_$ShoppingListItemModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
