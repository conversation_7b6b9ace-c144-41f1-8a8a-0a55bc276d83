# Backup API Reference

The `BackupAPI` class provides a simple API for integrating the backup system into your app.

## Methods

### Initialize

```dart
static Future<void> initialize(BuildContext context)
```

Initialize the backup system. This should be called once when your app starts.

**Parameters:**
- `context`: The build context.

**Example:**
```dart
@override
void initState() {
  super.initState();
  BackupAPI.initialize(context);
}
```

### Get Providers

```dart
static List<SingleChildWidget> getProviders()
```

Get providers for the backup system. This should be used with `MultiProvider` to provide the backup system to your app.

**Returns:**
- A list of providers for the backup system.

**Example:**
```dart
void main() {
  runApp(
    MultiProvider(
      providers: BackupAPI.getProviders(),
      child: MyApp(),
    ),
  );
}
```

### Show Backup Manager

```dart
static Future<void> showBackupManager(BuildContext context)
```

Show the backup manager screen. This screen allows users to manage backups, create new backups, restore backups, and configure backup settings.

**Parameters:**
- `context`: The build context.

**Example:**
```dart
ElevatedButton(
  onPressed: () => BackupAPI.showBackupManager(context),
  child: Text('Backup Manager'),
)
```

### Create Backup

```dart
static Future<bool> createBackup(BuildContext context)
```

Create a backup. This will create a backup of the app's data.

**Parameters:**
- `context`: The build context.

**Returns:**
- `true` if the backup was created successfully, `false` otherwise.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final success = await BackupAPI.createBackup(context);
    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Backup created successfully')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to create backup')),
      );
    }
  },
  child: Text('Create Backup'),
)
```

### Restore Backup

```dart
static Future<bool> restoreBackup(BuildContext context, BackupMetadata backup)
```

Restore a backup. This will restore the app's data from the specified backup.

**Parameters:**
- `context`: The build context.
- `backup`: The backup to restore.

**Returns:**
- `true` if the backup was restored successfully, `false` otherwise.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final backup = BackupAPI.getAvailableBackups(context)?.first;
    if (backup != null) {
      final success = await BackupAPI.restoreBackup(context, backup);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Backup restored successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to restore backup')),
        );
      }
    }
  },
  child: Text('Restore Backup'),
)
```

### Get Backup Stats

```dart
static Future<BackupStats> getBackupStats(BuildContext context)
```

Get backup statistics. This includes information like the number of backups, the total size of backups, and the success rate.

**Parameters:**
- `context`: The build context.

**Returns:**
- Backup statistics.

**Example:**
```dart
FutureBuilder<BackupStats>(
  future: BackupAPI.getBackupStats(context),
  builder: (context, snapshot) {
    if (snapshot.hasData) {
      final stats = snapshot.data!;
      return Text('Total Backups: ${stats.totalBackups}');
    } else {
      return CircularProgressIndicator();
    }
  },
)
```

### Get Backup Controller

```dart
static BackupController? getBackupController(BuildContext context)
```

Get the backup controller. This provides access to the backup controller, which can be used for more advanced operations.

**Parameters:**
- `context`: The build context.

**Returns:**
- The backup controller, or `null` if it's not available.

**Example:**
```dart
final controller = BackupAPI.getBackupController(context);
if (controller != null) {
  print('Last Backup: ${controller.lastBackupTime}');
}
```

### Update Backup Config

```dart
static Future<void> updateBackupConfig(BuildContext context, BackupConfig config)
```

Update backup configuration. This allows you to configure backup settings like automatic backup, cloud backup, and encryption.

**Parameters:**
- `context`: The build context.
- `config`: The new backup configuration.

**Example:**
```dart
final config = BackupConfig(
  enableAutoBackup: true,
  enableCloudBackup: true,
  enableLocalBackup: true,
  enableEncryption: true,
  enableCompression: true,
  enableValidation: true,
  backupFrequency: 'daily',
  retentionDays: 30,
  localStoragePath: 'backups',
);

BackupAPI.updateBackupConfig(context, config);
```

### Get Available Backups

```dart
static List<BackupMetadata>? getAvailableBackups(BuildContext context)
```

Get available backups. This returns a list of available backups.

**Parameters:**
- `context`: The build context.

**Returns:**
- A list of available backups, or `null` if they're not available.

**Example:**
```dart
final backups = BackupAPI.getAvailableBackups(context);
if (backups != null && backups.isNotEmpty) {
  print('Latest Backup: ${backups.first.formattedDate}');
}
```

### Load Available Backups

```dart
static Future<void> loadAvailableBackups(BuildContext context)
```

Load available backups. This refreshes the list of available backups.

**Parameters:**
- `context`: The build context.

**Example:**
```dart
ElevatedButton(
  onPressed: () => BackupAPI.loadAvailableBackups(context),
  child: Text('Refresh Backups'),
)
```

### Delete Backup

```dart
static Future<void> deleteBackup(BuildContext context, BackupMetadata backup)
```

Delete a backup. This deletes the specified backup.

**Parameters:**
- `context`: The build context.
- `backup`: The backup to delete.

**Example:**
```dart
ElevatedButton(
  onPressed: () {
    final backup = BackupAPI.getAvailableBackups(context)?.first;
    if (backup != null) {
      BackupAPI.deleteBackup(context, backup);
    }
  },
  child: Text('Delete Latest Backup'),
)
```

### Apply Retention Policy

```dart
static Future<int> applyRetentionPolicy(BuildContext context)
```

Apply retention policy. This deletes old backups according to the retention policy.

**Parameters:**
- `context`: The build context.

**Returns:**
- The number of backups deleted.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final deleted = await BackupAPI.applyRetentionPolicy(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Deleted $deleted old backups')),
    );
  },
  child: Text('Clean Up Backups'),
)
```

### Create Emergency Backup

```dart
static Future<String?> createEmergencyBackup(BuildContext context)
```

Create an emergency backup. This creates a backup before potentially destructive operations.

**Parameters:**
- `context`: The build context.

**Returns:**
- The path to the emergency backup, or `null` if it failed.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final backupPath = await BackupAPI.createEmergencyBackup(context);
    if (backupPath != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Emergency backup created')),
      );
    }
  },
  child: Text('Create Emergency Backup'),
)
```

### Restore From Emergency Backup

```dart
static Future<bool> restoreFromEmergencyBackup(BuildContext context, String backupPath)
```

Restore from an emergency backup. This restores the app's data from an emergency backup.

**Parameters:**
- `context`: The build context.
- `backupPath`: The path to the emergency backup.

**Returns:**
- `true` if the backup was restored successfully, `false` otherwise.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final backupPath = await BackupAPI.createEmergencyBackup(context);
    if (backupPath != null) {
      // Do something potentially destructive
      
      // If it fails, restore from the emergency backup
      final success = await BackupAPI.restoreFromEmergencyBackup(context, backupPath);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Restored from emergency backup')),
        );
      }
    }
  },
  child: Text('Risky Operation'),
)
```

### Export Backup

```dart
static Future<String?> exportBackup(BuildContext context, BackupMetadata backup)
```

Export a backup. This exports the specified backup to a file that can be shared.

**Parameters:**
- `context`: The build context.
- `backup`: The backup to export.

**Returns:**
- The path to the exported backup, or `null` if it failed.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final backup = BackupAPI.getAvailableBackups(context)?.first;
    if (backup != null) {
      final exportPath = await BackupAPI.exportBackup(context, backup);
      if (exportPath != null) {
        BackupAPI.shareBackup(context, exportPath);
      }
    }
  },
  child: Text('Export and Share Backup'),
)
```

### Import Backup

```dart
static Future<BackupMetadata?> importBackup(BuildContext context, String importPath)
```

Import a backup. This imports a backup from a file.

**Parameters:**
- `context`: The build context.
- `importPath`: The path to the backup file to import.

**Returns:**
- The imported backup, or `null` if it failed.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final importPath = await BackupAPI.pickBackupFile(context);
    if (importPath != null) {
      final backup = await BackupAPI.importBackup(context, importPath);
      if (backup != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Backup imported successfully')),
        );
      }
    }
  },
  child: Text('Import Backup'),
)
```

### Share Backup

```dart
static Future<bool> shareBackup(BuildContext context, String exportPath)
```

Share a backup. This shares the specified backup file with other apps.

**Parameters:**
- `context`: The build context.
- `exportPath`: The path to the backup file to share.

**Returns:**
- `true` if the backup was shared successfully, `false` otherwise.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final backup = BackupAPI.getAvailableBackups(context)?.first;
    if (backup != null) {
      final exportPath = await BackupAPI.exportBackup(context, backup);
      if (exportPath != null) {
        BackupAPI.shareBackup(context, exportPath);
      }
    }
  },
  child: Text('Share Backup'),
)
```

### Pick Backup File

```dart
static Future<String?> pickBackupFile(BuildContext context)
```

Pick a backup file to import. This opens a file picker to select a backup file.

**Parameters:**
- `context`: The build context.

**Returns:**
- The path to the selected backup file, or `null` if no file was selected.

**Example:**
```dart
ElevatedButton(
  onPressed: () async {
    final importPath = await BackupAPI.pickBackupFile(context);
    if (importPath != null) {
      final backup = await BackupAPI.importBackup(context, importPath);
      if (backup != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Backup imported successfully')),
        );
      }
    }
  },
  child: Text('Import Backup'),
)
```
