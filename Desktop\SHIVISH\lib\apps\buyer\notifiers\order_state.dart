import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../shared/models/order/order_model.dart';

part 'order_state.freezed.dart';

/// Order state
@freezed
class OrderState with _$OrderState {
  /// Initial state
  const factory OrderState.initial() = _Initial;

  /// Loading state
  const factory OrderState.loading() = _Loading;

  /// Loaded state
  const factory OrderState.loaded(OrderModel order) = _Loaded;

  /// Error state
  const factory OrderState.error(String message) = _Error;
}

/// Orders state
@freezed
class OrdersState with _$OrdersState {
  /// Initial state
  const factory OrdersState.initial() = _OrdersInitial;

  /// Loading state
  const factory OrdersState.loading() = _OrdersLoading;

  /// Loaded state
  const factory OrdersState.loaded(List<OrderModel> orders) = _OrdersLoaded;

  /// Error state
  const factory OrdersState.error(String message) = _OrdersError;
}
