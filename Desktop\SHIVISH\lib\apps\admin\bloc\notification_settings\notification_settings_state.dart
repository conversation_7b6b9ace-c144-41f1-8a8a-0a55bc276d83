import 'package:freezed_annotation/freezed_annotation.dart';

part 'notification_settings_state.freezed.dart';

@freezed
class NotificationSettingsState with _$NotificationSettingsState {
  const factory NotificationSettingsState.loading() = _Loading;
  const factory NotificationSettingsState.error(String message) = _Error;
  const factory NotificationSettingsState.loaded(
      List<NotificationSetting> settings) = _Loaded;
}

class NotificationSetting {
  final String id;
  final String title;
  final bool isEnabled;
  final String? description;
  final String? icon;

  const NotificationSetting({
    required this.id,
    required this.title,
    required this.isEnabled,
    this.description,
    this.icon,
  });
}
