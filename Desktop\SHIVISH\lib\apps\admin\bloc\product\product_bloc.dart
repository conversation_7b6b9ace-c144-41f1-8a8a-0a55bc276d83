import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/product/product_event.dart';
import 'package:shivish/apps/admin/bloc/product/product_state.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/services/product/product_service.dart';

@injectable
class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductService _productService;
  List<ProductModel> _products = [];
  Map<String, dynamic> _filters = {};
  static const int _pageSize = 20;

  Map<String, dynamic> get filters => _filters;

  ProductBloc(this._productService) : super(const ProductState.initial()) {
    on<ProductEvent>((event, emit) async {
      await event.when(
        loadProducts: () => _onLoadProducts(emit),
        loadMoreProducts: () => _onLoadMoreProducts(emit),
        updateProductStatus: (product, status, notes, isActive) =>
            _onUpdateProductStatus(
          emit,
          product,
          status,
          notes,
          isActive,
        ),
        deleteProduct: (id) => _onDeleteProduct(emit, id),
        applyFilters: (filters) => _onApplyFilters(emit, filters),
      );
    });
  }

  Future<void> _onLoadProducts(Emitter<ProductState> emit) async {
    try {
      emit(const ProductState.loading());
      _products = await _productService.getProducts().first;
      emit(ProductState.loaded(_products));
    } catch (e) {
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onLoadMoreProducts(Emitter<ProductState> emit) async {
    try {
      if (_products.isEmpty) return;
      emit(ProductState.loadingMore(_products));

      final lastProduct = _products.last;
      final lastDoc = await _productService.getProductDocument(lastProduct.id);
      final moreProducts = await _productService
          .getProducts(
            limit: _pageSize,
            startAfter: lastDoc,
            filters: _filters,
          )
          .first;

      if (moreProducts.isEmpty) {
        emit(ProductState.loaded(_products));
        return;
      }

      _products = [..._products, ...moreProducts];
      emit(ProductState.loaded(_products));
    } catch (e) {
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onUpdateProductStatus(
    Emitter<ProductState> emit,
    ProductModel product,
    String status,
    String? notes,
    bool isActive,
  ) async {
    try {
      final updatedProduct = product.copyWith(
        productStatus: ProductStatus.values.firstWhere(
          (e) => e.toString() == status,
        ),
        isApproved: isActive,
        verificationNotes: notes,
        updatedAt: DateTime.now(),
      );

      await _productService.createOrUpdateProduct(updatedProduct);

      _products = _products.map((p) {
        return p.id == product.id ? updatedProduct : p;
      }).toList();

      emit(ProductState.loaded(_products));
    } catch (e) {
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onDeleteProduct(
    Emitter<ProductState> emit,
    String id,
  ) async {
    try {
      await _productService.deleteProduct(id);

      _products = _products.where((p) => p.id != id).toList();

      emit(ProductState.loaded(_products));
    } catch (e) {
      emit(ProductState.error(e.toString()));
    }
  }

  Future<void> _onApplyFilters(
    Emitter<ProductState> emit,
    Map<String, dynamic> filters,
  ) async {
    try {
      _filters = filters;
      emit(const ProductState.loading());

      _products = await _productService
          .getProducts(
            limit: _pageSize,
            filters: _filters,
          )
          .first;

      emit(ProductState.loaded(_products));
    } catch (e) {
      emit(ProductState.error(e.toString()));
    }
  }
}
