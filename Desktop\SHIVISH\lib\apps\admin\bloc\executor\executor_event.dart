part of 'executor_bloc.dart';

@freezed
class ExecutorEvent with _$ExecutorEvent {
  const factory ExecutorEvent.loadExecutors() = LoadExecutors;
  const factory ExecutorEvent.loadMoreExecutors() = LoadMoreExecutors;
  const factory ExecutorEvent.createExecutor({
    required Map<String, dynamic> data,
  }) = CreateExecutor;
  const factory ExecutorEvent.updateExecutorStatus({
    required String id,
    required String status,
  }) = UpdateExecutorStatus;
  const factory ExecutorEvent.updateExecutorPerformance({
    required String id,
    required Map<String, dynamic> metrics,
  }) = UpdateExecutorPerformance;
  const factory ExecutorEvent.deleteExecutor({
    required String id,
  }) = DeleteExecutor;
  const factory ExecutorEvent.startRealtimeUpdates() = StartRealtimeUpdates;
  const factory ExecutorEvent.stopRealtimeUpdates() = StopRealtimeUpdates;
}
