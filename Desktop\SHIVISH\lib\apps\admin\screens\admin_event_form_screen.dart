import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../shared/models/event/event_model.dart';
import '../../../shared/models/product/product_model.dart';
import '../../../shared/services/auth/auth_service.dart';
import '../bloc/event_management/admin_event_management_bloc.dart';
import '../bloc/event_management/admin_event_management_event.dart';
import '../bloc/event_management/admin_event_management_state.dart';

class AdminEventFormScreen extends StatefulWidget {
  final EventModel? event;

  const AdminEventFormScreen({
    super.key,
    this.event,
  });

  @override
  State<AdminEventFormScreen> createState() => _AdminEventFormScreenState();
}

// Define an enum for the creation mode
enum CreationMode {
  event,
  productList,
}

class _AdminEventFormScreenState extends State<AdminEventFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _locationController;
  late EventType _selectedType;
  late DateTime _startDate;
  late DateTime _endDate;
  late bool _isPublic;
  String? _currentUserId;

  // Mode selection
  CreationMode _creationMode = CreationMode.event;

  // Product list related variables
  final List<EventProduct> _selectedProducts = [];
  List<ProductModel> _availableProducts = [];
  bool _isLoadingProducts = false;
  String? _productErrorMessage;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.event?.title ?? '');
    _descriptionController =
        TextEditingController(text: widget.event?.description ?? '');
    _locationController = TextEditingController(
        text: widget.event?.whatsappMessage?.split('at ').lastOrNull ?? '');
    _selectedType = widget.event?.type ?? EventType.festival;
    _startDate = widget.event?.startDate ?? DateTime.now();
    _endDate =
        widget.event?.endDate ?? DateTime.now().add(const Duration(days: 1));
    _isPublic = widget.event?.isPublic ?? true;

    // Determine creation mode based on existing event
    if (widget.event != null) {
      if (widget.event!.products != null && widget.event!.products!.isNotEmpty) {
        _creationMode = CreationMode.productList;
        _selectedProducts.addAll(widget.event!.products!);
      } else {
        _creationMode = CreationMode.event;
      }
    }

    _loadCurrentUserId();
    _loadAvailableProducts();
  }

  Future<void> _loadAvailableProducts() async {
    setState(() {
      _isLoadingProducts = true;
      _productErrorMessage = null;
    });

    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('products')
          .where('status', isEqualTo: 'approved')
          .where('isDeleted', isEqualTo: false)
          .get();

      final products = snapshot.docs.map((doc) {
        final data = doc.data();
        return ProductModel.fromJson({
          'id': doc.id,
          ...data,
        });
      }).toList();

      setState(() {
        _availableProducts = products;
        _isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        _productErrorMessage = 'Error loading products: $e';
        _isLoadingProducts = false;
      });
    }
  }

  Future<void> _loadCurrentUserId() async {
    final authService = context.read<AuthService>();
    final user = await authService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUserId = user.id;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.event == null ? 'Create Event' : 'Edit Event'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // If we can't pop, use GoRouter to navigate back to home
              context.go('/admin/home');
            }
          },
        ),
      ),
      body: BlocConsumer<AdminEventManagementBloc, AdminEventManagementState>(
        listener: (context, state) {
          switch (state) {
            case AdminEventManagementState eventCreated
                when state.toString().contains('eventCreated'):
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(eventCreated
                        .toString()
                        .split('message: ')[1]
                        .replaceAll(')', ''))),
              );
              context.pop();
              break;
            case AdminEventManagementState eventUpdated
                when state.toString().contains('eventUpdated'):
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text(eventUpdated
                        .toString()
                        .split('message: ')[1]
                        .replaceAll(')', ''))),
              );
              context.pop();
              break;
            case AdminEventManagementState error
                when state.toString().contains('error'):
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(error
                      .toString()
                      .split('message: ')[1]
                      .replaceAll(')', '')),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
              break;
            default:
              break;
          }
        },
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Mode selection
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Creation Mode',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        SegmentedButton<CreationMode>(
                          segments: const [
                            ButtonSegment<CreationMode>(
                              value: CreationMode.event,
                              label: Text('Festival Event'),
                              icon: Icon(Icons.event),
                            ),
                            ButtonSegment<CreationMode>(
                              value: CreationMode.productList,
                              label: Text('Product List'),
                              icon: Icon(Icons.list),
                            ),
                          ],
                          selected: {_creationMode},
                          onSelectionChanged: (Set<CreationMode> selection) {
                            final newMode = selection.first;

                            // If switching from product list to event, clear products
                            if (_creationMode == CreationMode.productList &&
                                newMode == CreationMode.event &&
                                _selectedProducts.isNotEmpty) {
                              setState(() {
                                _selectedProducts.clear();
                              });
                            }

                            setState(() {
                              _creationMode = newMode;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Conditional UI based on mode
                if (_creationMode == CreationMode.event) ...[
                  // Event fields
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Festival Title',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a title';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a description';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _locationController,
                    decoration: const InputDecoration(
                      labelText: 'Location',
                      border: OutlineInputBorder(),
                      hintText: 'Enter city, state or specific location',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a location';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<EventType>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Event Type',
                      border: OutlineInputBorder(),
                    ),
                    items: [EventType.festival].map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.name),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedType = value;
                        });
                      }
                    },
                  ),
                ],

                const SizedBox(height: 16),

                // Date selection - shown for both modes
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Date Selection',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        ListTile(
                          title: const Text('Start Date'),
                          subtitle: Text(_startDate.toString().split('.')[0]),
                          trailing: const Icon(Icons.calendar_today),
                          onTap: () {
                            _selectDateTime(
                              initialDate: _startDate,
                              firstDate: DateTime.now(),
                              onSelected: (selectedDate) {
                                setState(() {
                                  _startDate = selectedDate;
                                });
                              },
                            );
                          },
                        ),
                        ListTile(
                          title: const Text('End Date'),
                          subtitle: Text(_endDate.toString().split('.')[0]),
                          trailing: const Icon(Icons.calendar_today),
                          onTap: () {
                            _selectDateTime(
                              initialDate: _endDate,
                              firstDate: _startDate,
                              onSelected: (selectedDate) {
                                setState(() {
                                  _endDate = selectedDate;
                                });
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Product list section (shown only for Product List mode)
                if (_creationMode == CreationMode.productList) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Featured Products',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 16),
                          _buildProductsList(),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            onPressed: _showProductSelectionDialog,
                            icon: const Icon(Icons.add),
                            label: const Text('Add Products'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                SwitchListTile(
                  title: const Text('Public Event'),
                  value: _isPublic,
                  onChanged: (value) {
                    setState(() {
                      _isPublic = value;
                    });
                  },
                ),

                const SizedBox(height: 24),

                ElevatedButton(
                  onPressed: _submitForm,
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: Text(_creationMode == CreationMode.event
                    ? (widget.event == null ? 'Create Festival Event' : 'Update Festival Event')
                    : (widget.event == null ? 'Create Product List' : 'Update Product List')),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductsList() {
    if (_selectedProducts.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No products selected. Click "Add Products" to select products.'),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _selectedProducts.length,
      itemBuilder: (context, index) {
        final product = _selectedProducts[index];
        return ListTile(
          leading: product.imageUrl != null
              ? Image.network(
                  product.imageUrl!,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => const Icon(Icons.image_not_supported),
                )
              : const Icon(Icons.inventory),
          title: Text(product.name),
          subtitle: Text('₹${product.price} × ${product.quantity}'),
          trailing: IconButton(
            icon: const Icon(Icons.delete, color: Colors.red),
            onPressed: () {
              setState(() {
                _selectedProducts.removeAt(index);
              });
            },
          ),
        );
      },
    );
  }

  void _showProductSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            if (_isLoadingProducts) {
              return const AlertDialog(
                content: Center(child: CircularProgressIndicator()),
              );
            }

            if (_productErrorMessage != null) {
              return AlertDialog(
                title: const Text('Error'),
                content: Text(_productErrorMessage!),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _loadAvailableProducts();
                      _showProductSelectionDialog();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              );
            }

            if (_availableProducts.isEmpty) {
              return AlertDialog(
                title: const Text('No Products Available'),
                content: const Text('There are no approved products available to add.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Close'),
                  ),
                ],
              );
            }

            // Create a temporary list for selection
            List<EventProduct> tempSelectedProducts = List.from(_selectedProducts);

            return AlertDialog(
              title: const Text('Select Products'),
              content: SizedBox(
                width: double.maxFinite,
                height: 400,
                child: ListView.builder(
                  itemCount: _availableProducts.length,
                  itemBuilder: (context, index) {
                    final product = _availableProducts[index];
                    final isSelected = tempSelectedProducts.any((p) => p.productId == product.id);

                    return CheckboxListTile(
                      title: Text(product.name),
                      subtitle: Text('₹${product.price}'),
                      secondary: product.images.isNotEmpty
                          ? Image.network(
                              product.images.first,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => const Icon(Icons.image_not_supported),
                            )
                          : const Icon(Icons.inventory),
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            // Add product if not already in the list
                            if (!tempSelectedProducts.any((p) => p.productId == product.id)) {
                              tempSelectedProducts.add(EventProduct(
                                productId: product.id,
                                name: product.name,
                                price: product.price,
                                quantity: 1,
                                imageUrl: product.images.isNotEmpty ? product.images.first : null,
                                description: product.description,
                              ));
                            }
                          } else {
                            // Remove product
                            tempSelectedProducts.removeWhere((p) => p.productId == product.id);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Update the actual selected products list
                    this.setState(() {
                      _selectedProducts.clear();
                      _selectedProducts.addAll(tempSelectedProducts);
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Add Selected Products'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Helper method to select date and time with proper context handling
  Future<void> _selectDateTime({
    required DateTime initialDate,
    required DateTime firstDate,
    required Function(DateTime) onSelected,
  }) async {
    // Show date picker
    final date = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    // Check if widget is still mounted and we have a date
    if (date != null && mounted) {
      // Get time without using context directly in an async gap
      TimeOfDay? time;

      // Use a separate method to show time picker to avoid context issues
      await _showTimePicker(initialDate).then((selectedTime) {
        time = selectedTime;
      });

      // Check if widget is still mounted
      if (mounted) {
        final DateTime selectedDateTime;

        if (time != null) {
          // Combine date and time
          selectedDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time!.hour,
            time!.minute,
          );
        } else {
          // Use just the date
          selectedDateTime = date;
        }

        // Call the callback with the selected date/time
        onSelected(selectedDateTime);
      }
    }
  }

  // Helper method to show time picker
  Future<TimeOfDay?> _showTimePicker(DateTime initialDate) async {
    return showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(initialDate),
    );
  }

  void _submitForm() {
    // For product list mode, we only validate if products are selected
    if (_creationMode == CreationMode.productList) {
      if (_selectedProducts.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please add at least one product to the list')),
        );
        return;
      }
    } else {
      // For event mode, validate the form
      if (!(_formKey.currentState?.validate() ?? false)) {
        return;
      }
    }

    if (_currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please wait while we load your user information'),
        ),
      );
      return;
    }

    try {
      // Create the event model based on the selected mode
      final event = EventModel(
        id: widget.event?.id ?? '',
        // For product list, use a generic title if not provided
        title: _creationMode == CreationMode.event
            ? _titleController.text
            : 'Product List for ${_startDate.toString().split(' ')[0]} to ${_endDate.toString().split(' ')[0]}',
        // For product list, use a generic description
        description: _creationMode == CreationMode.event
            ? _descriptionController.text
            : 'Product list created by admin for display in buyer calendars',
        // Always use festival type
        type: EventType.festival,
        status: widget.event?.status ?? EventStatus.pending,
        startDate: _startDate,
        endDate: _endDate,
        createdBy: widget.event?.createdBy ?? _currentUserId!,
        approvedBy: widget.event?.approvedBy,
        rejectedBy: widget.event?.rejectedBy,
        rejectionReason: widget.event?.rejectionReason,
        isPublic: _isPublic,
        isRecurring: widget.event?.isRecurring ?? false,
        recurrencePattern: widget.event?.recurrencePattern,
        reminderBefore: widget.event?.reminderBefore,
        // Add location for events, generic message for product lists
        whatsappMessage: _creationMode == CreationMode.event
            ? 'New festival event: ${_titleController.text} at ${_locationController.text}'
            : 'New product list available from ${_startDate.toString().split(' ')[0]}',
        whatsappContacts: widget.event?.whatsappContacts ?? [],
        mediaUrls: widget.event?.mediaUrls ?? [],
        // Only include products for product list mode
        products: _creationMode == CreationMode.productList ? _selectedProducts : [],
        createdAt: widget.event?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        isDeleted: widget.event?.isDeleted ?? false,
      );

      if (widget.event == null) {
        context.read<AdminEventManagementBloc>().add(
              AdminEventManagementEvent.createEvent(event: event),
            );
      } else {
        context.read<AdminEventManagementBloc>().add(
              AdminEventManagementEvent.updateEvent(event: event),
            );
      }

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_creationMode == CreationMode.event
              ? 'Festival event ${widget.event == null ? 'created' : 'updated'} successfully!'
              : 'Product list ${widget.event == null ? 'created' : 'updated'} successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error ${widget.event == null ? 'creating' : 'updating'} ${_creationMode == CreationMode.event ? 'event' : 'product list'}: $e'),
          backgroundColor: Colors.red,
        ),
      );
      debugPrint('Error in _submitForm: $e');
    }
  }
}
