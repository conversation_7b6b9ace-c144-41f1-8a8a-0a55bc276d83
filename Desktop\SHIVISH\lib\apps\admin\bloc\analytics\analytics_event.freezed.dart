// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'analytics_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AnalyticsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String timeRange) loadData,
    required TResult Function(String timeRange) updateTimeRange,
    required TResult Function() refreshData,
    required TResult Function(AnalyticsFilterData filters) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String timeRange)? loadData,
    TResult? Function(String timeRange)? updateTimeRange,
    TResult? Function()? refreshData,
    TResult? Function(AnalyticsFilterData filters)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String timeRange)? loadData,
    TResult Function(String timeRange)? updateTimeRange,
    TResult Function()? refreshData,
    TResult Function(AnalyticsFilterData filters)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalyticsData value) loadData,
    required TResult Function(UpdateTimeRange value) updateTimeRange,
    required TResult Function(RefreshAnalyticsData value) refreshData,
    required TResult Function(ApplyFilters value) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalyticsData value)? loadData,
    TResult? Function(UpdateTimeRange value)? updateTimeRange,
    TResult? Function(RefreshAnalyticsData value)? refreshData,
    TResult? Function(ApplyFilters value)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalyticsData value)? loadData,
    TResult Function(UpdateTimeRange value)? updateTimeRange,
    TResult Function(RefreshAnalyticsData value)? refreshData,
    TResult Function(ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AnalyticsEventCopyWith<$Res> {
  factory $AnalyticsEventCopyWith(
          AnalyticsEvent value, $Res Function(AnalyticsEvent) then) =
      _$AnalyticsEventCopyWithImpl<$Res, AnalyticsEvent>;
}

/// @nodoc
class _$AnalyticsEventCopyWithImpl<$Res, $Val extends AnalyticsEvent>
    implements $AnalyticsEventCopyWith<$Res> {
  _$AnalyticsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadAnalyticsDataImplCopyWith<$Res> {
  factory _$$LoadAnalyticsDataImplCopyWith(_$LoadAnalyticsDataImpl value,
          $Res Function(_$LoadAnalyticsDataImpl) then) =
      __$$LoadAnalyticsDataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String timeRange});
}

/// @nodoc
class __$$LoadAnalyticsDataImplCopyWithImpl<$Res>
    extends _$AnalyticsEventCopyWithImpl<$Res, _$LoadAnalyticsDataImpl>
    implements _$$LoadAnalyticsDataImplCopyWith<$Res> {
  __$$LoadAnalyticsDataImplCopyWithImpl(_$LoadAnalyticsDataImpl _value,
      $Res Function(_$LoadAnalyticsDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timeRange = null,
  }) {
    return _then(_$LoadAnalyticsDataImpl(
      timeRange: null == timeRange
          ? _value.timeRange
          : timeRange // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$LoadAnalyticsDataImpl implements LoadAnalyticsData {
  const _$LoadAnalyticsDataImpl({required this.timeRange});

  @override
  final String timeRange;

  @override
  String toString() {
    return 'AnalyticsEvent.loadData(timeRange: $timeRange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadAnalyticsDataImpl &&
            (identical(other.timeRange, timeRange) ||
                other.timeRange == timeRange));
  }

  @override
  int get hashCode => Object.hash(runtimeType, timeRange);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadAnalyticsDataImplCopyWith<_$LoadAnalyticsDataImpl> get copyWith =>
      __$$LoadAnalyticsDataImplCopyWithImpl<_$LoadAnalyticsDataImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String timeRange) loadData,
    required TResult Function(String timeRange) updateTimeRange,
    required TResult Function() refreshData,
    required TResult Function(AnalyticsFilterData filters) applyFilters,
  }) {
    return loadData(timeRange);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String timeRange)? loadData,
    TResult? Function(String timeRange)? updateTimeRange,
    TResult? Function()? refreshData,
    TResult? Function(AnalyticsFilterData filters)? applyFilters,
  }) {
    return loadData?.call(timeRange);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String timeRange)? loadData,
    TResult Function(String timeRange)? updateTimeRange,
    TResult Function()? refreshData,
    TResult Function(AnalyticsFilterData filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadData != null) {
      return loadData(timeRange);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalyticsData value) loadData,
    required TResult Function(UpdateTimeRange value) updateTimeRange,
    required TResult Function(RefreshAnalyticsData value) refreshData,
    required TResult Function(ApplyFilters value) applyFilters,
  }) {
    return loadData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalyticsData value)? loadData,
    TResult? Function(UpdateTimeRange value)? updateTimeRange,
    TResult? Function(RefreshAnalyticsData value)? refreshData,
    TResult? Function(ApplyFilters value)? applyFilters,
  }) {
    return loadData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalyticsData value)? loadData,
    TResult Function(UpdateTimeRange value)? updateTimeRange,
    TResult Function(RefreshAnalyticsData value)? refreshData,
    TResult Function(ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadData != null) {
      return loadData(this);
    }
    return orElse();
  }
}

abstract class LoadAnalyticsData implements AnalyticsEvent {
  const factory LoadAnalyticsData({required final String timeRange}) =
      _$LoadAnalyticsDataImpl;

  String get timeRange;

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadAnalyticsDataImplCopyWith<_$LoadAnalyticsDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateTimeRangeImplCopyWith<$Res> {
  factory _$$UpdateTimeRangeImplCopyWith(_$UpdateTimeRangeImpl value,
          $Res Function(_$UpdateTimeRangeImpl) then) =
      __$$UpdateTimeRangeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String timeRange});
}

/// @nodoc
class __$$UpdateTimeRangeImplCopyWithImpl<$Res>
    extends _$AnalyticsEventCopyWithImpl<$Res, _$UpdateTimeRangeImpl>
    implements _$$UpdateTimeRangeImplCopyWith<$Res> {
  __$$UpdateTimeRangeImplCopyWithImpl(
      _$UpdateTimeRangeImpl _value, $Res Function(_$UpdateTimeRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timeRange = null,
  }) {
    return _then(_$UpdateTimeRangeImpl(
      timeRange: null == timeRange
          ? _value.timeRange
          : timeRange // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdateTimeRangeImpl implements UpdateTimeRange {
  const _$UpdateTimeRangeImpl({required this.timeRange});

  @override
  final String timeRange;

  @override
  String toString() {
    return 'AnalyticsEvent.updateTimeRange(timeRange: $timeRange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTimeRangeImpl &&
            (identical(other.timeRange, timeRange) ||
                other.timeRange == timeRange));
  }

  @override
  int get hashCode => Object.hash(runtimeType, timeRange);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTimeRangeImplCopyWith<_$UpdateTimeRangeImpl> get copyWith =>
      __$$UpdateTimeRangeImplCopyWithImpl<_$UpdateTimeRangeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String timeRange) loadData,
    required TResult Function(String timeRange) updateTimeRange,
    required TResult Function() refreshData,
    required TResult Function(AnalyticsFilterData filters) applyFilters,
  }) {
    return updateTimeRange(timeRange);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String timeRange)? loadData,
    TResult? Function(String timeRange)? updateTimeRange,
    TResult? Function()? refreshData,
    TResult? Function(AnalyticsFilterData filters)? applyFilters,
  }) {
    return updateTimeRange?.call(timeRange);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String timeRange)? loadData,
    TResult Function(String timeRange)? updateTimeRange,
    TResult Function()? refreshData,
    TResult Function(AnalyticsFilterData filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateTimeRange != null) {
      return updateTimeRange(timeRange);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalyticsData value) loadData,
    required TResult Function(UpdateTimeRange value) updateTimeRange,
    required TResult Function(RefreshAnalyticsData value) refreshData,
    required TResult Function(ApplyFilters value) applyFilters,
  }) {
    return updateTimeRange(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalyticsData value)? loadData,
    TResult? Function(UpdateTimeRange value)? updateTimeRange,
    TResult? Function(RefreshAnalyticsData value)? refreshData,
    TResult? Function(ApplyFilters value)? applyFilters,
  }) {
    return updateTimeRange?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalyticsData value)? loadData,
    TResult Function(UpdateTimeRange value)? updateTimeRange,
    TResult Function(RefreshAnalyticsData value)? refreshData,
    TResult Function(ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateTimeRange != null) {
      return updateTimeRange(this);
    }
    return orElse();
  }
}

abstract class UpdateTimeRange implements AnalyticsEvent {
  const factory UpdateTimeRange({required final String timeRange}) =
      _$UpdateTimeRangeImpl;

  String get timeRange;

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTimeRangeImplCopyWith<_$UpdateTimeRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RefreshAnalyticsDataImplCopyWith<$Res> {
  factory _$$RefreshAnalyticsDataImplCopyWith(_$RefreshAnalyticsDataImpl value,
          $Res Function(_$RefreshAnalyticsDataImpl) then) =
      __$$RefreshAnalyticsDataImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshAnalyticsDataImplCopyWithImpl<$Res>
    extends _$AnalyticsEventCopyWithImpl<$Res, _$RefreshAnalyticsDataImpl>
    implements _$$RefreshAnalyticsDataImplCopyWith<$Res> {
  __$$RefreshAnalyticsDataImplCopyWithImpl(_$RefreshAnalyticsDataImpl _value,
      $Res Function(_$RefreshAnalyticsDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshAnalyticsDataImpl implements RefreshAnalyticsData {
  const _$RefreshAnalyticsDataImpl();

  @override
  String toString() {
    return 'AnalyticsEvent.refreshData()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RefreshAnalyticsDataImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String timeRange) loadData,
    required TResult Function(String timeRange) updateTimeRange,
    required TResult Function() refreshData,
    required TResult Function(AnalyticsFilterData filters) applyFilters,
  }) {
    return refreshData();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String timeRange)? loadData,
    TResult? Function(String timeRange)? updateTimeRange,
    TResult? Function()? refreshData,
    TResult? Function(AnalyticsFilterData filters)? applyFilters,
  }) {
    return refreshData?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String timeRange)? loadData,
    TResult Function(String timeRange)? updateTimeRange,
    TResult Function()? refreshData,
    TResult Function(AnalyticsFilterData filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (refreshData != null) {
      return refreshData();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalyticsData value) loadData,
    required TResult Function(UpdateTimeRange value) updateTimeRange,
    required TResult Function(RefreshAnalyticsData value) refreshData,
    required TResult Function(ApplyFilters value) applyFilters,
  }) {
    return refreshData(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalyticsData value)? loadData,
    TResult? Function(UpdateTimeRange value)? updateTimeRange,
    TResult? Function(RefreshAnalyticsData value)? refreshData,
    TResult? Function(ApplyFilters value)? applyFilters,
  }) {
    return refreshData?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalyticsData value)? loadData,
    TResult Function(UpdateTimeRange value)? updateTimeRange,
    TResult Function(RefreshAnalyticsData value)? refreshData,
    TResult Function(ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (refreshData != null) {
      return refreshData(this);
    }
    return orElse();
  }
}

abstract class RefreshAnalyticsData implements AnalyticsEvent {
  const factory RefreshAnalyticsData() = _$RefreshAnalyticsDataImpl;
}

/// @nodoc
abstract class _$$ApplyFiltersImplCopyWith<$Res> {
  factory _$$ApplyFiltersImplCopyWith(
          _$ApplyFiltersImpl value, $Res Function(_$ApplyFiltersImpl) then) =
      __$$ApplyFiltersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({AnalyticsFilterData filters});
}

/// @nodoc
class __$$ApplyFiltersImplCopyWithImpl<$Res>
    extends _$AnalyticsEventCopyWithImpl<$Res, _$ApplyFiltersImpl>
    implements _$$ApplyFiltersImplCopyWith<$Res> {
  __$$ApplyFiltersImplCopyWithImpl(
      _$ApplyFiltersImpl _value, $Res Function(_$ApplyFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_$ApplyFiltersImpl(
      null == filters
          ? _value.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as AnalyticsFilterData,
    ));
  }
}

/// @nodoc

class _$ApplyFiltersImpl implements ApplyFilters {
  const _$ApplyFiltersImpl(this.filters);

  @override
  final AnalyticsFilterData filters;

  @override
  String toString() {
    return 'AnalyticsEvent.applyFilters(filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplyFiltersImpl &&
            (identical(other.filters, filters) || other.filters == filters));
  }

  @override
  int get hashCode => Object.hash(runtimeType, filters);

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      __$$ApplyFiltersImplCopyWithImpl<_$ApplyFiltersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String timeRange) loadData,
    required TResult Function(String timeRange) updateTimeRange,
    required TResult Function() refreshData,
    required TResult Function(AnalyticsFilterData filters) applyFilters,
  }) {
    return applyFilters(filters);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String timeRange)? loadData,
    TResult? Function(String timeRange)? updateTimeRange,
    TResult? Function()? refreshData,
    TResult? Function(AnalyticsFilterData filters)? applyFilters,
  }) {
    return applyFilters?.call(filters);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String timeRange)? loadData,
    TResult Function(String timeRange)? updateTimeRange,
    TResult Function()? refreshData,
    TResult Function(AnalyticsFilterData filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(filters);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalyticsData value) loadData,
    required TResult Function(UpdateTimeRange value) updateTimeRange,
    required TResult Function(RefreshAnalyticsData value) refreshData,
    required TResult Function(ApplyFilters value) applyFilters,
  }) {
    return applyFilters(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalyticsData value)? loadData,
    TResult? Function(UpdateTimeRange value)? updateTimeRange,
    TResult? Function(RefreshAnalyticsData value)? refreshData,
    TResult? Function(ApplyFilters value)? applyFilters,
  }) {
    return applyFilters?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalyticsData value)? loadData,
    TResult Function(UpdateTimeRange value)? updateTimeRange,
    TResult Function(RefreshAnalyticsData value)? refreshData,
    TResult Function(ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(this);
    }
    return orElse();
  }
}

abstract class ApplyFilters implements AnalyticsEvent {
  const factory ApplyFilters(final AnalyticsFilterData filters) =
      _$ApplyFiltersImpl;

  AnalyticsFilterData get filters;

  /// Create a copy of AnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
