// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_media_management_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AdminMediaManagementState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminMediaManagementStateCopyWith<$Res> {
  factory $AdminMediaManagementStateCopyWith(AdminMediaManagementState value,
          $Res Function(AdminMediaManagementState) then) =
      _$AdminMediaManagementStateCopyWithImpl<$Res, AdminMediaManagementState>;
}

/// @nodoc
class _$AdminMediaManagementStateCopyWithImpl<$Res,
        $Val extends AdminMediaManagementState>
    implements $AdminMediaManagementStateCopyWith<$Res> {
  _$AdminMediaManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'AdminMediaManagementState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AdminMediaManagementState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'AdminMediaManagementState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements AdminMediaManagementState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminMediaManagementState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements AdminMediaManagementState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedPendingMediaImplCopyWith<$Res> {
  factory _$$LoadedPendingMediaImplCopyWith(_$LoadedPendingMediaImpl value,
          $Res Function(_$LoadedPendingMediaImpl) then) =
      __$$LoadedPendingMediaImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<MediaModel> mediaList});
}

/// @nodoc
class __$$LoadedPendingMediaImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res,
        _$LoadedPendingMediaImpl>
    implements _$$LoadedPendingMediaImplCopyWith<$Res> {
  __$$LoadedPendingMediaImplCopyWithImpl(_$LoadedPendingMediaImpl _value,
      $Res Function(_$LoadedPendingMediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaList = null,
  }) {
    return _then(_$LoadedPendingMediaImpl(
      null == mediaList
          ? _value._mediaList
          : mediaList // ignore: cast_nullable_to_non_nullable
              as List<MediaModel>,
    ));
  }
}

/// @nodoc

class _$LoadedPendingMediaImpl implements _LoadedPendingMedia {
  const _$LoadedPendingMediaImpl(final List<MediaModel> mediaList)
      : _mediaList = mediaList;

  final List<MediaModel> _mediaList;
  @override
  List<MediaModel> get mediaList {
    if (_mediaList is EqualUnmodifiableListView) return _mediaList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mediaList);
  }

  @override
  String toString() {
    return 'AdminMediaManagementState.loadedPendingMedia(mediaList: $mediaList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedPendingMediaImpl &&
            const DeepCollectionEquality()
                .equals(other._mediaList, _mediaList));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_mediaList));

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedPendingMediaImplCopyWith<_$LoadedPendingMediaImpl> get copyWith =>
      __$$LoadedPendingMediaImplCopyWithImpl<_$LoadedPendingMediaImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return loadedPendingMedia(mediaList);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return loadedPendingMedia?.call(mediaList);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (loadedPendingMedia != null) {
      return loadedPendingMedia(mediaList);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return loadedPendingMedia(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return loadedPendingMedia?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (loadedPendingMedia != null) {
      return loadedPendingMedia(this);
    }
    return orElse();
  }
}

abstract class _LoadedPendingMedia implements AdminMediaManagementState {
  const factory _LoadedPendingMedia(final List<MediaModel> mediaList) =
      _$LoadedPendingMediaImpl;

  List<MediaModel> get mediaList;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedPendingMediaImplCopyWith<_$LoadedPendingMediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MediaApprovedImplCopyWith<$Res> {
  factory _$$MediaApprovedImplCopyWith(
          _$MediaApprovedImpl value, $Res Function(_$MediaApprovedImpl) then) =
      __$$MediaApprovedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$MediaApprovedImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$MediaApprovedImpl>
    implements _$$MediaApprovedImplCopyWith<$Res> {
  __$$MediaApprovedImplCopyWithImpl(
      _$MediaApprovedImpl _value, $Res Function(_$MediaApprovedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$MediaApprovedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MediaApprovedImpl implements _MediaApproved {
  const _$MediaApprovedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminMediaManagementState.mediaApproved(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaApprovedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaApprovedImplCopyWith<_$MediaApprovedImpl> get copyWith =>
      __$$MediaApprovedImplCopyWithImpl<_$MediaApprovedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return mediaApproved(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return mediaApproved?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaApproved != null) {
      return mediaApproved(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return mediaApproved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return mediaApproved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaApproved != null) {
      return mediaApproved(this);
    }
    return orElse();
  }
}

abstract class _MediaApproved implements AdminMediaManagementState {
  const factory _MediaApproved(final String message) = _$MediaApprovedImpl;

  String get message;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaApprovedImplCopyWith<_$MediaApprovedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MediaRejectedImplCopyWith<$Res> {
  factory _$$MediaRejectedImplCopyWith(
          _$MediaRejectedImpl value, $Res Function(_$MediaRejectedImpl) then) =
      __$$MediaRejectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$MediaRejectedImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$MediaRejectedImpl>
    implements _$$MediaRejectedImplCopyWith<$Res> {
  __$$MediaRejectedImplCopyWithImpl(
      _$MediaRejectedImpl _value, $Res Function(_$MediaRejectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$MediaRejectedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MediaRejectedImpl implements _MediaRejected {
  const _$MediaRejectedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminMediaManagementState.mediaRejected(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaRejectedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaRejectedImplCopyWith<_$MediaRejectedImpl> get copyWith =>
      __$$MediaRejectedImplCopyWithImpl<_$MediaRejectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return mediaRejected(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return mediaRejected?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaRejected != null) {
      return mediaRejected(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return mediaRejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return mediaRejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaRejected != null) {
      return mediaRejected(this);
    }
    return orElse();
  }
}

abstract class _MediaRejected implements AdminMediaManagementState {
  const factory _MediaRejected(final String message) = _$MediaRejectedImpl;

  String get message;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaRejectedImplCopyWith<_$MediaRejectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MediaUploadedImplCopyWith<$Res> {
  factory _$$MediaUploadedImplCopyWith(
          _$MediaUploadedImpl value, $Res Function(_$MediaUploadedImpl) then) =
      __$$MediaUploadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$MediaUploadedImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$MediaUploadedImpl>
    implements _$$MediaUploadedImplCopyWith<$Res> {
  __$$MediaUploadedImplCopyWithImpl(
      _$MediaUploadedImpl _value, $Res Function(_$MediaUploadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$MediaUploadedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MediaUploadedImpl implements _MediaUploaded {
  const _$MediaUploadedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminMediaManagementState.mediaUploaded(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaUploadedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaUploadedImplCopyWith<_$MediaUploadedImpl> get copyWith =>
      __$$MediaUploadedImplCopyWithImpl<_$MediaUploadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return mediaUploaded(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return mediaUploaded?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaUploaded != null) {
      return mediaUploaded(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return mediaUploaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return mediaUploaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaUploaded != null) {
      return mediaUploaded(this);
    }
    return orElse();
  }
}

abstract class _MediaUploaded implements AdminMediaManagementState {
  const factory _MediaUploaded(final String message) = _$MediaUploadedImpl;

  String get message;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaUploadedImplCopyWith<_$MediaUploadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MediaUpdatedImplCopyWith<$Res> {
  factory _$$MediaUpdatedImplCopyWith(
          _$MediaUpdatedImpl value, $Res Function(_$MediaUpdatedImpl) then) =
      __$$MediaUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$MediaUpdatedImplCopyWithImpl<$Res>
    extends _$AdminMediaManagementStateCopyWithImpl<$Res, _$MediaUpdatedImpl>
    implements _$$MediaUpdatedImplCopyWith<$Res> {
  __$$MediaUpdatedImplCopyWithImpl(
      _$MediaUpdatedImpl _value, $Res Function(_$MediaUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$MediaUpdatedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$MediaUpdatedImpl implements _MediaUpdated {
  const _$MediaUpdatedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminMediaManagementState.mediaUpdated(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaUpdatedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaUpdatedImplCopyWith<_$MediaUpdatedImpl> get copyWith =>
      __$$MediaUpdatedImplCopyWithImpl<_$MediaUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<MediaModel> mediaList) loadedPendingMedia,
    required TResult Function(String message) mediaApproved,
    required TResult Function(String message) mediaRejected,
    required TResult Function(String message) mediaUploaded,
    required TResult Function(String message) mediaUpdated,
  }) {
    return mediaUpdated(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult? Function(String message)? mediaApproved,
    TResult? Function(String message)? mediaRejected,
    TResult? Function(String message)? mediaUploaded,
    TResult? Function(String message)? mediaUpdated,
  }) {
    return mediaUpdated?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<MediaModel> mediaList)? loadedPendingMedia,
    TResult Function(String message)? mediaApproved,
    TResult Function(String message)? mediaRejected,
    TResult Function(String message)? mediaUploaded,
    TResult Function(String message)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaUpdated != null) {
      return mediaUpdated(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingMedia value) loadedPendingMedia,
    required TResult Function(_MediaApproved value) mediaApproved,
    required TResult Function(_MediaRejected value) mediaRejected,
    required TResult Function(_MediaUploaded value) mediaUploaded,
    required TResult Function(_MediaUpdated value) mediaUpdated,
  }) {
    return mediaUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult? Function(_MediaApproved value)? mediaApproved,
    TResult? Function(_MediaRejected value)? mediaRejected,
    TResult? Function(_MediaUploaded value)? mediaUploaded,
    TResult? Function(_MediaUpdated value)? mediaUpdated,
  }) {
    return mediaUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingMedia value)? loadedPendingMedia,
    TResult Function(_MediaApproved value)? mediaApproved,
    TResult Function(_MediaRejected value)? mediaRejected,
    TResult Function(_MediaUploaded value)? mediaUploaded,
    TResult Function(_MediaUpdated value)? mediaUpdated,
    required TResult orElse(),
  }) {
    if (mediaUpdated != null) {
      return mediaUpdated(this);
    }
    return orElse();
  }
}

abstract class _MediaUpdated implements AdminMediaManagementState {
  const factory _MediaUpdated(final String message) = _$MediaUpdatedImpl;

  String get message;

  /// Create a copy of AdminMediaManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaUpdatedImplCopyWith<_$MediaUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
