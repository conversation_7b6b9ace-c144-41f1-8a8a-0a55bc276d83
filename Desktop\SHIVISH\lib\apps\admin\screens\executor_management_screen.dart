import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../shared/models/executor.dart';

class ExecutorManagementScreen extends StatelessWidget {
  const ExecutorManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Executor Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Force refresh by rebuilding the widget
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const ExecutorManagementScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance
            .collection('executors')
            .where('isDeleted', isEqualTo: false)
            .orderBy('createdAt', descending: true)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
            return const Center(
              child: Text('No executors found'),
            );
          }

          final executors = snapshot.data!.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Executor.fromJson({
              'id': doc.id,
              ...data,
            });
          }).toList();

          return ListView.builder(
            itemCount: executors.length,
            itemBuilder: (context, index) {
              final executor = executors[index];
              return ListTile(
                leading: CircleAvatar(
                  child: Text(executor.name.substring(0, 1).toUpperCase()),
                ),
                title: Text(executor.name),
                subtitle: Text(executor.email),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: Icon(
                        executor.status == 'active' ? Icons.check_circle : Icons.cancel,
                        color: executor.status == 'active' ? Colors.green : Colors.red,
                      ),
                      onPressed: () => _updateExecutorStatus(
                        context,
                        executor.id,
                        executor.status != 'active'
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _deleteExecutor(context, executor),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddExecutorDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<void> _updateExecutorStatus(BuildContext context, String executorId, bool isActive) async {
    try {
      await FirebaseFirestore.instance.collection('executors').doc(executorId).update({
        'status': isActive ? 'active' : 'inactive',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Executor ${isActive ? 'activated' : 'deactivated'} successfully'),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating executor: $e')),
        );
      }
    }
  }

  Future<void> _deleteExecutor(BuildContext context, Executor executor) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Executor'),
        content: Text('Are you sure you want to delete ${executor.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true && context.mounted) {
      try {
        await FirebaseFirestore.instance.collection('executors').doc(executor.id).update({
          'isDeleted': true,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Executor deleted successfully')),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting executor: $e')),
          );
        }
      }
    }
  }

  Future<void> _showAddExecutorDialog(BuildContext context) async {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add New Executor'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(labelText: 'Name'),
            ),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(labelText: 'Email'),
              keyboardType: TextInputType.emailAddress,
            ),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(labelText: 'Phone'),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Add'),
          ),
        ],
      ),
    );

    if (result == true && context.mounted) {
      if (nameController.text.isEmpty || emailController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Name and email are required')),
        );
        return;
      }

      try {
        await FirebaseFirestore.instance.collection('executors').add({
          'name': nameController.text,
          'email': emailController.text,
          'phone': phoneController.text,
          'role': 'executor',
          'status': 'active',
          'isActive': true,
          'isDeleted': false,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Executor added successfully')),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error adding executor: $e')),
          );
        }
      }
    }
  }


}
