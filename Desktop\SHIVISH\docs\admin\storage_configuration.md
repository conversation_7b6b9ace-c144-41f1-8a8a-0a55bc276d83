# Storage Configuration Guide

This guide explains how to configure storage providers in the Shivish Multi-App system. The system supports multiple storage providers for different types of content, allowing you to optimize storage based on your needs.

> **Default Configuration**: The system is pre-configured to use local storage at `D:\SHIVISH_STORAGE` for all content types. This directory will be created automatically if it doesn't exist.

## Accessing Storage Settings

1. Log in to the Admin app
2. Navigate to **System Settings**
3. Click on **Storage Settings**

## Understanding Storage Configuration

The storage system consists of three main components:

1. **Storage Providers**: Different backends for storing files (local storage, Firebase Storage, Google Drive, etc.)
2. **Collection Configurations**: Settings for different types of content (documents, images, media, backups, user files)
3. **Default Provider**: The fallback storage provider to use when no specific provider is configured for a collection

## Adding a Storage Provider

To add a new storage provider:

1. In the Storage Settings screen, click the **+** button in the Storage Providers section
2. Fill in the provider details:
   - **Provider Type**: Select the type of storage provider
   - **Provider Name**: Enter a unique name for this provider (e.g., "local_drive_d", "firebase_main")
   - **Enabled**: Toggle to enable/disable this provider
   - **Provider-specific settings**: Fill in the settings specific to the selected provider type

### Local Storage Provider

For local storage:

- **Base Path**: Enter the full path to the storage directory (e.g., "D:\SHIVISH_STORAGE")

> **Important**: For Windows paths, use either forward slashes (D:/SHIVISH_STORAGE) or escaped backslashes (D:\\SHIVISH_STORAGE)

### Firebase Storage Provider

For Firebase Storage:

- **Bucket Name**: (Optional) Enter a custom bucket name if you're using a non-default bucket

### Google Drive Provider

For Google Drive:

- **API Key**: Enter your Google Drive API key
- **Root Folder ID**: Enter the ID of the root folder in Google Drive

## Configuring Collection Storage

After adding storage providers, you can configure which provider to use for each type of content:

1. In the Collection Storage Settings section, find the collection you want to configure
2. Select the storage provider from the dropdown
3. Enter the base path for this collection (e.g., "images", "documents")
4. Toggle the Enabled switch to enable/disable storage for this collection

## Setting the Default Provider

The default provider is used when no specific provider is configured for a collection:

1. In the Storage Providers section, find the provider you want to set as default
2. Click the "Set as Default" button (or use the context menu)

## Using Multiple Storage Locations

You can configure multiple storage providers of the same type with different settings. For example:

### Multiple Local Storage Locations

1. Add a local storage provider named "local_drive_d" with base path "D:\SHIVISH_STORAGE"
2. Add another local storage provider named "local_drive_e" with base path "E:\BACKUP_STORAGE"
3. Configure different collections to use different providers:
   - Set "images" and "media" to use "local_drive_d"
   - Set "backups" to use "local_drive_e"

### Mixing Provider Types

You can also mix different provider types:

1. Configure "images" and "media" to use local storage for faster access
2. Configure "backups" to use Google Drive for cloud backup
3. Configure "documents" to use Firebase Storage for better sharing capabilities

## Load Balancing

The system supports load balancing across multiple storage providers for each collection type. This allows you to distribute storage operations across multiple providers for better performance and reliability.

### How Load Balancing Works

1. **Add Multiple Providers**: First, add multiple storage providers (e.g., multiple local storage locations or a mix of provider types)
2. **Configure Collection**: For each collection, add a primary provider and additional providers
3. **Select Balancing Strategy**: Choose how operations should be distributed across providers

### Load Balancing Strategies

The system supports the following load balancing strategies:

1. **Single Provider**: All operations use the primary provider (no load balancing)
2. **Round Robin**: Operations are distributed sequentially across all providers
3. **Random**: Operations are randomly distributed across all providers

### Example: Load Balanced Image Storage

1. Add three local storage providers:
   - "local_drive_d" with base path "D:\SHIVISH_STORAGE"
   - "local_drive_e" with base path "E:\MEDIA_STORAGE"
   - "local_drive_f" with base path "F:\BACKUP_STORAGE"

2. Configure the "images" collection:
   - Set "local_drive_d" as the primary provider
   - Add "local_drive_e" and "local_drive_f" as additional providers
   - Select "Round Robin" as the balancing strategy

3. Result:
   - Image uploads will be distributed across all three drives
   - First image goes to drive D, second to drive E, third to drive F, fourth back to drive D, and so on
   - This distributes the load and provides redundancy

## Debug Settings

The Storage Settings screen also includes debug options:

- **Skip Document Uploads in Debug Mode**: When enabled, document uploads will be skipped in debug mode, which can be useful during development and testing

## Default Storage Configuration

The system comes pre-configured with the following default settings:

1. **Default Storage Provider**: Local storage at `D:\SHIVISH_STORAGE`
2. **Content Organization**:
   - Documents: `D:\SHIVISH_STORAGE\documents`
   - Images: `D:\SHIVISH_STORAGE\images`
   - Media: `D:\SHIVISH_STORAGE\media`
   - Backups: `D:\SHIVISH_STORAGE\backups`
   - User Files: `D:\SHIVISH_STORAGE\user_files`

These directories will be created automatically when the system needs to store files. You don't need to create them manually.

If you want to use a different path, you can update the configuration as described in this guide.

## Best Practices

1. **Use descriptive names** for your storage providers to easily identify them
2. **Configure collection-specific paths** to organize your storage
3. **Enable only the providers you need** to avoid confusion
4. **Set appropriate permissions** on local storage directories
5. **Regularly back up** your storage configuration
6. **Ensure sufficient disk space** is available on the storage drives

## Troubleshooting

### Files Not Uploading

- Check if the selected provider is enabled
- Verify the base path exists and is writable
- Check if the collection configuration is enabled

### Cannot Access Local Storage

- Ensure the path exists and is accessible
- Check file permissions
- For Windows paths, make sure to use the correct format

### Changes Not Taking Effect

- Refresh the app
- Check if the storage configuration was saved successfully
- Verify that the provider is enabled

## Advanced Configuration

For advanced configuration options, such as custom storage providers or additional settings, please contact the system administrator.
