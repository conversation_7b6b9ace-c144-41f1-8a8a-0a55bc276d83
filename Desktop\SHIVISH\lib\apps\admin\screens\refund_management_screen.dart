import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_bloc.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_event.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_state.dart';
import 'package:shivish/apps/admin/widgets/refund_list_item.dart';
import 'package:shivish/shared/ui_components/dialogs/refund_form_dialog.dart';
import 'package:shivish/shared/ui_components/states/empty_state.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/services/auth/auth_service.dart';
import 'package:shivish/shared/services/user/user_service.dart';

class RefundManagementScreen extends StatelessWidget {
  final AuthService _authService;
  final UserService _userService;

  const RefundManagementScreen({
    super.key,
    required AuthService authService,
    required UserService userService,
  })  : _authService = authService,
        _userService = userService;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Refund Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<RefundBloc, RefundState>(
        builder: (context, state) {
          return state.map(
            initial: (_) => const Center(child: Text('No refunds yet')),
            loading: (_) => const LoadingIndicator(),
            loaded: (loaded) => _buildRefundList(context, loaded.refunds),
            error: (error) => ErrorMessage(message: error.message),
          );
        },
      ),
    );
  }

  Widget _buildRefundList(BuildContext context, List<dynamic> refunds) {
    if (refunds.isEmpty) {
      return const EmptyState(
        message: 'No refunds found',
        icon: Icons.receipt_long,
        actionLabel: 'Create Refund',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<RefundBloc>().add(const RefundEvent.loadRefunds());
      },
      child: ListView.builder(
        itemCount: refunds.length,
        itemBuilder: (context, index) {
          return RefundListItem(
            refund: refunds[index],
            authService: _authService,
            userService: _userService,
          );
        },
      ),
    );
  }

  Future<void> _showCreateDialog(BuildContext context) async {
    final result = await showDialog<dynamic>(
      context: context,
      builder: (context) => const RefundFormDialog(),
    );

    if (result != null) {
      context.read<RefundBloc>().add(RefundEvent.createRefund(result));
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SuccessMessage(message: 'Refund created successfully'),
        );
      }
    }
  }
}
