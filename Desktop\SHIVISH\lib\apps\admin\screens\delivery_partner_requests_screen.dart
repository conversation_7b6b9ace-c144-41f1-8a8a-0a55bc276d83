import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';

final _logger = getLogger('DeliveryPartnerRequestsScreen');

/// Provider for pending delivery partners
final pendingDeliveryPartnersProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return FirebaseFirestore.instance
      .collection('delivery_persons')  // Changed from 'delivery_partners' to 'delivery_persons'
      .where('isVerified', isEqualTo: false)
      .where('isActive', isEqualTo: false)
      .snapshots()
      .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();
      });
});

/// Screen for admin to approve delivery partner requests
class DeliveryPartnerRequestsScreen extends ConsumerWidget {
  const DeliveryPartnerRequestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pendingPartnersAsync = ref.watch(pendingDeliveryPartnersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Delivery Partner Requests'),
      ),
      body: pendingPartnersAsync.when(
        data: (pendingPartners) {
          if (pendingPartners.isEmpty) {
            return const Center(
              child: Text('No pending delivery partners'),
            );
          }

          return ListView.builder(
            itemCount: pendingPartners.length,
            itemBuilder: (context, index) {
              final partner = pendingPartners[index];
              return _buildPartnerCard(context, ref, partner);
            },
          );
        },
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) {
          _logger.severe('Error loading pending delivery partners: $error\n$stackTrace');
          return ErrorMessage(message: error.toString());
        },
      ),
    );
  }

  Widget _buildPartnerCard(BuildContext context, WidgetRef ref, Map<String, dynamic> partner) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy');

    // Format created date - handle different timestamp formats
    String createdDate = 'Unknown date';
    final createdAt = partner['createdAt'];

    if (createdAt is Timestamp) {
      createdDate = dateFormat.format(createdAt.toDate());
    } else if (createdAt is String) {
      try {
        createdDate = dateFormat.format(DateTime.parse(createdAt));
      } catch (e) {
        _logger.warning('Failed to parse createdAt string: $e');
      }
    } else if (createdAt != null) {
      _logger.warning('Unexpected createdAt type: ${createdAt.runtimeType}');
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    partner['name']?.substring(0, 1).toUpperCase() ?? 'D',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        partner['name'] ?? 'Unknown',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        partner['email'] ?? 'No email',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: const Text('Pending'),
                  backgroundColor: Colors.orange.shade100,
                  labelStyle: TextStyle(color: Colors.orange.shade800),
                ),
              ],
            ),
            const Divider(height: 24),
            _buildInfoRow('Phone', partner['phone'] ?? 'Not provided'),
            _buildInfoRow('Vehicle Type', partner['vehicleType'] ?? 'Not provided'),
            _buildInfoRow('Vehicle Number', partner['vehicleNumber'] ?? 'Not provided'),
            _buildInfoRow('Aadhar Number', partner['aadharNumber'] ?? 'Not provided'),
            _buildInfoRow('Driving License', partner['drivingLicense'] ?? 'Not provided'),
            _buildInfoRow('Registered On', createdDate),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => _showRejectDialog(context, ref, partner),
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: () => _showApproveDialog(context, ref, partner),
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showApproveDialog(BuildContext context, WidgetRef ref, Map<String, dynamic> partner) {
    final notesController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Delivery Partner'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to approve ${partner['name']}?'),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                await _approveDeliveryPartner(
                  partner['id'],
                  notes: notesController.text.isNotEmpty ? notesController.text : null,
                );

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${partner['name']} has been approved'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error approving delivery partner: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  void _showRejectDialog(BuildContext context, WidgetRef ref, Map<String, dynamic> partner) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Delivery Partner'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to reject ${partner['name']}?'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for Rejection (Required)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              if (reasonController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a reason for rejection'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context);

              try {
                await _rejectDeliveryPartner(
                  partner['id'],
                  reason: reasonController.text,
                );

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${partner['name']} has been rejected'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error rejecting delivery partner: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<void> _approveDeliveryPartner(String deliveryPartnerId, {String? notes}) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final currentUser = firestore.collection('users').doc('admin'); // Placeholder for actual admin ID

      await firestore.collection('delivery_persons').doc(deliveryPartnerId).update({
        'isVerified': true,
        'isActive': true,
        'approvedBy': currentUser.id,
        'approverRole': 'admin',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvalNotes': notes,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      _logger.info('Delivery partner $deliveryPartnerId approved by admin');
    } catch (e) {
      _logger.severe('Error approving delivery partner: $e');
      rethrow;
    }
  }

  Future<void> _rejectDeliveryPartner(String deliveryPartnerId, {required String reason}) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final currentUser = firestore.collection('users').doc('admin'); // Placeholder for actual admin ID

      await firestore.collection('delivery_persons').doc(deliveryPartnerId).update({
        'isVerified': false,
        'isActive': false,
        'rejectedBy': currentUser.id,
        'rejectorRole': 'admin',
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectionReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      _logger.info('Delivery partner $deliveryPartnerId rejected by admin');
    } catch (e) {
      _logger.severe('Error rejecting delivery partner: $e');
      rethrow;
    }
  }
}
