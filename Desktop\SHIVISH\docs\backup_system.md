# Backup System Documentation

## Overview

The Backup System is a comprehensive solution for backing up and restoring application data. It supports both local storage and cloud backup, with features like encryption, compression, validation, and automatic scheduling.

## Features

- **Local Backup**: Store backups on the device using Hive.
- **Cloud Backup**: Store backups in Google Drive.
- **Automatic Backup**: Schedule backups with configurable frequency.
- **Progress Tracking**: Real-time progress tracking with UI updates.
- **Conflict Resolution**: Detect and resolve conflicts between local and cloud data.
- **Encryption**: Encrypt backup data for security.
- **Compression**: Compress backup data to save space.
- **Validation**: Validate backup integrity.
- **Retention Policies**: Configure how long to keep old backups.
- **Notifications**: Notify users of backup status.
- **Analytics**: Track backup statistics and analytics.
- **Export/Import**: Export and import backups.
- **Emergency Backup**: Create emergency backups before potentially destructive operations.
- **Recovery**: Recover from backup failures or corruption.

## Architecture

The Backup System is built with a modular architecture that separates concerns and makes it easy to extend or modify.

### Core Components

- **HybridBackupService**: Handles backup and restore operations.
- **BackupController**: Manages backup state and operations.
- **BackupScheduler**: Handles automatic backup scheduling.
- **GoogleDriveManager**: Manages Google Drive operations.
- **SyncConflictHandler**: Detects and resolves conflicts.
- **BackupRecoveryService**: Handles recovery from failures.
- **BackupRetentionService**: Manages backup retention policies.
- **BackupAnalyticsService**: Tracks backup statistics.
- **BackupNotificationService**: Handles notifications.
- **BackupExportImportService**: Handles exporting and importing backups.

### Data Models

- **BackupConfig**: Configuration for backup operations.
- **BackupMetadata**: Metadata for available backups.
- **BackupProgress**: Tracks progress of backup operations.
- **BackupStats**: Statistics about backups.

### Integration

- **BackupModule**: Module for integrating the backup system.
- **BackupAPI**: Simple API for integrating the backup system.
- **BackupWidget**: Widget for easily integrating the backup system.
- **BackupDependencyInjector**: Handles dependency injection.

## Getting Started

### Installation

Add the following dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.0.15
  flutter_secure_storage: ^8.0.0
  encrypt: ^5.0.1
  google_sign_in: ^6.1.4
  googleapis: ^11.2.0
  firebase_auth: ^4.6.3
  firebase_core: ^2.14.0
  shared_preferences: ^2.2.0
  provider: ^6.0.5
  flutter_local_notifications: ^14.1.1
  intl: ^0.18.1
  share_plus: ^7.0.2
  file_picker: ^5.3.2
```

### Basic Usage

1. Initialize the backup system:

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  runApp(
    MultiProvider(
      providers: BackupAPI.getProviders(),
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize the backup system
    BackupAPI.initialize(context);
    
    return MaterialApp(
      title: 'My App',
      home: MyHomePage(),
    );
  }
}
```

2. Add backup functionality to your app:

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/widgets/backup/backup_widget.dart';

class MyHomePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('My App'),
        actions: [
          IconButton(
            icon: Icon(Icons.backup),
            onPressed: () => BackupAPI.showBackupManager(context),
          ),
        ],
      ),
      body: Center(
        child: BackupWidget(),
      ),
    );
  }
}
```

### Advanced Usage

#### Custom Backup Configuration

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/models/backup_config.dart';

void updateBackupConfig(BuildContext context) {
  final config = BackupConfig(
    enableAutoBackup: true,
    enableCloudBackup: true,
    enableLocalBackup: true,
    enableEncryption: true,
    enableCompression: true,
    enableValidation: true,
    backupFrequency: 'daily',
    retentionDays: 30,
    localStoragePath: 'backups',
  );
  
  BackupAPI.updateBackupConfig(context, config);
}
```

#### Manual Backup

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';

Future<void> createBackup(BuildContext context) async {
  final success = await BackupAPI.createBackup(context);
  
  if (success) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Backup created successfully')),
    );
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to create backup')),
    );
  }
}
```

#### Restore Backup

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/models/backup_metadata.dart';

Future<void> restoreBackup(BuildContext context, BackupMetadata backup) async {
  final success = await BackupAPI.restoreBackup(context, backup);
  
  if (success) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Backup restored successfully')),
    );
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to restore backup')),
    );
  }
}
```

#### Export and Import Backups

```dart
import 'package:flutter/material.dart';
import 'package:SHIVISH/shared/api/backup_api.dart';
import 'package:SHIVISH/shared/models/backup_metadata.dart';

Future<void> exportBackup(BuildContext context, BackupMetadata backup) async {
  final exportPath = await BackupAPI.exportBackup(context, backup);
  
  if (exportPath != null) {
    final shared = await BackupAPI.shareBackup(context, exportPath);
    
    if (!shared) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to share backup')),
      );
    }
  } else {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Failed to export backup')),
    );
  }
}

Future<void> importBackup(BuildContext context) async {
  final importPath = await BackupAPI.pickBackupFile(context);
  
  if (importPath != null) {
    final backup = await BackupAPI.importBackup(context, importPath);
    
    if (backup != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Backup imported successfully')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to import backup')),
      );
    }
  }
}
```

## API Reference

### BackupAPI

The `BackupAPI` class provides a simple API for integrating the backup system into your app.

#### Methods

- `initialize(BuildContext context)`: Initialize the backup system.
- `getProviders()`: Get providers for the backup system.
- `showBackupManager(BuildContext context)`: Show the backup manager screen.
- `createBackup(BuildContext context)`: Create a backup.
- `restoreBackup(BuildContext context, BackupMetadata backup)`: Restore a backup.
- `getBackupStats(BuildContext context)`: Get backup statistics.
- `getBackupController(BuildContext context)`: Get the backup controller.
- `updateBackupConfig(BuildContext context, BackupConfig config)`: Update backup configuration.
- `getAvailableBackups(BuildContext context)`: Get available backups.
- `loadAvailableBackups(BuildContext context)`: Load available backups.
- `deleteBackup(BuildContext context, BackupMetadata backup)`: Delete a backup.
- `applyRetentionPolicy(BuildContext context)`: Apply retention policy.
- `createEmergencyBackup(BuildContext context)`: Create an emergency backup.
- `restoreFromEmergencyBackup(BuildContext context, String backupPath)`: Restore from an emergency backup.
- `exportBackup(BuildContext context, BackupMetadata backup)`: Export a backup.
- `importBackup(BuildContext context, String importPath)`: Import a backup.
- `shareBackup(BuildContext context, String exportPath)`: Share a backup.
- `pickBackupFile(BuildContext context)`: Pick a backup file to import.

### BackupWidget

The `BackupWidget` class provides a widget for easily integrating the backup system into your app.

#### Properties

- `showBackupButton`: Whether to show the backup button.
- `showRestoreButton`: Whether to show the restore button.
- `showManagerButton`: Whether to show the backup manager button.
- `showStats`: Whether to show the backup stats.
- `backupButtonBuilder`: Custom builder for the backup button.
- `restoreButtonBuilder`: Custom builder for the restore button.
- `managerButtonBuilder`: Custom builder for the backup manager button.
- `statsBuilder`: Custom builder for the backup stats.

### BackupConfig

The `BackupConfig` class represents the configuration for backup operations.

#### Properties

- `enableAutoBackup`: Whether to enable automatic backup.
- `enableCloudBackup`: Whether to enable cloud backup.
- `enableLocalBackup`: Whether to enable local backup.
- `enableEncryption`: Whether to enable encryption.
- `enableCompression`: Whether to enable compression.
- `enableValidation`: Whether to enable validation.
- `backupFrequency`: Frequency of automatic backups.
- `retentionDays`: Number of days to keep backups.
- `localStoragePath`: Path to store local backups.
- `excludedCollections`: Collections to exclude from backup.
- `cloudSettings`: Settings for cloud backup.

### BackupMetadata

The `BackupMetadata` class represents metadata for a backup.

#### Properties

- `id`: Unique identifier for the backup.
- `timestamp`: When the backup was created.
- `size`: Size of the backup in bytes.
- `source`: Source of the backup (local or cloud).
- `path`: Path to the backup file (for local backups).
- `driveFile`: Google Drive file ID (for cloud backups).
- `type`: Type of backup (regular, emergency, or auto).
- `metadata`: Additional metadata.

#### Methods

- `formattedSize`: Get the size formatted as a string.
- `formattedDate`: Get the date formatted as a string.
- `isLocal`: Whether the backup is stored locally.
- `isCloud`: Whether the backup is stored in the cloud.
- `isRegular`: Whether the backup is a regular backup.
- `isEmergency`: Whether the backup is an emergency backup.
- `isAuto`: Whether the backup is an automatic backup.

## Troubleshooting

### Common Issues

#### Backup Creation Fails

- Check if the device has enough storage space.
- Check if the app has the necessary permissions.
- Check if the Google Drive API is properly configured.

#### Restore Fails

- Check if the backup file exists.
- Check if the backup file is corrupted.
- Check if the app has the necessary permissions.

#### Automatic Backup Not Working

- Check if automatic backup is enabled in the configuration.
- Check if the app has the necessary permissions.
- Check if the device has enough storage space.

### Error Handling

The backup system includes robust error handling to recover from failures:

- **Emergency Backups**: Created before potentially destructive operations.
- **Recovery Service**: Handles recovery from backup failures or corruption.
- **Conflict Resolution**: Detects and resolves conflicts between local and cloud data.

## Best Practices

- **Regular Backups**: Configure automatic backups to run regularly.
- **Multiple Backup Sources**: Enable both local and cloud backups.
- **Encryption**: Enable encryption for sensitive data.
- **Validation**: Enable validation to ensure backup integrity.
- **Retention Policy**: Configure a retention policy to manage backup storage.
- **Testing**: Regularly test backup and restore functionality.

## Contributing

Contributions to the Backup System are welcome! Please follow these steps:

1. Fork the repository.
2. Create a new branch for your feature or bug fix.
3. Write tests for your changes.
4. Make your changes and ensure all tests pass.
5. Submit a pull request.

## License

The Backup System is licensed under the MIT License. See the LICENSE file for details.
