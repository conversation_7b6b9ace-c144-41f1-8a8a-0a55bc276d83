import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/apps/admin/providers/auth_provider.dart';

/// Provider for pending hospital requests
final pendingHospitalRequestsProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return FirebaseFirestore.instance
      .collection('hospital_requests')
      .where('status', isEqualTo: 'pending')
      .snapshots()
      .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();
      });
});

/// Screen for admin to approve hospital requests
class HospitalRequestsScreen extends ConsumerStatefulWidget {
  const HospitalRequestsScreen({super.key});

  @override
  ConsumerState<HospitalRequestsScreen> createState() => _HospitalRequestsScreenState();
}

class _HospitalRequestsScreenState extends ConsumerState<HospitalRequestsScreen> {
  bool _isLoading = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final pendingRequestsAsync = ref.watch(pendingHospitalRequestsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Hospital Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh the provider and ignore the result
              final _ = ref.refresh(pendingHospitalRequestsProvider);
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? Center(
                  child: ErrorMessage(
                    message: _errorMessage!,
                    onRetry: () {
                      // Refresh the provider and ignore the result
                      final _ = ref.refresh(pendingHospitalRequestsProvider);
                    },
                  ),
                )
              : pendingRequestsAsync.when(
                  data: (requests) {
                    if (requests.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.local_hospital,
                              size: 64,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No Pending Requests',
                              style: Theme.of(context).textTheme.titleLarge,
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'There are no pending hospital requests at this time.',
                              style: Theme.of(context).textTheme.bodyMedium,
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: requests.length,
                      itemBuilder: (context, index) {
                        final request = requests[index];
                        return _buildRequestCard(request);
                      },
                    );
                  },
                  loading: () => const Center(child: LoadingIndicator()),
                  error: (error, _) => Center(
                    child: ErrorMessage(
                      message: 'Error loading hospital requests: $error',
                      onRetry: () {
                        // Refresh the provider and ignore the result
                        final _ = ref.refresh(pendingHospitalRequestsProvider);
                      },
                    ),
                  ),
                ),
    );
  }

  Widget _buildRequestCard(Map<String, dynamic> request) {
    final createdAt = request['createdAt'] as Timestamp?;
    final createdAtDate = createdAt?.toDate() ?? DateTime.now();
    final timeAgo = timeago.format(createdAtDate);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.local_hospital, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    request['hospitalName'] ?? 'Unknown Hospital',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.amber.shade300),
                  ),
                  child: const Text(
                    'Pending',
                    style: TextStyle(
                      color: Colors.amber,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(),
            _buildInfoRow('Owner', request['ownerName'] ?? 'Unknown'),
            _buildInfoRow('Email', request['email'] ?? 'Unknown'),
            _buildInfoRow('Phone', request['phone'] ?? 'Unknown'),
            _buildInfoRow('Requested', timeAgo),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => _showRejectDialog(request['id']),
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _approveHospital(request['id']),
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _approveHospital(String requestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final firestore = FirebaseFirestore.instance;

      // Get request document
      final requestDoc = await firestore.collection('hospital_requests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Hospital request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['userId'] as String;
      final hospitalId = requestData['hospitalId'] as String;

      // Update hospital document
      await firestore.collection('hospitals').doc(hospitalId).update({
        'isApproved': true,
        'isActive': true,
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': ref.read(adminAuthStateProvider).user?.uid ?? 'unknown',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update hospital user document
      await firestore.collection('hospital_users').doc(userId).update({
        'isApproved': true,
        'isActive': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update request status
      await firestore.collection('hospital_requests').doc(requestId).update({
        'status': 'approved',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': ref.read(adminAuthStateProvider).user?.uid ?? 'unknown',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Refresh the list and ignore the result
      final _ = ref.refresh(pendingHospitalRequestsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Hospital approved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving hospital: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showRejectDialog(String requestId) async {
    final reasonController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Hospital Request'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                Navigator.pop(context);
                _rejectHospital(requestId, reasonController.text);
              }
            },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: Colors.red,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<void> _rejectHospital(String requestId, String reason) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Update request status
      await FirebaseFirestore.instance.collection('hospital_requests').doc(requestId).update({
        'status': 'rejected',
        'rejectionReason': reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': ref.read(adminAuthStateProvider).user?.uid ?? 'unknown',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Refresh the list and ignore the result
      final _ = ref.refresh(pendingHospitalRequestsProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Hospital request rejected'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting hospital: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
