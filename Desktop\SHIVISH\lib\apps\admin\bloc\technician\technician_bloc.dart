import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_event.dart';
import 'package:shivish/apps/admin/bloc/technician/technician_state.dart';
import 'package:shivish/shared/models/technician.dart';
import 'package:shivish/shared/services/technician_service.dart';

@injectable
class TechnicianBloc extends Bloc<TechnicianEvent, TechnicianState> {
  final TechnicianService _technicianService;
  List<Technician> _technicians = [];
  Map<String, dynamic> _filters = {};
  static const int _pageSize = 20;

  TechnicianBloc(this._technicianService)
      : super(const TechnicianState.initial()) {
    on<TechnicianEvent>((event, emit) async {
      await event.when(
        loadTechnicians: () => _onLoadTechnicians(emit),
        loadMoreTechnicians: () => _onLoadMoreTechnicians(emit),
        updateTechnicianStatus: (technician, status, notes, isActive) =>
            _onUpdateTechnicianStatus(
          emit,
          technician,
          status,
          notes,
          isActive,
        ),
        deleteTechnician: (id) => _onDeleteTechnician(emit, id),
        applyFilters: (filters) => _onApplyFilters(emit, filters),
      );
    });
  }

  Future<void> _onLoadTechnicians(Emitter<TechnicianState> emit) async {
    try {
      emit(const TechnicianState.loading());
      _technicians = await _technicianService.getTechnicians(
        limit: _pageSize,
        filters: _filters,
      );
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onLoadMoreTechnicians(Emitter<TechnicianState> emit) async {
    try {
      if (_technicians.isEmpty) return;
      emit(TechnicianState.loadingMore(_technicians));

      final lastTechnician = _technicians.last;
      final lastDoc =
          await _technicianService.getTechnicianDocument(lastTechnician.id);
      final moreTechnicians = await _technicianService.getTechnicians(
        limit: _pageSize,
        startAfter: lastDoc,
        filters: _filters,
      );

      if (moreTechnicians.isEmpty) {
        emit(TechnicianState.loaded(_technicians));
        return;
      }

      _technicians = [..._technicians, ...moreTechnicians];
      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onUpdateTechnicianStatus(
    Emitter<TechnicianState> emit,
    Technician technician,
    String status,
    String? notes,
    bool isActive,
  ) async {
    try {
      final updatedTechnician = technician.copyWith(
        verificationStatus: status,
        verificationNotes: notes,
        isActive: isActive,
        updatedAt: DateTime.now(),
      );

      await _technicianService.updateTechnician(updatedTechnician);

      _technicians = _technicians.map((t) {
        return t.id == technician.id ? updatedTechnician : t;
      }).toList();

      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onDeleteTechnician(
    Emitter<TechnicianState> emit,
    String id,
  ) async {
    try {
      await _technicianService.deleteTechnician(id);

      _technicians = _technicians.where((t) => t.id != id).toList();

      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      emit(TechnicianState.error(e.toString()));
    }
  }

  Future<void> _onApplyFilters(
    Emitter<TechnicianState> emit,
    Map<String, dynamic> filters,
  ) async {
    try {
      _filters = filters;
      emit(const TechnicianState.loading());

      _technicians = await _technicianService.getTechnicians(
        limit: _pageSize,
        filters: _filters,
      );

      emit(TechnicianState.loaded(_technicians));
    } catch (e) {
      emit(TechnicianState.error(e.toString()));
    }
  }
}
