﻿package com.shivish

import android.content.Context
import android.speech.tts.TextToSpeech
import android.util.Log
import com.shivish.utils.CustomUtteranceProgressListener
import com.shivish.utils.TTSUtils
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.util.*

/**
 * Flutter plugin for Text-to-Speech functionality
 */
class TTSPlugin : FlutterPlugin, MethodCallHandler, TextToSpeech.OnInitListener {
    private val TAG = "TTSPlugin"
    private lateinit var channel: MethodChannel
    private var context: Context? = null
    private var textToSpeech: TextToSpeech? = null
    private var isInitialized = false

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "tts_plugin")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
        initializeTTS()
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        shutdownTTS()
        context = null
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "speak" -> {
                val text = call.argument<String>("text")
                if (text != null) {
                    speak(text, result)
                } else {
                    result.error("INVALID_ARGUMENT", "Text cannot be null", null)
                }
            }
            "stop" -> {
                stop(result)
            }
            "isAvailable" -> {
                result.success(TTSUtils.isTTSAvailable(context!!))
            }
            "getLanguages" -> {
                getAvailableLanguages(result)
            }
            "setLanguage" -> {
                val languageCode = call.argument<String>("language")
                if (languageCode != null) {
                    setLanguage(languageCode, result)
                } else {
                    result.error("INVALID_ARGUMENT", "Language code cannot be null", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun initializeTTS() {
        try {
            context?.let { ctx ->
                textToSpeech = TextToSpeech(ctx, this)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing TTS: ${e.message}")
        }
    }

    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            try {
                textToSpeech?.let { tts ->
                    val result = tts.setLanguage(Locale.getDefault())
                    if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        Log.w(TAG, "Default language not supported, trying English")
                        tts.setLanguage(Locale.US)
                    }
                    
                    // Set utterance progress listener
                    tts.setOnUtteranceProgressListener(CustomUtteranceProgressListener())
                    
                    isInitialized = true
                    Log.d(TAG, "TTS initialized successfully")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up TTS: ${e.message}")
            }
        } else {
            Log.e(TAG, "TTS initialization failed with status: $status")
        }
    }

    private fun speak(text: String, result: Result) {
        try {
            if (!isInitialized || textToSpeech == null) {
                result.error("TTS_NOT_INITIALIZED", "TTS is not initialized", null)
                return
            }

            val success = TTSUtils.speakText(textToSpeech, text)
            if (success) {
                result.success(true)
            } else {
                result.error("TTS_ERROR", "Failed to speak text", null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text: ${e.message}")
            result.error("TTS_ERROR", "Error speaking text: ${e.message}", null)
        }
    }

    private fun stop(result: Result) {
        try {
            textToSpeech?.stop()
            result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping TTS: ${e.message}")
            result.error("TTS_ERROR", "Error stopping TTS: ${e.message}", null)
        }
    }

    private fun getAvailableLanguages(result: Result) {
        try {
            val languages = TTSUtils.getSupportedLanguages(textToSpeech)
            val languageCodes = languages.map { it.toString() }
            result.success(languageCodes)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting available languages: ${e.message}")
            result.error("TTS_ERROR", "Error getting available languages: ${e.message}", null)
        }
    }

    private fun setLanguage(languageCode: String, result: Result) {
        try {
            if (!isInitialized || textToSpeech == null) {
                result.error("TTS_NOT_INITIALIZED", "TTS is not initialized", null)
                return
            }

            val locale = Locale.forLanguageTag(languageCode)
            val success = TTSUtils.isLanguageSupported(textToSpeech, locale)
            
            if (success) {
                textToSpeech?.setLanguage(locale)
                result.success(true)
            } else {
                result.error("LANGUAGE_NOT_SUPPORTED", "Language not supported: $languageCode", null)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting language: ${e.message}")
            result.error("TTS_ERROR", "Error setting language: ${e.message}", null)
        }
    }

    private fun shutdownTTS() {
        try {
            textToSpeech?.stop()
            textToSpeech?.shutdown()
            textToSpeech = null
            isInitialized = false
        } catch (e: Exception) {
            Log.e(TAG, "Error shutting down TTS: ${e.message}")
        }
    }
}
