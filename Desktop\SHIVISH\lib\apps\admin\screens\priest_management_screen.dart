import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/priest/priest_bloc.dart';
import '../widgets/priest_list_item.dart';
import '../widgets/priest_filter_dialog.dart';
import '../widgets/priest_verification_dialog.dart';
import '../widgets/priest_delete_dialog.dart';
import '../widgets/priest_performance_chart.dart';
import '../../../shared/models/priest.dart';

class PriestManagementScreen extends StatefulWidget {
  const PriestManagementScreen({super.key});

  @override
  State<PriestManagementScreen> createState() => _PriestManagementScreenState();
}

class _PriestManagementScreenState extends State<PriestManagementScreen> {
  final _scrollController = ScrollController();
  Map<String, dynamic> _filters = {};
  final Set<String> _selectedPriests = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<PriestBloc>().add(const PriestEvent.loadPriests());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      context.read<PriestBloc>().add(const PriestEvent.loadMorePriests());
    }
  }

  Future<void> _showFilterDialog() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => PriestFilterDialog(currentFilters: _filters),
    );

    if (result != null) {
      setState(() {
        _filters = result;
      });
      context.read<PriestBloc>().add(const PriestEvent.loadPriests());
    }
  }

  Future<void> _showVerificationDialog(Priest priest) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => PriestVerificationDialog(priest: priest),
    );

    if (result != null) {
      context.read<PriestBloc>().add(
            PriestEvent.updatePriestVerification(
              id: priest.id,
              isVerified: result['isVerified'],
              verificationStatus: result['verificationStatus'],
              verificationNotes: result['verificationNotes'],
            ),
          );
    }
  }


  Future<void> _showDeleteDialog(Priest priest) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => PriestDeleteDialog(priest: priest),
    );

    if (result == true) {
      context.read<PriestBloc>().add(PriestEvent.deletePriest(id: priest.id));
    }
  }

  void _toggleSelection(String priestId) {
    setState(() {
      if (_selectedPriests.contains(priestId)) {
        _selectedPriests.remove(priestId);
      } else {
        _selectedPriests.add(priestId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Priest Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: BlocBuilder<PriestBloc, PriestState>(
        builder: (context, state) {
          return state.map(
            initial: (_) => const Center(child: CircularProgressIndicator()),
            loading: (_) => const Center(child: CircularProgressIndicator()),
            error: (error) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    error.message,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context
                          .read<PriestBloc>()
                          .add(const PriestEvent.loadPriests());
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
            loaded: (loaded) {
              final priests = loaded.priests;

              if (priests.isEmpty) {
                return const Center(
                  child: Text('No priests found'),
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  context
                      .read<PriestBloc>()
                      .add(const PriestEvent.loadPriests());
                },
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: priests.length,
                  itemBuilder: (context, index) {
                    final priest = priests[index];
                    return Column(
                      children: [
                        PriestListItem(
                          priest: priest,
                          isSelected: _selectedPriests.contains(priest.id),
                          onSelect: () => _toggleSelection(priest.id),
                          onStatusUpdate: () {
                            context.read<PriestBloc>().add(
                                  PriestEvent.updatePriestStatus(
                                    id: priest.id,
                                    isActive: !priest.isActive,
                                  ),
                                );
                          },
                          onVerificationUpdate: () =>
                              _showVerificationDialog(priest),
                          onDelete: () => _showDeleteDialog(priest),
                        ),
                        PriestPerformanceChart(priest: priest),
                      ],
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
