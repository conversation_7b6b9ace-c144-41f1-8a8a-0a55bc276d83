// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_management_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$UserManagementEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserManagementEventCopyWith<$Res> {
  factory $UserManagementEventCopyWith(
          UserManagementEvent value, $Res Function(UserManagementEvent) then) =
      _$UserManagementEventCopyWithImpl<$Res, UserManagementEvent>;
}

/// @nodoc
class _$UserManagementEventCopyWithImpl<$Res, $Val extends UserManagementEvent>
    implements $UserManagementEventCopyWith<$Res> {
  _$UserManagementEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadUsersImplCopyWith<$Res> {
  factory _$$LoadUsersImplCopyWith(
          _$LoadUsersImpl value, $Res Function(_$LoadUsersImpl) then) =
      __$$LoadUsersImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadUsersImplCopyWithImpl<$Res>
    extends _$UserManagementEventCopyWithImpl<$Res, _$LoadUsersImpl>
    implements _$$LoadUsersImplCopyWith<$Res> {
  __$$LoadUsersImplCopyWithImpl(
      _$LoadUsersImpl _value, $Res Function(_$LoadUsersImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadUsersImpl implements LoadUsers {
  const _$LoadUsersImpl();

  @override
  String toString() {
    return 'UserManagementEvent.loadUsers()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadUsersImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) {
    return loadUsers();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) {
    return loadUsers?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) {
    if (loadUsers != null) {
      return loadUsers();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) {
    return loadUsers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) {
    return loadUsers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) {
    if (loadUsers != null) {
      return loadUsers(this);
    }
    return orElse();
  }
}

abstract class LoadUsers implements UserManagementEvent {
  const factory LoadUsers() = _$LoadUsersImpl;
}

/// @nodoc
abstract class _$$AddUserImplCopyWith<$Res> {
  factory _$$AddUserImplCopyWith(
          _$AddUserImpl value, $Res Function(_$AddUserImpl) then) =
      __$$AddUserImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User user});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$AddUserImplCopyWithImpl<$Res>
    extends _$UserManagementEventCopyWithImpl<$Res, _$AddUserImpl>
    implements _$$AddUserImplCopyWith<$Res> {
  __$$AddUserImplCopyWithImpl(
      _$AddUserImpl _value, $Res Function(_$AddUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$AddUserImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$AddUserImpl implements AddUser {
  const _$AddUserImpl(this.user);

  @override
  final User user;

  @override
  String toString() {
    return 'UserManagementEvent.addUser(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddUserImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddUserImplCopyWith<_$AddUserImpl> get copyWith =>
      __$$AddUserImplCopyWithImpl<_$AddUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) {
    return addUser(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) {
    return addUser?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) {
    if (addUser != null) {
      return addUser(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) {
    return addUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) {
    return addUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) {
    if (addUser != null) {
      return addUser(this);
    }
    return orElse();
  }
}

abstract class AddUser implements UserManagementEvent {
  const factory AddUser(final User user) = _$AddUserImpl;

  User get user;

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddUserImplCopyWith<_$AddUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateUserImplCopyWith<$Res> {
  factory _$$UpdateUserImplCopyWith(
          _$UpdateUserImpl value, $Res Function(_$UpdateUserImpl) then) =
      __$$UpdateUserImplCopyWithImpl<$Res>;
  @useResult
  $Res call({User user});

  $UserCopyWith<$Res> get user;
}

/// @nodoc
class __$$UpdateUserImplCopyWithImpl<$Res>
    extends _$UserManagementEventCopyWithImpl<$Res, _$UpdateUserImpl>
    implements _$$UpdateUserImplCopyWith<$Res> {
  __$$UpdateUserImplCopyWithImpl(
      _$UpdateUserImpl _value, $Res Function(_$UpdateUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? user = null,
  }) {
    return _then(_$UpdateUserImpl(
      null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as User,
    ));
  }

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserCopyWith<$Res> get user {
    return $UserCopyWith<$Res>(_value.user, (value) {
      return _then(_value.copyWith(user: value));
    });
  }
}

/// @nodoc

class _$UpdateUserImpl implements UpdateUser {
  const _$UpdateUserImpl(this.user);

  @override
  final User user;

  @override
  String toString() {
    return 'UserManagementEvent.updateUser(user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateUserImpl &&
            (identical(other.user, user) || other.user == user));
  }

  @override
  int get hashCode => Object.hash(runtimeType, user);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateUserImplCopyWith<_$UpdateUserImpl> get copyWith =>
      __$$UpdateUserImplCopyWithImpl<_$UpdateUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) {
    return updateUser(user);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) {
    return updateUser?.call(user);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) {
    if (updateUser != null) {
      return updateUser(user);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) {
    return updateUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) {
    return updateUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) {
    if (updateUser != null) {
      return updateUser(this);
    }
    return orElse();
  }
}

abstract class UpdateUser implements UserManagementEvent {
  const factory UpdateUser(final User user) = _$UpdateUserImpl;

  User get user;

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateUserImplCopyWith<_$UpdateUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteUserImplCopyWith<$Res> {
  factory _$$DeleteUserImplCopyWith(
          _$DeleteUserImpl value, $Res Function(_$DeleteUserImpl) then) =
      __$$DeleteUserImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String userId});
}

/// @nodoc
class __$$DeleteUserImplCopyWithImpl<$Res>
    extends _$UserManagementEventCopyWithImpl<$Res, _$DeleteUserImpl>
    implements _$$DeleteUserImplCopyWith<$Res> {
  __$$DeleteUserImplCopyWithImpl(
      _$DeleteUserImpl _value, $Res Function(_$DeleteUserImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
  }) {
    return _then(_$DeleteUserImpl(
      null == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteUserImpl implements DeleteUser {
  const _$DeleteUserImpl(this.userId);

  @override
  final String userId;

  @override
  String toString() {
    return 'UserManagementEvent.deleteUser(userId: $userId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteUserImpl &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, userId);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteUserImplCopyWith<_$DeleteUserImpl> get copyWith =>
      __$$DeleteUserImplCopyWithImpl<_$DeleteUserImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) {
    return deleteUser(userId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) {
    return deleteUser?.call(userId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) {
    if (deleteUser != null) {
      return deleteUser(userId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) {
    return deleteUser(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) {
    return deleteUser?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) {
    if (deleteUser != null) {
      return deleteUser(this);
    }
    return orElse();
  }
}

abstract class DeleteUser implements UserManagementEvent {
  const factory DeleteUser(final String userId) = _$DeleteUserImpl;

  String get userId;

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteUserImplCopyWith<_$DeleteUserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilterUsersImplCopyWith<$Res> {
  factory _$$FilterUsersImplCopyWith(
          _$FilterUsersImpl value, $Res Function(_$FilterUsersImpl) then) =
      __$$FilterUsersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({UserRole? role, UserStatus? status, bool? isActive});
}

/// @nodoc
class __$$FilterUsersImplCopyWithImpl<$Res>
    extends _$UserManagementEventCopyWithImpl<$Res, _$FilterUsersImpl>
    implements _$$FilterUsersImplCopyWith<$Res> {
  __$$FilterUsersImplCopyWithImpl(
      _$FilterUsersImpl _value, $Res Function(_$FilterUsersImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? role = freezed,
    Object? status = freezed,
    Object? isActive = freezed,
  }) {
    return _then(_$FilterUsersImpl(
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as UserStatus?,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$FilterUsersImpl implements FilterUsers {
  const _$FilterUsersImpl({this.role, this.status, this.isActive});

  @override
  final UserRole? role;
  @override
  final UserStatus? status;
  @override
  final bool? isActive;

  @override
  String toString() {
    return 'UserManagementEvent.filterUsers(role: $role, status: $status, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterUsersImpl &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, role, status, isActive);

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterUsersImplCopyWith<_$FilterUsersImpl> get copyWith =>
      __$$FilterUsersImplCopyWithImpl<_$FilterUsersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadUsers,
    required TResult Function(User user) addUser,
    required TResult Function(User user) updateUser,
    required TResult Function(String userId) deleteUser,
    required TResult Function(
            UserRole? role, UserStatus? status, bool? isActive)
        filterUsers,
  }) {
    return filterUsers(role, status, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadUsers,
    TResult? Function(User user)? addUser,
    TResult? Function(User user)? updateUser,
    TResult? Function(String userId)? deleteUser,
    TResult? Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
  }) {
    return filterUsers?.call(role, status, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadUsers,
    TResult Function(User user)? addUser,
    TResult Function(User user)? updateUser,
    TResult Function(String userId)? deleteUser,
    TResult Function(UserRole? role, UserStatus? status, bool? isActive)?
        filterUsers,
    required TResult orElse(),
  }) {
    if (filterUsers != null) {
      return filterUsers(role, status, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadUsers value) loadUsers,
    required TResult Function(AddUser value) addUser,
    required TResult Function(UpdateUser value) updateUser,
    required TResult Function(DeleteUser value) deleteUser,
    required TResult Function(FilterUsers value) filterUsers,
  }) {
    return filterUsers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadUsers value)? loadUsers,
    TResult? Function(AddUser value)? addUser,
    TResult? Function(UpdateUser value)? updateUser,
    TResult? Function(DeleteUser value)? deleteUser,
    TResult? Function(FilterUsers value)? filterUsers,
  }) {
    return filterUsers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadUsers value)? loadUsers,
    TResult Function(AddUser value)? addUser,
    TResult Function(UpdateUser value)? updateUser,
    TResult Function(DeleteUser value)? deleteUser,
    TResult Function(FilterUsers value)? filterUsers,
    required TResult orElse(),
  }) {
    if (filterUsers != null) {
      return filterUsers(this);
    }
    return orElse();
  }
}

abstract class FilterUsers implements UserManagementEvent {
  const factory FilterUsers(
      {final UserRole? role,
      final UserStatus? status,
      final bool? isActive}) = _$FilterUsersImpl;

  UserRole? get role;
  UserStatus? get status;
  bool? get isActive;

  /// Create a copy of UserManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterUsersImplCopyWith<_$FilterUsersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
