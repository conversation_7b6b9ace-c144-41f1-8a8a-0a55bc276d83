import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/refund.dart';

part 'refund_event.freezed.dart';

@freezed
class RefundEvent with _$RefundEvent {
  const factory RefundEvent.loadRefunds() = LoadRefunds;
  const factory RefundEvent.createRefund(Refund refund) = CreateRefund;
  const factory RefundEvent.updateRefund(Refund refund) = UpdateRefund;
  const factory RefundEvent.deleteRefund(String id) = DeleteRefund;
  const factory RefundEvent.approveRefund(String id, String approvedBy) =
      ApproveRefund;
  const factory RefundEvent.rejectRefund(
    String id,
    String rejectedBy,
    String rejectionReason,
  ) = RejectRefund;
  const factory RefundEvent.processRefund(
    String id,
    String processedBy,
    String transactionId,
  ) = ProcessRefund;
  const factory RefundEvent.createDispute(
    String id,
    String disputeReason,
  ) = CreateDispute;
  const factory RefundEvent.resolveDispute(
    String id,
    String resolvedBy,
    String resolution,
  ) = ResolveDispute;
  const factory RefundEvent.updatePartialRefund(
    String id,
    double partialAmount,
    String partialReason,
  ) = UpdatePartialRefund;
  const factory RefundEvent.updateRefundMethod(
    String id,
    Map<String, dynamic> method,
  ) = UpdateRefundMethod;
  const factory RefundEvent.updateTrackingInfo(
    String id,
    Map<String, dynamic> tracking,
  ) = UpdateTrackingInfo;
  const factory RefundEvent.addAuditLog(
    String id,
    String action,
    String performedBy,
    Map<String, dynamic> details,
  ) = AddAuditLog;
}
