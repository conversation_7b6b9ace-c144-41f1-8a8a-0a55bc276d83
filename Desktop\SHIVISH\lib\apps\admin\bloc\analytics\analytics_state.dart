import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/analytics/analytics_model.dart';
import 'package:shivish/apps/admin/widgets/analytics_filter_dialog.dart';

part 'analytics_state.freezed.dart';

@freezed
class AnalyticsState with _$AnalyticsState {
  const factory AnalyticsState.initial() = AnalyticsInitial;
  const factory AnalyticsState.loading() = AnalyticsLoading;
  const factory AnalyticsState.loaded(
    AnalyticsData data, {
    @Default('week') String timeRange,
    AnalyticsFilterData? filters,
  }) = AnalyticsLoaded;
  const factory AnalyticsState.error(String message) = AnalyticsError;
}
