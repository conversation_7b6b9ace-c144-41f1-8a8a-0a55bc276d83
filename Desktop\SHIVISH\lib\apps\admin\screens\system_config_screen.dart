import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/providers/system_config_provider.dart';
import '../widgets/security_config_section.dart';
import '../widgets/backup_config_section.dart';
import '../widgets/refund_config_section.dart';
import '../widgets/ai_config_section.dart';
import '../widgets/voice_command_config_section.dart';
import '../widgets/chatbot_config_section.dart';
import 'package:flutter/foundation.dart';
import '../../../shared/core/service_locator.dart';
import '../../../shared/services/system_config_service.dart';

class SystemConfigScreen extends ConsumerWidget {
  const SystemConfigScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final configAsync = ref.watch(systemConfigProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('System Configuration'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh the configuration
              ref.invalidate(systemConfigProvider);
            },
            tooltip: 'Refresh configuration',
          ),
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: () {
                // Check if the service is registered
                final isRegistered = serviceLocator.isRegistered<SystemConfigService>();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('SystemConfigService registered: $isRegistered'),
                    duration: const Duration(seconds: 3),
                  ),
                );

                // Try to get the service
                try {
                  final service = serviceLocator<SystemConfigService>();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Service instance: ${service.hashCode}'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error getting service: $e'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              },
              tooltip: 'Debug',
            ),
        ],
      ),
      body: configAsync.when(
        data: (config) {
          if (config == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'No configuration found',
                    style: TextStyle(fontSize: 18),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // Refresh the configuration
                      ref.invalidate(systemConfigProvider);
                    },
                    child: const Text('Refresh'),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      // Create a default configuration
                      try {
                        final service = ref.read(systemConfigServiceProvider);
                        final defaultConfig = await service.getConfig();

                        // Check if the widget is still mounted before using context
                        if (!context.mounted) return;

                        if (defaultConfig != null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Default configuration created successfully'),
                              backgroundColor: Colors.green,
                            ),
                          );
                          // Refresh the configuration
                          ref.invalidate(systemConfigProvider);
                        }
                      } catch (e) {
                        // Check if the widget is still mounted before using context
                        if (!context.mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Error creating default configuration: $e'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                    child: const Text('Create Default Configuration'),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SecurityConfigSection(config: config.security),
                const SizedBox(height: 24),
                BackupConfigSection(config: config.backup),
                const SizedBox(height: 24),
                RefundConfigSection(config: config.refund),
                const SizedBox(height: 24),
                AIConfigSection(config: config.ai),
                const SizedBox(height: 24),
                VoiceCommandConfigSection(config: config.voiceCommand),
                const SizedBox(height: 24),
                ChatbotConfigSection(config: config.chatbot),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 60,
              ),
              const SizedBox(height: 16),
              Text(
                'Error loading configuration:',
                style: const TextStyle(
                  color: Colors.red,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error.toString(),
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  // Refresh the configuration
                  ref.invalidate(systemConfigProvider);
                },
                child: const Text('Try Again'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
