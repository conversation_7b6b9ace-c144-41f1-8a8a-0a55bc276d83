import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/models/user.dart';

@injectable
class UserService {
  final FirebaseFirestore _firestore;

  UserService(this._firestore);

  CollectionReference<Map<String, dynamic>> get _usersCollection =>
      _firestore.collection('users');

  Future<List<User>> getUsers() async {
    try {
      final snapshot = await _usersCollection.get();
      return snapshot.docs
          .map((doc) => User.fromJson({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to load users: $e');
    }
  }

  Future<User> addUser(User user) async {
    try {
      final docRef = await _usersCollection.add(user.toJson());
      return user.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('Failed to add user: $e');
    }
  }

  Future<User> updateUser(User user) async {
    try {
      await _usersCollection.doc(user.id).update(user.toJson());
      return user;
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  Future<void> deleteUser(String userId) async {
    try {
      await _usersCollection.doc(userId).delete();
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }
}
