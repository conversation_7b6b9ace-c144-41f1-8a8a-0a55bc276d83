import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/services/ai_settings_service.dart';
import 'package:shivish/apps/admin/bloc/ai_settings/ai_settings_event.dart';
import 'package:shivish/apps/admin/bloc/ai_settings/ai_settings_state.dart';

class AISettingsBloc extends Bloc<AISettingsEvent, AISettingsState> {
  final AISettingsService _aiSettingsService;

  AISettingsBloc(this._aiSettingsService)
      : super(const AISettingsState.initial()) {
    on<LoadAISettings>(_onLoadAISettings);
    on<CreateAISettings>(_onCreateAISettings);
    on<UpdateAISettings>(_onUpdateAISettings);
    on<DeleteAISettings>(_onDeleteAISettings);
    on<UpdateVoiceCommandSettings>(_onUpdateVoiceCommandSettings);
    on<UpdateChatbotSettings>(_onUpdateChatbotSettings);
    on<UpdateLanguageModelSettings>(_onUpdateLanguageModelSettings);
    on<UpdateResponseCustomization>(_onUpdateResponseCustomization);
    on<UpdateVoiceRecognitionSettings>(_onUpdateVoiceRecognitionSettings);
  }

  Future<void> _onLoadAISettings(
    LoadAISettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onCreateAISettings(
    CreateAISettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.createAISettings(event.settings);
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateAISettings(
    UpdateAISettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateAISettings(event.settings);
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onDeleteAISettings(
    DeleteAISettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.deleteAISettings(event.id);
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateVoiceCommandSettings(
    UpdateVoiceCommandSettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateVoiceCommandSettings(
        event.id,
        event.settings,
      );
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateChatbotSettings(
    UpdateChatbotSettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateChatbotSettings(
        event.id,
        event.settings,
      );
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateLanguageModelSettings(
    UpdateLanguageModelSettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateLanguageModelSettings(
        event.id,
        event.settings,
      );
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateResponseCustomization(
    UpdateResponseCustomization event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateResponseCustomization(
        event.id,
        event.settings,
      );
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateVoiceRecognitionSettings(
    UpdateVoiceRecognitionSettings event,
    Emitter<AISettingsState> emit,
  ) async {
    try {
      emit(const AISettingsState.loading());
      await _aiSettingsService.updateVoiceRecognitionSettings(
        event.id,
        event.settings,
      );
      final settings = await _aiSettingsService.getAISettings();
      emit(AISettingsState.loaded(settings));
    } catch (e) {
      emit(AISettingsState.error(e.toString()));
    }
  }
}
