import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/alarm/tone_model.dart';
import '../../../shared/providers/tone_provider.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';
import '../../../shared/utils/logger.dart';
import '../widgets/tone_creation_drawer.dart';

class ToneManagementScreen extends ConsumerStatefulWidget {
  const ToneManagementScreen({super.key});

  @override
  ConsumerState<ToneManagementScreen> createState() =>
      _ToneManagementScreenState();
}

class _ToneManagementScreenState extends ConsumerState<ToneManagementScreen> {
  ToneStatus _selectedStatus = ToneStatus.pending;
  String _selectedLanguage = 'all';
  bool _showOnlyMultiple = false;

  final List<Map<String, String>> _languages = [
    {'code': 'all', 'name': 'All Languages'},
    {'code': 'en', 'name': 'English'},
    {'code': 'hi', 'name': 'Hindi'},
    {'code': 'te', 'name': 'Telugu'},
    {'code': 'ta', 'name': 'Tamil'},
    {'code': 'kn', 'name': 'Kannada'},
  ];

  @override
  Widget build(BuildContext context) {
    final tonesAsync = ref.watch(tonesProvider);
    final user = ref.watch(authProvider);
    if (user == null) {
      return const Center(child: Text('Please sign in to manage tones'));
    }

    return Scaffold(
        appBar: AppBar(
          title: const Text('Tone Management'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.invalidate(tonesProvider);
              },
            ),
          ],
        ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showToneCreationDrawer();
        },
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          _buildStatusFilter(),
          Expanded(
            child: tonesAsync.when(
              data: (tones) {
                // Filter tones based on selected criteria
                var filteredTones = tones.where((tone) {
                  // Filter by status
                  if (tone.status != _selectedStatus) {
                    return false;
                  }

                  // Filter by language
                  if (_selectedLanguage != 'all' &&
                      tone.language != _selectedLanguage) {
                    return false;
                  }

                  // Filter by multiple/day-wise tones
                  if (_showOnlyMultiple && !tone.isMultiple) {
                    return false;
                  }

                  return true;
                }).toList();

                if (filteredTones.isEmpty) {
                  return Center(
                    child: Text('No ${_selectedStatus.name} tones'),
                  );
                }

                return ListView.builder(
                  itemCount: filteredTones.length,
                  itemBuilder: (context, index) {
                    final tone = filteredTones[index];
                    return _ToneCard(
                      tone: tone,
                      onApprove: _selectedStatus == ToneStatus.pending
                          ? () => _approveTone(tone.id, user.uid)
                          : null,
                      onReject: _selectedStatus == ToneStatus.pending
                          ? () => _showRejectDialog(tone.id, user.uid)
                          : null,
                    );
                  },
                );
              },
              loading: () => const Center(child: LoadingIndicator()),
              error: (error, stack) {
                getLogger('ToneManagementScreen').error('Error loading tones: $error', error, stack);
                return Center(
                  child: ErrorMessage(
                    message: 'Failed to load tones. Please try again.',
                    onRetry: () => ref.invalidate(tonesProvider),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filter Tones',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatusChip(ToneStatus.pending, 'Pending'),
              _buildStatusChip(ToneStatus.approved, 'Approved'),
              _buildStatusChip(ToneStatus.rejected, 'Rejected'),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedLanguage,
                  decoration: const InputDecoration(
                    labelText: 'Language',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  items: _languages.map((language) {
                    return DropdownMenuItem<String>(
                      value: language['code'],
                      child: Text(language['name']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedLanguage = value;
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: SwitchListTile(
                  title: const Text('Day-wise Tones'),
                  value: _showOnlyMultiple,
                  onChanged: (value) {
                    setState(() {
                      _showOnlyMultiple = value;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(ToneStatus status, String label) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedStatus = status;
          });
        }
      },
    );
  }

  Future<void> _approveTone(String toneId, String adminId) async {
    try {
      await ref.read(approveToneProvider(
        toneId: toneId,
        adminId: adminId,
      ).future);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tone approved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to approve tone: $e')),
        );
      }
    }
  }

  Future<void> _showRejectDialog(String toneId, String adminId) async {
    final reasonController = TextEditingController();

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Tone'),
        content: TextField(
          controller: reasonController,
          decoration: const InputDecoration(
            labelText: 'Rejection Reason',
            hintText: 'Enter reason for rejection',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              if (reasonController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a rejection reason'),
                  ),
                );
                return;
              }

              try {
                await ref.read(rejectToneProvider(
                  toneId: toneId,
                  adminId: adminId,
                  reason: reasonController.text.trim(),
                ).future);
                if (mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Tone rejected successfully')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to reject tone: $e')),
                  );
                }
              }
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _showToneCreationDrawer() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ToneCreationDrawer(
          onToneCreated: () {
            // Refresh the tones list after a new tone is created
            ref.invalidate(tonesProvider);
          },
        ),
      ),
    );
  }
}

class _ToneCard extends StatelessWidget {
  final ToneModel tone;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;

  const _ToneCard({
    required this.tone,
    this.onApprove,
    this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    tone.name,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(tone.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tone.status.name.toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Category: ${tone.category}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                  ),
            ),
            Text(
              'Uploaded by: ${tone.uploadedBy}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Language: ${tone.language}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Type: ${tone.isMultiple ? 'Multiple (Day-wise)' : 'Single'}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (tone.isMultiple && tone.daysOfWeek.isNotEmpty)
              Text(
                'Days: ${tone.daysOfWeek.join(', ')}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            Text(
              'Duration: ${tone.duration} seconds',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (tone.rejectionReason != null) ...[
              const SizedBox(height: 8),
              Text(
                'Rejection Reason: ${tone.rejectionReason}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
              ),
            ],
            if (onApprove != null || onReject != null) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (onReject != null) ...[
                    TextButton(
                      onPressed: onReject,
                      child: const Text('Reject'),
                    ),
                    const SizedBox(width: 8),
                  ],
                  if (onApprove != null)
                    ElevatedButton(
                      onPressed: onApprove,
                      child: const Text('Approve'),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(ToneStatus status) {
    switch (status) {
      case ToneStatus.pending:
        return Colors.orange;
      case ToneStatus.approved:
        return Colors.green;
      case ToneStatus.rejected:
        return Colors.red;
    }
  }
}
