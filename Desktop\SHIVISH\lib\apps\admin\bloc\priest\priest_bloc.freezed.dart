// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'priest_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PriestEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriestEventCopyWith<$Res> {
  factory $PriestEventCopyWith(
          PriestEvent value, $Res Function(PriestEvent) then) =
      _$PriestEventCopyWithImpl<$Res, PriestEvent>;
}

/// @nodoc
class _$PriestEventCopyWithImpl<$Res, $Val extends PriestEvent>
    implements $PriestEventCopyWith<$Res> {
  _$PriestEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadPriestsImplCopyWith<$Res> {
  factory _$$LoadPriestsImplCopyWith(
          _$LoadPriestsImpl value, $Res Function(_$LoadPriestsImpl) then) =
      __$$LoadPriestsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadPriestsImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$LoadPriestsImpl>
    implements _$$LoadPriestsImplCopyWith<$Res> {
  __$$LoadPriestsImplCopyWithImpl(
      _$LoadPriestsImpl _value, $Res Function(_$LoadPriestsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadPriestsImpl implements _LoadPriests {
  const _$LoadPriestsImpl();

  @override
  String toString() {
    return 'PriestEvent.loadPriests()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadPriestsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadPriests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadPriests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadPriests != null) {
      return loadPriests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadPriests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadPriests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadPriests != null) {
      return loadPriests(this);
    }
    return orElse();
  }
}

abstract class _LoadPriests implements PriestEvent {
  const factory _LoadPriests() = _$LoadPriestsImpl;
}

/// @nodoc
abstract class _$$LoadMorePriestsImplCopyWith<$Res> {
  factory _$$LoadMorePriestsImplCopyWith(_$LoadMorePriestsImpl value,
          $Res Function(_$LoadMorePriestsImpl) then) =
      __$$LoadMorePriestsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMorePriestsImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$LoadMorePriestsImpl>
    implements _$$LoadMorePriestsImplCopyWith<$Res> {
  __$$LoadMorePriestsImplCopyWithImpl(
      _$LoadMorePriestsImpl _value, $Res Function(_$LoadMorePriestsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMorePriestsImpl implements _LoadMorePriests {
  const _$LoadMorePriestsImpl();

  @override
  String toString() {
    return 'PriestEvent.loadMorePriests()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMorePriestsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadMorePriests();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadMorePriests?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMorePriests != null) {
      return loadMorePriests();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadMorePriests(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadMorePriests?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMorePriests != null) {
      return loadMorePriests(this);
    }
    return orElse();
  }
}

abstract class _LoadMorePriests implements PriestEvent {
  const factory _LoadMorePriests() = _$LoadMorePriestsImpl;
}

/// @nodoc
abstract class _$$UpdatePriestStatusImplCopyWith<$Res> {
  factory _$$UpdatePriestStatusImplCopyWith(_$UpdatePriestStatusImpl value,
          $Res Function(_$UpdatePriestStatusImpl) then) =
      __$$UpdatePriestStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, bool isActive});
}

/// @nodoc
class __$$UpdatePriestStatusImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$UpdatePriestStatusImpl>
    implements _$$UpdatePriestStatusImplCopyWith<$Res> {
  __$$UpdatePriestStatusImplCopyWithImpl(_$UpdatePriestStatusImpl _value,
      $Res Function(_$UpdatePriestStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isActive = null,
  }) {
    return _then(_$UpdatePriestStatusImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UpdatePriestStatusImpl implements _UpdatePriestStatus {
  const _$UpdatePriestStatusImpl({required this.id, required this.isActive});

  @override
  final String id;
  @override
  final bool isActive;

  @override
  String toString() {
    return 'PriestEvent.updatePriestStatus(id: $id, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePriestStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, isActive);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePriestStatusImplCopyWith<_$UpdatePriestStatusImpl> get copyWith =>
      __$$UpdatePriestStatusImplCopyWithImpl<_$UpdatePriestStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updatePriestStatus(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updatePriestStatus?.call(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updatePriestStatus != null) {
      return updatePriestStatus(id, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updatePriestStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updatePriestStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updatePriestStatus != null) {
      return updatePriestStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdatePriestStatus implements PriestEvent {
  const factory _UpdatePriestStatus(
      {required final String id,
      required final bool isActive}) = _$UpdatePriestStatusImpl;

  String get id;
  bool get isActive;

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePriestStatusImplCopyWith<_$UpdatePriestStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePriestVerificationImplCopyWith<$Res> {
  factory _$$UpdatePriestVerificationImplCopyWith(
          _$UpdatePriestVerificationImpl value,
          $Res Function(_$UpdatePriestVerificationImpl) then) =
      __$$UpdatePriestVerificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String id,
      bool isVerified,
      String verificationStatus,
      String? verificationNotes});
}

/// @nodoc
class __$$UpdatePriestVerificationImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$UpdatePriestVerificationImpl>
    implements _$$UpdatePriestVerificationImplCopyWith<$Res> {
  __$$UpdatePriestVerificationImplCopyWithImpl(
      _$UpdatePriestVerificationImpl _value,
      $Res Function(_$UpdatePriestVerificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isVerified = null,
    Object? verificationStatus = null,
    Object? verificationNotes = freezed,
  }) {
    return _then(_$UpdatePriestVerificationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      verificationStatus: null == verificationStatus
          ? _value.verificationStatus
          : verificationStatus // ignore: cast_nullable_to_non_nullable
              as String,
      verificationNotes: freezed == verificationNotes
          ? _value.verificationNotes
          : verificationNotes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$UpdatePriestVerificationImpl implements _UpdatePriestVerification {
  const _$UpdatePriestVerificationImpl(
      {required this.id,
      required this.isVerified,
      required this.verificationStatus,
      this.verificationNotes});

  @override
  final String id;
  @override
  final bool isVerified;
  @override
  final String verificationStatus;
  @override
  final String? verificationNotes;

  @override
  String toString() {
    return 'PriestEvent.updatePriestVerification(id: $id, isVerified: $isVerified, verificationStatus: $verificationStatus, verificationNotes: $verificationNotes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePriestVerificationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.verificationStatus, verificationStatus) ||
                other.verificationStatus == verificationStatus) &&
            (identical(other.verificationNotes, verificationNotes) ||
                other.verificationNotes == verificationNotes));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, isVerified, verificationStatus, verificationNotes);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePriestVerificationImplCopyWith<_$UpdatePriestVerificationImpl>
      get copyWith => __$$UpdatePriestVerificationImplCopyWithImpl<
          _$UpdatePriestVerificationImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updatePriestVerification(
        id, isVerified, verificationStatus, verificationNotes);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updatePriestVerification?.call(
        id, isVerified, verificationStatus, verificationNotes);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updatePriestVerification != null) {
      return updatePriestVerification(
          id, isVerified, verificationStatus, verificationNotes);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updatePriestVerification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updatePriestVerification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updatePriestVerification != null) {
      return updatePriestVerification(this);
    }
    return orElse();
  }
}

abstract class _UpdatePriestVerification implements PriestEvent {
  const factory _UpdatePriestVerification(
      {required final String id,
      required final bool isVerified,
      required final String verificationStatus,
      final String? verificationNotes}) = _$UpdatePriestVerificationImpl;

  String get id;
  bool get isVerified;
  String get verificationStatus;
  String? get verificationNotes;

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePriestVerificationImplCopyWith<_$UpdatePriestVerificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletePriestImplCopyWith<$Res> {
  factory _$$DeletePriestImplCopyWith(
          _$DeletePriestImpl value, $Res Function(_$DeletePriestImpl) then) =
      __$$DeletePriestImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeletePriestImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$DeletePriestImpl>
    implements _$$DeletePriestImplCopyWith<$Res> {
  __$$DeletePriestImplCopyWithImpl(
      _$DeletePriestImpl _value, $Res Function(_$DeletePriestImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeletePriestImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeletePriestImpl implements _DeletePriest {
  const _$DeletePriestImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'PriestEvent.deletePriest(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletePriestImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletePriestImplCopyWith<_$DeletePriestImpl> get copyWith =>
      __$$DeletePriestImplCopyWithImpl<_$DeletePriestImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return deletePriest(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return deletePriest?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deletePriest != null) {
      return deletePriest(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return deletePriest(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return deletePriest?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deletePriest != null) {
      return deletePriest(this);
    }
    return orElse();
  }
}

abstract class _DeletePriest implements PriestEvent {
  const factory _DeletePriest({required final String id}) = _$DeletePriestImpl;

  String get id;

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletePriestImplCopyWith<_$DeletePriestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StartRealtimeUpdatesImplCopyWith(_$StartRealtimeUpdatesImpl value,
          $Res Function(_$StartRealtimeUpdatesImpl) then) =
      __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$StartRealtimeUpdatesImpl>
    implements _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  __$$StartRealtimeUpdatesImplCopyWithImpl(_$StartRealtimeUpdatesImpl _value,
      $Res Function(_$StartRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartRealtimeUpdatesImpl implements _StartRealtimeUpdates {
  const _$StartRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'PriestEvent.startRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class _StartRealtimeUpdates implements PriestEvent {
  const factory _StartRealtimeUpdates() = _$StartRealtimeUpdatesImpl;
}

/// @nodoc
abstract class _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StopRealtimeUpdatesImplCopyWith(_$StopRealtimeUpdatesImpl value,
          $Res Function(_$StopRealtimeUpdatesImpl) then) =
      __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$PriestEventCopyWithImpl<$Res, _$StopRealtimeUpdatesImpl>
    implements _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  __$$StopRealtimeUpdatesImplCopyWithImpl(_$StopRealtimeUpdatesImpl _value,
      $Res Function(_$StopRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopRealtimeUpdatesImpl implements _StopRealtimeUpdates {
  const _$StopRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'PriestEvent.stopRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StopRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPriests,
    required TResult Function() loadMorePriests,
    required TResult Function(String id, bool isActive) updatePriestStatus,
    required TResult Function(String id, bool isVerified,
            String verificationStatus, String? verificationNotes)
        updatePriestVerification,
    required TResult Function(String id) deletePriest,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPriests,
    TResult? Function()? loadMorePriests,
    TResult? Function(String id, bool isActive)? updatePriestStatus,
    TResult? Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult? Function(String id)? deletePriest,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPriests,
    TResult Function()? loadMorePriests,
    TResult Function(String id, bool isActive)? updatePriestStatus,
    TResult Function(String id, bool isVerified, String verificationStatus,
            String? verificationNotes)?
        updatePriestVerification,
    TResult Function(String id)? deletePriest,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadPriests value) loadPriests,
    required TResult Function(_LoadMorePriests value) loadMorePriests,
    required TResult Function(_UpdatePriestStatus value) updatePriestStatus,
    required TResult Function(_UpdatePriestVerification value)
        updatePriestVerification,
    required TResult Function(_DeletePriest value) deletePriest,
    required TResult Function(_StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(_StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadPriests value)? loadPriests,
    TResult? Function(_LoadMorePriests value)? loadMorePriests,
    TResult? Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult? Function(_UpdatePriestVerification value)?
        updatePriestVerification,
    TResult? Function(_DeletePriest value)? deletePriest,
    TResult? Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadPriests value)? loadPriests,
    TResult Function(_LoadMorePriests value)? loadMorePriests,
    TResult Function(_UpdatePriestStatus value)? updatePriestStatus,
    TResult Function(_UpdatePriestVerification value)? updatePriestVerification,
    TResult Function(_DeletePriest value)? deletePriest,
    TResult Function(_StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(_StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class _StopRealtimeUpdates implements PriestEvent {
  const factory _StopRealtimeUpdates() = _$StopRealtimeUpdatesImpl;
}

/// @nodoc
mixin _$PriestState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Priest> priests) loaded,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Priest> priests)? loaded,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Priest> priests)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriestStateCopyWith<$Res> {
  factory $PriestStateCopyWith(
          PriestState value, $Res Function(PriestState) then) =
      _$PriestStateCopyWithImpl<$Res, PriestState>;
}

/// @nodoc
class _$PriestStateCopyWithImpl<$Res, $Val extends PriestState>
    implements $PriestStateCopyWith<$Res> {
  _$PriestStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$PriestStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'PriestState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Priest> priests) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Priest> priests)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Priest> priests)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements PriestState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$PriestStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'PriestState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Priest> priests) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Priest> priests)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Priest> priests)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements PriestState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Priest> priests});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$PriestStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priests = null,
  }) {
    return _then(_$LoadedImpl(
      null == priests
          ? _value._priests
          : priests // ignore: cast_nullable_to_non_nullable
              as List<Priest>,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements _Loaded {
  const _$LoadedImpl(final List<Priest> priests) : _priests = priests;

  final List<Priest> _priests;
  @override
  List<Priest> get priests {
    if (_priests is EqualUnmodifiableListView) return _priests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_priests);
  }

  @override
  String toString() {
    return 'PriestState.loaded(priests: $priests)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._priests, _priests));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_priests));

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Priest> priests) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(priests);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Priest> priests)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(priests);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Priest> priests)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(priests);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class _Loaded implements PriestState {
  const factory _Loaded(final List<Priest> priests) = _$LoadedImpl;

  List<Priest> get priests;

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$PriestStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'PriestState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<Priest> priests) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<Priest> priests)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<Priest> priests)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements PriestState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of PriestState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
