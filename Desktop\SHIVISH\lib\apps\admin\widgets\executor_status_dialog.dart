import 'package:flutter/material.dart';
import '../../../../shared/models/executor.dart';

class ExecutorStatusDialog extends StatefulWidget {
  final Executor executor;

  const ExecutorStatusDialog({
    super.key,
    required this.executor,
  });

  @override
  State<ExecutorStatusDialog> createState() => _ExecutorStatusDialogState();
}

class _ExecutorStatusDialogState extends State<ExecutorStatusDialog> {
  late String _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.executor.status;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Update Executor Status'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Current Status: ${widget.executor.status}'),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedStatus,
            decoration: const InputDecoration(
              labelText: 'New Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: 'active',
                child: Text('Active'),
              ),
              DropdownMenuItem(
                value: 'inactive',
                child: Text('Inactive'),
              ),
              DropdownMenuItem(
                value: 'suspended',
                child: Text('Suspended'),
              ),
              DropdownMenuItem(
                value: 'pending',
                child: Text('Pending'),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedStatus = value;
                });
              }
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () => Navigator.pop(context, _selectedStatus),
          child: const Text('Update'),
        ),
      ],
    );
  }
}
