import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/event_product_model.dart';

/// Dialog for searching and selecting products
class ProductSearchDialog extends ConsumerStatefulWidget {
  /// Creates a [ProductSearchDialog]
  const ProductSearchDialog({
    this.selectedProducts = const [],
    super.key,
  });

  /// The currently selected products
  final List<EventProduct> selectedProducts;

  @override
  ConsumerState<ProductSearchDialog> createState() =>
      _ProductSearchDialogState();
}

class _ProductSearchDialogState extends ConsumerState<ProductSearchDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<EventProduct> _selectedProducts = [];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _selectedProducts = List.from(widget.selectedProducts);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final productsAsync = ref.watch(productsProvider);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Products',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Search and select products to add to your list',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 200),
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search products',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            // Selected products count
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Selected: ${_selectedProducts.length} products',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Product list
            Expanded(
              child: productsAsync.when(
                data: (products) {
                  // Filter by search query
                  final filteredProducts = products.where((product) {
                    if (_searchQuery.isEmpty) return true;
                    return product.name
                            .toLowerCase()
                            .contains(_searchQuery.toLowerCase()) ||
                        (product.description?.toLowerCase() ?? '')
                            .contains(_searchQuery.toLowerCase());
                  }).toList();

                  if (filteredProducts.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: theme.colorScheme.primary
                                .withValues(alpha: 128),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No products found',
                            style: theme.textTheme.titleMedium,
                          ),
                        ],
                      ),
                    );
                  }

                  return ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = filteredProducts[index];
                      final isSelected = _isProductSelected(product.id);

                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: CheckboxListTile(
                          value: isSelected,
                          onChanged: (value) {
                            if (value == true) {
                              _addProduct(product);
                            } else {
                              _removeProduct(product.id);
                            }
                          },
                          title: Text(product.name),
                          subtitle: Text(
                            product.description ?? 'No description',
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          secondary: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: product.imageUrl != null
                                  ? Image.network(
                                      product.imageUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error,
                                              stackTrace) =>
                                          const Icon(Icons.image_not_supported),
                                    )
                                  : const Icon(Icons.shopping_cart),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stackTrace) => Center(
                  child: Text('Error loading products: $error'),
                ),
              ),
            ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () =>
                        Navigator.of(context).pop(_selectedProducts),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Done'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isProductSelected(String productId) {
    return _selectedProducts.any((p) => p.id == productId);
  }

  void _addProduct(EventProduct product) {
    if (!_isProductSelected(product.id)) {
      setState(() {
        _selectedProducts.add(
          EventProduct(
            id: product.id,
            name: product.name,
            description: product.description,
            price: product.price,
            imageUrl: product.imageUrl,
          ),
        );
      });
    }
  }

  void _removeProduct(String productId) {
    setState(() {
      _selectedProducts.removeWhere((p) => p.id == productId);
    });
  }
}

/// Provider for products
final productsProvider = FutureProvider<List<EventProduct>>((ref) async {
  // This would typically fetch products from a service
  // For now, we'll return a mock list
  await Future.delayed(const Duration(milliseconds: 500));
  return [
    EventProduct(
      id: '1',
      name: 'Rice',
      description: 'Premium quality basmati rice',
      price: 120.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '2',
      name: 'Wheat Flour',
      description: 'Organic whole wheat flour',
      price: 45.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '3',
      name: 'Sugar',
      description: 'Refined white sugar',
      price: 40.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '4',
      name: 'Cooking Oil',
      description: 'Pure sunflower oil',
      price: 110.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '5',
      name: 'Milk',
      description: 'Fresh cow milk',
      price: 25.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '6',
      name: 'Ghee',
      description: 'Pure cow ghee',
      price: 450.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '7',
      name: 'Turmeric Powder',
      description: 'Organic turmeric powder',
      price: 60.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '8',
      name: 'Red Chilli Powder',
      description: 'Spicy red chilli powder',
      price: 70.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '9',
      name: 'Coriander Powder',
      description: 'Aromatic coriander powder',
      price: 55.0,
      imageUrl: null,
    ),
    EventProduct(
      id: '10',
      name: 'Garam Masala',
      description: 'Blend of aromatic spices',
      price: 85.0,
      imageUrl: null,
    ),
  ];
});
