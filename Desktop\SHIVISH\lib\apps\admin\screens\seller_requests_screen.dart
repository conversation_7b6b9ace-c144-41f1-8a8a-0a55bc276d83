import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../bloc/auth/auth_bloc.dart';

class SellerRequestsScreen extends StatefulWidget {
  const SellerRequestsScreen({super.key});

  @override
  State<SellerRequestsScreen> createState() => _SellerRequestsScreenState();
}

class _SellerRequestsScreenState extends State<SellerRequestsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  List<Map<String, dynamic>> _sellerRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSellerRequests();
  }

  Future<void> _loadSellerRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Query sellers collection directly for unapproved sellers
      final snapshot = await _firestore
          .collection('sellers')
          .where('isApproved', isEqualTo: false)
          .get();

      final requests = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      debugPrint('Found ${requests.length} pending seller requests');

      setState(() {
        _sellerRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading seller requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approveSeller(String sellerId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get current user info for approval tracking
      final currentUser = FirebaseAuth.instance.currentUser;
      final userDoc = currentUser != null
          ? await _firestore.collection('users').doc(currentUser.uid).get()
          : null;

      final approverRole = userDoc?.data()?['role'] ?? 'unknown';
      final approverName = userDoc?.data()?['displayName'] ?? currentUser?.displayName ?? 'Unknown';

      // Update seller document to mark as approved
      await _firestore.collection('sellers').doc(sellerId).update({
        'isApproved': true,
        'isActive': true,
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': currentUser?.uid ?? 'unknown',
        'approverRole': approverRole,
        'approverName': approverName,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user document to grant seller access
      try {
        await _firestore.collection('users').doc(sellerId).update({
          'isApproved': true,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        debugPrint('Updated user document for seller $sellerId');
      } catch (e) {
        debugPrint('Error updating user document: $e');
        // Continue even if this fails
      }

      // Reload the list
      await _loadSellerRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller approved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving seller: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectSeller(String sellerId) async {
    // Get auth state before async operation
    final authState = context.read<AuthBloc>().state;
    final userId = authState is AuthAuthenticatedState
        ? authState.user.uid
        : FirebaseAuth.instance.currentUser?.uid ?? 'unknown';

    final reasonController = TextEditingController();

    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Seller'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Update seller document to mark as rejected
      await _firestore.collection('sellers').doc(sellerId).update({
        'isApproved': false,
        'isActive': false,
        'rejectionReason': reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': userId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user document if needed
      try {
        await _firestore.collection('users').doc(sellerId).update({
          'isApproved': false,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        debugPrint('Updated user document for seller $sellerId');
      } catch (e) {
        debugPrint('Error updating user document: $e');
        // Continue even if this fails
      }

      // Reload the list
      await _loadSellerRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller rejected successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting seller: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSellerRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadSellerRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _sellerRequests.isEmpty
                  ? const Center(
                      child: Text('No pending seller requests'),
                    )
                  : ListView.builder(
                      itemCount: _sellerRequests.length,
                      itemBuilder: (context, index) {
                        final request = _sellerRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['businessName'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phoneNumber'] != null)
                                  Text('Phone: ${request['phoneNumber']}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Created: ${_formatTimestamp(request['createdAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                if (request['businessDescription'] != null && request['businessDescription'].toString().isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text('Description: ${request['businessDescription']}'),
                                  ),
                                if (request['category'] != null)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text('Category: ${request['category']}'),
                                  ),
                                if (request['businessDocuments'] != null && (request['businessDocuments'] as List).isNotEmpty)
                                  _buildDocumentsList(request['businessDocuments'] as List),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectSeller(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveSeller(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  Widget _buildDocumentsList(List documents) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Text(
          'Documents:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...documents.map((doc) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              const Icon(Icons.description, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${doc['type']}: ${doc['name'] ?? 'Document'}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is Timestamp) {
      final date = timestamp.toDate();
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    }

    return 'N/A';
  }
}
