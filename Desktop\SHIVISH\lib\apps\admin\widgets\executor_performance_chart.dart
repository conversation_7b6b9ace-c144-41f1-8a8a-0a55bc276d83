import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../shared/models/executor.dart';

class ExecutorPerformanceChart extends StatelessWidget {
  final Executor executor;

  const ExecutorPerformanceChart({
    super.key,
    required this.executor,
  });

  @override
  Widget build(BuildContext context) {
    final metrics = executor.performanceMetrics ?? {};
    final tasksCompleted = metrics['tasksCompleted'] as int? ?? 0;
    final tasksInProgress = metrics['tasksInProgress'] as int? ?? 0;
    final tasksPending = metrics['tasksPending'] as int? ?? 0;
    final totalTasks = tasksCompleted + tasksInProgress + tasksPending;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Overview',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: tasksCompleted.toDouble(),
                      title:
                          '${(tasksCompleted / totalTasks * 100).toStringAsFixed(1)}%',
                      color: Colors.green,
                      radius: 100,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: tasksInProgress.toDouble(),
                      title:
                          '${(tasksInProgress / totalTasks * 100).toStringAsFixed(1)}%',
                      color: Colors.orange,
                      radius: 100,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    PieChartSectionData(
                      value: tasksPending.toDouble(),
                      title:
                          '${(tasksPending / totalTasks * 100).toStringAsFixed(1)}%',
                      color: Colors.red,
                      radius: 100,
                      titleStyle: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildLegendItem('Completed', Colors.green),
                _buildLegendItem('In Progress', Colors.orange),
                _buildLegendItem('Pending', Colors.red),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Text(label),
      ],
    );
  }
}
