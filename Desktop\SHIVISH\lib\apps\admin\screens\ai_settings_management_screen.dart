import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/ai_settings/ai_settings_bloc.dart';
import 'package:shivish/apps/admin/bloc/ai_settings/ai_settings_event.dart';
import 'package:shivish/apps/admin/bloc/ai_settings/ai_settings_state.dart';
import 'package:shivish/shared/models/ai_settings.dart';
import 'package:shivish/shared/ui_components/dialogs/ai_settings_form_dialog.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';

class AISettingsManagementScreen extends StatelessWidget {
  const AISettingsManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Settings Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<AISettingsBloc, AISettingsState>(
        builder: (context, state) {
          return state.when(
            initial: () => const Center(
              child: Text('No AI settings configured yet'),
            ),
            loading: () => const LoadingIndicator(),
            loaded: (settings) => _buildSettingsList(context, settings),
            error: (message) => ErrorMessage(message: message),
          );
        },
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context, List<AISettings> settings) {
    if (settings.isEmpty) {
      return const Center(
        child: Text('No AI settings configured yet'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context
            .read<AISettingsBloc>()
            .add(const AISettingsEvent.loadAISettings());
      },
      child: ListView.builder(
        itemCount: settings.length,
        itemBuilder: (context, index) {
          final setting = settings[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ListTile(
              title: Text(setting.name),
              subtitle: Text(setting.description),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  StatusBadge(
                    isActive: setting.isEnabled,
                    onToggle: () => _showStatusChangeDialog(
                      context,
                      setting,
                      !setting.isEnabled,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _showEditDialog(context, setting),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () => _showDeleteDialog(context, setting),
                  ),
                ],
              ),
              onTap: () => _showEditDialog(context, setting),
            ),
          );
        },
      ),
    );
  }

  Future<void> _showCreateDialog(BuildContext context) async {
    final result = await showDialog<AISettings>(
      context: context,
      builder: (context) => const AISettingsFormDialog(),
    );

    if (result != null && context.mounted) {
      context.read<AISettingsBloc>().add(
            AISettingsEvent.createAISettings(result),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              SuccessMessage(message: 'AI settings created successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _showEditDialog(
      BuildContext context, AISettings settings) async {
    final result = await showDialog<AISettings>(
      context: context,
      builder: (context) => AISettingsFormDialog(settings: settings),
    );

    if (result != null && context.mounted) {
      context.read<AISettingsBloc>().add(
            AISettingsEvent.updateAISettings(result),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              SuccessMessage(message: 'AI settings updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _showStatusChangeDialog(
    BuildContext context,
    AISettings settings,
    bool newStatus,
  ) async {
    final confirmed = await AppDialogs.showConfirm(
      context: context,
      title: 'Change AI Settings Status',
      message: 'Are you sure you want to ${newStatus ? 'enable' : 'disable'} '
          'the AI settings "${settings.name}"?',
      confirmText: 'Yes',
      cancelText: 'No',
    );

    if (confirmed == true && context.mounted) {
      context.read<AISettingsBloc>().add(
            AISettingsEvent.updateAISettings(
              settings.copyWith(isEnabled: newStatus),
            ),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: SuccessMessage(
            message:
                'AI settings ${newStatus ? 'enabled' : 'disabled'} successfully',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _showDeleteDialog(
      BuildContext context, AISettings settings) async {
    final confirmed = await AppDialogs.showConfirm(
      context: context,
      title: 'Delete AI Settings',
      message: 'Are you sure you want to delete the AI settings '
          '"${settings.name}"? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      isDestructive: true,
    );

    if (confirmed == true && context.mounted) {
      context.read<AISettingsBloc>().add(
            AISettingsEvent.deleteAISettings(settings.id),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              SuccessMessage(message: 'AI settings deleted successfully'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
