name: CI/CD Pipeline

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]

jobs:
  analyze:
    name: Static Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.3'
          channel: 'stable'
      - name: Install dependencies
        run: flutter pub get
      - name: Verify formatting
        run: dart format --output=none --set-exit-if-changed .
      - name: Analyze project source
        run: flutter analyze
      - name: Run custom static analysis
        run: flutter pub run custom_lint

  test:
    name: Run Tests
    needs: analyze
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.3'
          channel: 'stable'
      - name: Install dependencies
        run: flutter pub get
      - name: Run unit tests
        run: flutter test --coverage
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: coverage/lcov.info

  build_android:
    name: Build Android
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.3'
          channel: 'stable'
      - name: Setup Android keystore
        run: |
          echo "${{ secrets.KEYSTORE_BASE64 }}" | base64 --decode > android/app/keystore.jks
          echo "storeFile=keystore.jks" > android/key.properties
          echo "storePassword=${{ secrets.STORE_PASSWORD }}" >> android/key.properties
          echo "keyPassword=${{ secrets.KEY_PASSWORD }}" >> android/key.properties
          echo "keyAlias=${{ secrets.KEY_ALIAS }}" >> android/key.properties
      - name: Build APK
        run: flutter build apk --release
      - name: Build App Bundle
        run: flutter build appbundle --release
      - name: Upload APK
        uses: actions/upload-artifact@v3
        with:
          name: release-apk
          path: build/app/outputs/flutter-apk/app-release.apk
      - name: Upload Bundle
        uses: actions/upload-artifact@v3
        with:
          name: release-bundle
          path: build/app/outputs/bundle/release/app-release.aab

  build_ios:
    name: Build iOS
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.3'
          channel: 'stable'
      - name: Install dependencies
        run: flutter pub get
      - name: Setup iOS certificates
        uses: apple-actions/import-codesigning-certs@v1
        with:
          p12-file-base64: ${{ secrets.IOS_P12_BASE64 }}
          p12-password: ${{ secrets.IOS_P12_PASSWORD }}
      - name: Setup provisioning profile
        run: |
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          echo "${{ secrets.IOS_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/profile.mobileprovision
      - name: Build iOS
        run: flutter build ios --release --no-codesign
      - name: Upload IPA
        uses: actions/upload-artifact@v3
        with:
          name: release-ipa
          path: build/ios/ipa/app-release.ipa

  build_web:
    name: Build Web
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.19.3'
          channel: 'stable'
      - name: Install dependencies
        run: flutter pub get
      - name: Build Web
        run: flutter build web --release
      - name: Upload Web Build
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: build/web

  deploy_android:
    name: Deploy Android
    needs: build_android
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v3
        with:
          name: release-bundle
      - name: Upload to Play Store
        uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.PLAY_STORE_SERVICE_ACCOUNT_JSON }}
          packageName: com.shivish.app
          releaseFiles: app-release.aab
          track: internal
          status: completed
          whatsNewDirectory: distribution/whatsnew

  deploy_ios:
    name: Deploy iOS
    needs: build_ios
    if: github.ref == 'refs/heads/main'
    runs-on: macos-latest
    steps:
      - uses: actions/download-artifact@v3
        with:
          name: release-ipa
      - name: Upload to App Store
        uses: apple-actions/upload-testflight-build@v1
        with:
          app-path: app-release.ipa
          issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
          api-key-id: ${{ secrets.APPSTORE_API_KEY_ID }}
          api-private-key: ${{ secrets.APPSTORE_API_PRIVATE_KEY }}

  deploy_web:
    name: Deploy Web
    needs: build_web
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/download-artifact@v3
        with:
          name: web-build
      - name: Deploy to Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
          channelId: live
          projectId: shivish-app 