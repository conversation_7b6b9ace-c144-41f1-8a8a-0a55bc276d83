import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:shivish/shared/core/auth/bloc/auth_bloc.dart';
import 'package:shivish/shared/core/auth/bloc/auth_event.dart';
import 'package:shivish/shared/core/auth/repositories/auth_repository.dart';
import 'package:shivish/shared/core/di/injection.dart';
import 'package:shivish/shared/core/flavor/flavor_manager.dart';
import 'package:shivish/shared/core/service_locator.dart';
import 'package:shivish/shared/screens/flavor_selection_app.dart';
import 'package:shivish/shared/screens/splash_screen.dart';
import 'package:shivish/shared/services/app_initialization_service.dart';
import 'package:shivish/shared/services/analytics/analytics_service.dart';
import 'package:shivish/shared/services/language_service.dart';
import 'package:shivish/shared/services/supabase_initialization_service.dart';
import 'package:shivish/shared/config/environment_config.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'shared/controllers/backup_controller.dart';
import 'package:shivish/apps/admin/admin_app.dart';
import 'package:shivish/apps/buyer/buyer_app.dart';
import 'package:shivish/apps/seller/seller_app.dart';
import 'package:shivish/apps/priest/priest_app.dart';
import 'package:shivish/apps/technician/technician_app.dart';
import 'package:shivish/apps/executor/executor_app.dart';
import 'package:shivish/apps/saviour/saviour_app.dart';
import 'package:shivish/apps/hospital/hospital_app.dart';


// Import app implementations for each flavor

// Providers and AppConfig moved to each flavor's app.dart file

Future<void> main() async {
  await runZonedGuarded(() async {
    try {
      WidgetsFlutterBinding.ensureInitialized();

      // Add debug print for initialization steps
      debugPrint('Starting app initialization...');

      // Detect the app flavor using the FlavorManager
      final appFlavor = await FlavorManager.detectFlavor();

      // If no flavor is detected, show a flavor selection dialog
      if (appFlavor == null) {
        debugPrint('No app flavor detected, will show flavor selection dialog');
        // We'll handle this in the app
      } else {
        debugPrint('Detected app flavor: ${appFlavor.name}');
      }

      // Check initialization status
      await AppInitializationService.performInitializationChecks();

      // Set preferred orientations and status bar appearance
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      // Set status bar to transparent with light icons
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
      ));

      // Set system UI mode to edge-to-edge
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: [SystemUiOverlay.top],
      );

      // Ensure the status bar is transparent
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

      // Initialize Supabase with error handling
      try {
        await EnvironmentConfig.initialize();
        await SupabaseInitializationService.initialize();
        debugPrint('Supabase initialized successfully');
      } catch (e) {
        debugPrint('Supabase initialization failed: $e');
        // Continue without Supabase - don't rethrow the error
      }

      // Initialize Hive
      try {
        await Hive.initFlutter();
        await Hive.openBox('userData');
        await Hive.openBox('settings');
        await Hive.openBox('appState');
        await Hive.openBox('cache');
        debugPrint('Hive initialized successfully');
      } catch (e) {
        debugPrint('Hive initialization failed: $e');
        rethrow;
      }

      // Setup error reporting with analytics
      if (!kDebugMode) {
        try {
          FlutterError.onError = (FlutterErrorDetails details) {
            debugPrint('Flutter Error: ${details.exception}');
            debugPrint('Stack trace: ${details.stack}');

            // Log to analytics for error tracking
            try {
              final analyticsService = serviceLocator<AnalyticsService>();
              analyticsService.logError(
                error: details.exception.toString(),
                type: 'flutter_error',
                stackTrace: details.stack,
              );
            } catch (analyticsError) {
              debugPrint('Failed to log error to analytics: $analyticsError');
            }
          };
          PlatformDispatcher.instance.onError = (error, stack) {
            debugPrint('Platform Error: $error');
            debugPrint('Stack trace: $stack');

            // Log to analytics for error tracking
            try {
              final analyticsService = serviceLocator<AnalyticsService>();
              analyticsService.logError(
                error: error.toString(),
                type: 'platform_error',
                stackTrace: stack,
              );
            } catch (analyticsError) {
              debugPrint('Failed to log error to analytics: $analyticsError');
            }
            return true;
          };
          debugPrint('Error reporting initialized successfully');
        } catch (e) {
          debugPrint('Error reporting initialization failed: $e');
        }
      }

      // Initialize shared preferences with error handling
      try {
        await SharedPreferences.getInstance();
        debugPrint('SharedPreferences initialized successfully');
      } catch (e) {
        debugPrint('SharedPreferences initialization failed: $e');
        rethrow;
      }

      // Setup service locator with error handling
      try {
        await setupServiceLocator();
        debugPrint('Service locator initialized successfully');
      } catch (e) {
        debugPrint('Service locator initialization failed: $e');
        rethrow;
      }

      // Initialize dependencies
      try {
        await initializeDependencies();
        debugPrint('Dependencies initialized successfully');
      } catch (e) {
        debugPrint('Dependencies initialization failed: $e');
        rethrow;
      }

      // Initialize analytics with error handling
      try {
        final analyticsService = serviceLocator<SupabaseAnalyticsService>();
        await analyticsService.logEvent(
          name: 'app_start',
          parameters: {'flavor': appFlavor?.name ?? 'unknown'},
        );
        debugPrint('Analytics initialized successfully');
      } catch (e) {
        debugPrint('Analytics initialization failed: $e');
        // Continue without analytics
      }

      // Initialize language service with error handling
      try {
        final languageService = await LanguageService.initialize();
        serviceLocator.registerSingleton<LanguageService>(languageService);
        debugPrint('Language service initialized successfully');
      } catch (e) {
        debugPrint('Language service initialization failed: $e');
        // Continue without language service
      }

      // Each flavor app will create its own configuration

      // Router initialization moved to each flavor's app.dart file
      debugPrint('Router initialization delegated to flavor-specific apps');

      // Initialize auth bloc
      final authRepository = serviceLocator<AuthRepository>();
      // Create and initialize auth bloc, but don't store the reference
      // since it's managed by the service locator
      final authBloc = AuthBloc(authRepository: authRepository);
      authBloc.insert(const AuthEvent.checkAuthStatus());
      // Register in service locator if needed
      serviceLocator.registerSingleton<AuthBloc>(authBloc);
      debugPrint('Auth bloc initialized');

      // Show splash screen first, then launch the appropriate app
      void launchApp() {
        if (appFlavor != null) {
          debugPrint('Launching app for flavor: ${appFlavor.name}');

          switch (appFlavor) {
            case AppFlavor.admin:
              debugPrint('Launching Admin App');
              runApp(const riverpod.ProviderScope(child: AdminApp()));
              break;

            case AppFlavor.buyer:
              debugPrint('Launching Buyer App');
              runApp(const riverpod.ProviderScope(child: BuyerApp()));
              break;

            case AppFlavor.seller:
              debugPrint('Launching Seller App');
              runApp(const riverpod.ProviderScope(child: SellerApp()));
              break;

            case AppFlavor.priest:
              debugPrint('Launching Priest App');
              // Use riverpod.ProviderScope for consistent initialization
              runApp(const riverpod.ProviderScope(child: PriestApp()));
              break;

            case AppFlavor.technician:
              debugPrint('Launching Technician App');
              // Use riverpod.ProviderScope for consistent initialization
              runApp(const riverpod.ProviderScope(child: TechnicianApp()));
              break;

            case AppFlavor.executor:
              debugPrint('Launching Executor App');
              runApp(const riverpod.ProviderScope(child: ExecutorApp()));
              break;

            case AppFlavor.saviour:
              debugPrint('Launching Saviour Delivery App');
              runApp(const riverpod.ProviderScope(child: SaviourApp()));
              break;

            case AppFlavor.hospital:
              debugPrint('Launching Hospital Management App');
              runApp(const riverpod.ProviderScope(child: HospitalApp()));
              break;
          }
        } else {
          // If no flavor is detected, show a flavor selection dialog
          debugPrint('No flavor detected, showing flavor selection dialog');
          runApp(const FlavorSelectionApp());
        }
      }

      // Show splash screen first, then launch the app
      runApp(
        MaterialApp(
          debugShowCheckedModeBanner: false,
          home: SplashScreen(
            onComplete: launchApp,
            duration: const Duration(seconds: 5),
          ),
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: const Color(0xFF6200EE),
              brightness: Brightness.light,
            ),
            useMaterial3: true,
          ),
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('Error during initialization: $e');
      debugPrint(stackTrace.toString());

      // Show error app with more details in debug mode
      runApp(ErrorApp(
        error: kDebugMode ? '$e\n$stackTrace' : e.toString(),
      ));
    }
  }, (error, stackTrace) {
    debugPrint('Uncaught error: $error');
    debugPrint(stackTrace.toString());
    // TODO: Log to Supabase analytics for error tracking
  });
}

// ShivishApp class removed - each flavor now uses its own app.dart file

class ErrorApp extends StatelessWidget {
  final String error;

  const ErrorApp({
    super.key,
    required this.error,
  });

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<BackupController>(
          create: (_) => serviceLocator<BackupController>(),
        ),
        // ...other providers...
      ],
      child: MaterialApp(
        title: 'Error',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: Colors.red,
            brightness: Brightness.light,
          ),
          useMaterial3: true,
        ),
        home: Scaffold(
          appBar: AppBar(
            title: const Text('Error'),
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'An error occurred during initialization:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: SingleChildScrollView(
                    child: Text(
                      error,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// _createAppConfig method removed - each flavor now creates its own config
