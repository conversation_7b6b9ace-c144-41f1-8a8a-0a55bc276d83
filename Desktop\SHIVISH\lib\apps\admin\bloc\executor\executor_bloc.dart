import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../../../shared/models/executor.dart';
import '../../../../shared/services/executor_service.dart';

part 'executor_event.dart';
part 'executor_state.dart';
part 'executor_bloc.freezed.dart';

@injectable
class ExecutorBloc extends Bloc<ExecutorEvent, ExecutorState> {
  final ExecutorService _executorService;
  static const int _pageSize = 20;
  bool _hasMore = true;
  StreamSubscription<List<Executor>>? _executorsSubscription;
  bool _isRealtimeEnabled = false;
  List<Executor> _cachedExecutors = [];

  ExecutorBloc(this._executorService) : super(const ExecutorState.initial()) {
    on<LoadExecutors>(_onLoadExecutors);
    on<LoadMoreExecutors>(_onLoadMoreExecutors);
    on<UpdateExecutorStatus>(_onUpdateExecutorStatus);
    on<UpdateExecutorPerformance>(_onUpdateExecutorPerformance);
    on<DeleteExecutor>(_onDeleteExecutor);
    on<CreateExecutor>(_onCreateExecutor);
    on<StartRealtimeUpdates>(_onStartRealtimeUpdates);
    on<StopRealtimeUpdates>(_onStopRealtimeUpdates);
  }

  @override
  Future<void> close() {
    _executorsSubscription?.cancel();
    return super.close();
  }

  Future<void> _onLoadExecutors(
    LoadExecutors event,
    Emitter<ExecutorState> emit,
  ) async {
    try {
      emit(const ExecutorState.loading());
      _hasMore = true;
      final executors = await _executorService.getExecutors(
        limit: _pageSize,
        startAfter: null,
      );
      emit(ExecutorState.loaded(executors));
      _hasMore = executors.length >= _pageSize;
    } catch (e) {
      emit(ExecutorState.error(e.toString()));
    }
  }

  Future<void> _onLoadMoreExecutors(
    LoadMoreExecutors event,
    Emitter<ExecutorState> emit,
  ) async {
    if (!_hasMore) return;

    try {
      final currentState = state;
      if (currentState is Loaded) {
        final executors = await _executorService.getExecutors(
          limit: _pageSize,
          startAfter: currentState.executors.last,
        );
        _hasMore = executors.length >= _pageSize;
        emit(ExecutorState.loaded([...currentState.executors, ...executors]));
      }
    } catch (e) {
      emit(ExecutorState.error(e.toString()));
    }
  }

  Future<void> _onUpdateExecutorStatus(
    UpdateExecutorStatus event,
    Emitter<ExecutorState> emit,
  ) async {
    final currentState = state;
    if (currentState is Loaded) {
      try {
        // Optimistic update
        final updatedExecutors = currentState.executors.map((executor) {
          if (executor.id == event.id) {
            return executor.copyWith(status: event.status);
          }
          return executor;
        }).toList();
        emit(ExecutorState.loaded(updatedExecutors));

        // Perform actual update
        await _executorService.updateExecutorStatus(event.id, event.status);

        // If real-time updates are enabled, the stream will handle the update
        if (!_isRealtimeEnabled) {
          // Reload data to ensure consistency
          final executors = await _executorService.getExecutors(
            limit: _pageSize,
            startAfter: null,
          );
          emit(ExecutorState.loaded(executors));
        }
      } catch (e) {
        // Revert optimistic update on error
        emit(ExecutorState.loaded(currentState.executors));
        emit(ExecutorState.error(e.toString()));
      }
    }
  }

  Future<void> _onUpdateExecutorPerformance(
    UpdateExecutorPerformance event,
    Emitter<ExecutorState> emit,
  ) async {
    final currentState = state;
    if (currentState is Loaded) {
      try {
        // Optimistic update
        final updatedExecutors = currentState.executors.map((executor) {
          if (executor.id == event.id) {
            return executor.copyWith(performanceMetrics: event.metrics);
          }
          return executor;
        }).toList();
        emit(ExecutorState.loaded(updatedExecutors));

        // Perform actual update
        await _executorService.updateExecutorPerformance(
          event.id,
          event.metrics,
        );

        // If real-time updates are enabled, the stream will handle the update
        if (!_isRealtimeEnabled) {
          // Reload data to ensure consistency
          final executors = await _executorService.getExecutors(
            limit: _pageSize,
            startAfter: null,
          );
          emit(ExecutorState.loaded(executors));
        }
      } catch (e) {
        // Revert optimistic update on error
        emit(ExecutorState.loaded(currentState.executors));
        emit(ExecutorState.error(e.toString()));
      }
    }
  }

  Future<void> _onDeleteExecutor(
    DeleteExecutor event,
    Emitter<ExecutorState> emit,
  ) async {
    final currentState = state;
    if (currentState is Loaded) {
      try {
        // Optimistic update
        final updatedExecutors = currentState.executors
            .where((executor) => executor.id != event.id)
            .toList();
        emit(ExecutorState.loaded(updatedExecutors));

        // Perform actual delete
        await _executorService.deleteExecutor(event.id);

        // If real-time updates are enabled, the stream will handle the update
        if (!_isRealtimeEnabled) {
          // Reload data to ensure consistency
          final executors = await _executorService.getExecutors(
            limit: _pageSize,
            startAfter: null,
          );
          emit(ExecutorState.loaded(executors));
        }
      } catch (e) {
        // Revert optimistic update on error
        emit(ExecutorState.loaded(currentState.executors));
        emit(ExecutorState.error(e.toString()));
      }
    }
  }

  Future<void> _onCreateExecutor(
    CreateExecutor event,
    Emitter<ExecutorState> emit,
  ) async {
    final currentState = state;
    if (currentState is Loaded) {
      try {
        // Create temporary executor for optimistic update
        final tempExecutor = Executor(
          id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
          name: event.data['name'] ?? '',
          email: event.data['email'] ?? '',
          phone: event.data['phone'] ?? '',
          role: event.data['role'] ?? '',
          status: event.data['status'] ?? 'pending',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          isDeleted: false,
        );

        // Optimistic update
        final updatedExecutors = [...currentState.executors, tempExecutor];
        emit(ExecutorState.loaded(updatedExecutors));

        // Perform actual create
        await _executorService.createExecutor(event.data);

        // If real-time updates are enabled, the stream will handle the update
        if (!_isRealtimeEnabled) {
          // Reload data to ensure consistency
          final executors = await _executorService.getExecutors(
            limit: _pageSize,
            startAfter: null,
          );
          emit(ExecutorState.loaded(executors));
        }
      } catch (e) {
        // Revert optimistic update on error
        emit(ExecutorState.loaded(currentState.executors));
        emit(ExecutorState.error(e.toString()));
      }
    }
  }

  Future<void> _onStartRealtimeUpdates(
    StartRealtimeUpdates event,
    Emitter<ExecutorState> emit,
  ) async {
    final currentState = state;
    try {
      emit(const ExecutorState.loading());
      _isRealtimeEnabled = true;
      _executorsSubscription?.cancel();

      // Store current state for optimistic updates
      if (currentState is Loaded) {
        _cachedExecutors = currentState.executors;
      }

      _executorsSubscription =
          _executorService.watchExecutors(limit: _pageSize).listen(
        (executors) {
          _cachedExecutors = executors;
          emit(ExecutorState.loaded(executors));
          _hasMore = executors.length >= _pageSize;
        },
        onError: (error) {
          _isRealtimeEnabled = false;
          emit(ExecutorState.error(error.toString()));
          // Fallback to cached data if available
          if (_cachedExecutors.isNotEmpty) {
            emit(ExecutorState.loaded(_cachedExecutors));
          }
        },
      );
    } catch (e) {
      _isRealtimeEnabled = false;
      emit(ExecutorState.error(e.toString()));
      // Fallback to cached data if available
      if (_cachedExecutors.isNotEmpty) {
        emit(ExecutorState.loaded(_cachedExecutors));
      }
    }
  }

  Future<void> _onStopRealtimeUpdates(
    StopRealtimeUpdates event,
    Emitter<ExecutorState> emit,
  ) async {
    _isRealtimeEnabled = false;
    await _executorsSubscription?.cancel();
    _executorsSubscription = null;
  }
}
