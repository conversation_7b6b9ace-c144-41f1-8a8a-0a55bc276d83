import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_bloc.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_event.dart';
import 'package:shivish/shared/models/api_key.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/ui_components/dialogs/api_key_form_dialog.dart';

class ApiKeyListItem extends StatelessWidget {
  final ApiKey apiKey;

  const ApiKeyListItem({
    super.key,
    required this.apiKey,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    apiKey.name,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                StatusBadge(
                  isActive: apiKey.isActive,
                  onToggle: () => _showStatusDialog(context),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Key: ${apiKey.key.substring(0, 8)}...',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 4),
            Text(
              'Created: ${_formatDate(apiKey.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            if (apiKey.lastUsedAt != null) ...[
              const SizedBox(height: 4),
              Text(
                'Last Used: ${_formatDate(apiKey.lastUsedAt!)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _showDeleteDialog(context),
                  icon: const Icon(Icons.delete),
                  label: const Text('Delete'),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  onPressed: () => _showEditDialog(context),
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
  }

  Future<void> _showStatusDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AppDialog(
        title: 'Toggle API Key Status',
        message:
            'Are you sure you want to ${apiKey.isActive ? 'deactivate' : 'activate'} this API key?',
        confirmText: apiKey.isActive ? 'Deactivate' : 'Activate',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed == true) {
      context.read<ApiKeyBloc>().add(
            ApiKeyEvent.toggleStatus(apiKey.id, !apiKey.isActive),
          );
    }
  }

  Future<void> _showDeleteDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Delete API Key',
        message:
            'Are you sure you want to delete this API key? This action cannot be undone.',
        confirmText: 'Delete',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed == true) {
      context.read<ApiKeyBloc>().add(
            ApiKeyEvent.delete(apiKey.id),
          );
    }
  }

  Future<void> _showEditDialog(BuildContext context) async {
    final result = await showDialog<ApiKey>(
      context: context,
      builder: (context) => ApiKeyFormDialog(apiKey: apiKey),
    );

    if (result != null) {
      context.read<ApiKeyBloc>().add(
            ApiKeyEvent.update(result),
          );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SuccessMessage(
            message: 'API key updated successfully',
          ),
        );
      }
    }
  }
}
