import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_event.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_state.dart';
import 'package:shivish/apps/admin/models/user.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:shivish/shared/services/user/user_service.dart';

@injectable
class UserManagementBloc
    extends Bloc<UserManagementEvent, UserManagementState> {
  final UserService _userService;

  UserManagementBloc(this._userService)
      : super(const UserManagementState.initial()) {
    on<LoadUsers>(_onLoadUsers);
    on<AddUser>(_onAddUser);
    on<UpdateUser>(_onUpdateUser);
    on<DeleteUser>(_onDeleteUser);
    on<FilterUsers>(_onFilterUsers);
  }

  Future<void> _onLoadUsers(
      LoadUsers event, Emitter<UserManagementState> emit) async {
    emit(const UserManagementState.loading());
    try {
      final userModels =
          await _userService.getUsersByRole(UserRole.buyer).first;
      final users =
          userModels.map((model) => User.fromUserModel(model)).toList();
      emit(UserManagementState.loaded(users));
    } catch (e) {
      emit(UserManagementState.error(e.toString()));
    }
  }

  Future<void> _onAddUser(
      AddUser event, Emitter<UserManagementState> emit) async {
    try {
      final userModel = event.user.toUserModel();
      await _userService.createOrUpdateUser(userModel);
      add(const LoadUsers());
    } catch (e) {
      emit(UserManagementState.error(e.toString()));
    }
  }

  Future<void> _onUpdateUser(
      UpdateUser event, Emitter<UserManagementState> emit) async {
    try {
      final userModel = event.user.toUserModel();
      await _userService.createOrUpdateUser(userModel);
      add(const LoadUsers());
    } catch (e) {
      emit(UserManagementState.error(e.toString()));
    }
  }

  Future<void> _onDeleteUser(
      DeleteUser event, Emitter<UserManagementState> emit) async {
    try {
      await _userService.deleteUser(event.userId);
      add(const LoadUsers());
    } catch (e) {
      emit(UserManagementState.error(e.toString()));
    }
  }

  Future<void> _onFilterUsers(
      FilterUsers event, Emitter<UserManagementState> emit) async {
    try {
      final userModels =
          await _userService.getUsersByRole(event.role ?? UserRole.buyer).first;
      var filteredUsers =
          userModels.map((model) => User.fromUserModel(model)).toList();

      if (event.status != null) {
        filteredUsers = filteredUsers
            .where((user) =>
                (event.status == UserStatus.active && user.isActive) ||
                (event.status == UserStatus.inactive && !user.isActive))
            .toList();
      }

      if (event.isActive != null) {
        filteredUsers = filteredUsers
            .where((user) => user.isActive == event.isActive)
            .toList();
      }

      emit(UserManagementState.loaded(filteredUsers));
    } catch (e) {
      emit(UserManagementState.error(e.toString()));
    }
  }
}
