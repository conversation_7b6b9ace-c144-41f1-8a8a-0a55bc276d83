import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_bloc.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_event.dart';
import 'package:shivish/shared/models/backup.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';
import 'package:shivish/shared/ui_components/badges/status_badge.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';

class BackupListItem extends StatelessWidget {
  final Backup backup;

  const BackupListItem({
    super.key,
    required this.backup,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(backup.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(backup.description),
            const SizedBox(height: 4),
            Text('Created: ${_formatDate(backup.createdAt)}'),
            if (backup.lastRestoredAt != null)
              Text('Last restored: ${_formatDate(backup.lastRestoredAt!)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            StatusBadge(
              isActive: backup.status == 'active',
              onToggle: () => _showStatusDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.restore),
              onPressed: () => _showRestoreDialog(context),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return date.toLocal().toString().split('.')[0];
  }

  Future<void> _showStatusDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AppDialog(
        title: 'Toggle Backup Status',
        message:
            'Are you sure you want to ${backup.status == 'active' ? 'deactivate' : 'activate'} this backup?',
        confirmText: 'Yes',
        cancelText: 'No',
      ),
    );

    if (result == true) {
      context.read<BackupBloc>().add(
            BackupEvent.updateBackup(
              backup.copyWith(
                status: backup.status == 'active' ? 'inactive' : 'active',
              ),
            ),
          );
    }
  }

  Future<void> _showRestoreDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Restore Backup',
        message:
            'Are you sure you want to restore this backup? This will overwrite current data.',
        confirmText: 'Yes',
        cancelText: 'No',
      ),
    );

    if (result == true) {
      context.read<BackupBloc>().add(
            BackupEvent.restoreBackup(backup.id),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Backup restored successfully'),
      );
    }
  }

  Future<void> _showDeleteDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const AppDialog(
        title: 'Delete Backup',
        message:
            'Are you sure you want to delete this backup? This action cannot be undone.',
        confirmText: 'Yes',
        cancelText: 'No',
      ),
    );

    if (result == true) {
      context.read<BackupBloc>().add(
            BackupEvent.deleteBackup(backup.id),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Backup deleted successfully'),
      );
    }
  }
}
