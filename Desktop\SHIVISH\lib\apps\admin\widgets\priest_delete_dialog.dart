import 'package:flutter/material.dart';
import '../../../shared/models/priest.dart';

class PriestDeleteDialog extends StatelessWidget {
  final Priest priest;

  const PriestDeleteDialog({
    super.key,
    required this.priest,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Priest'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Are you sure you want to delete ${priest.name}?'),
          const SizedBox(height: 16),
          const Text(
            'This action cannot be undone. All associated data will be permanently deleted.',
            style: TextStyle(color: Colors.red),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(context, true),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
          ),
          child: const Text('Delete'),
        ),
      ],
    );
  }
}
