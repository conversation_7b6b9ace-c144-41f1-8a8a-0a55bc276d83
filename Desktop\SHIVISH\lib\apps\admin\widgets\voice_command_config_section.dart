import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/voice_command_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class VoiceCommandConfigSection extends ConsumerWidget {
  final VoiceCommandConfigModel config;

  const VoiceCommandConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Voice Command Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Enabled',
              config.enabled,
              (value) => _updateConfig(
                ref,
                config.copyWith(enabled: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Wake Word',
              config.wakeWord,
              (value) => _updateConfig(
                ref,
                config.copyWith(wakeWord: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Language',
              config.language,
              (value) => _updateConfig(
                ref,
                config.copyWith(language: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Sensitivity',
              (config.sensitivity * 100).toInt(),
              (value) => _updateConfig(
                ref,
                config.copyWith(sensitivity: value / 100),
              ),
            ),
            const SizedBox(height: 16),
            _buildSupportedCommandsField(
              context,
              ref,
              config.supportedCommands,
              (value) => _updateConfig(
                ref,
                config.copyWith(supportedCommands: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildCommandMappingsField(
              context,
              ref,
              config.commandMappings,
              (value) => _updateConfig(
                ref,
                config.copyWith(commandMappings: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    BuildContext context,
    WidgetRef ref,
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Expanded(
          child: TextFormField(
            initialValue: value,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberField(
    BuildContext context,
    WidgetRef ref,
    String label,
    int value,
    Function(int) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        SizedBox(
          width: 100,
          child: TextFormField(
            initialValue: value.toString(),
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              final number = int.tryParse(value);
              if (number != null) {
                onChanged(number);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    BuildContext context,
    WidgetRef ref,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSupportedCommandsField(
    BuildContext context,
    WidgetRef ref,
    List<String> commands,
    Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Supported Commands'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: commands.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: commands[index],
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newCommands = List<String>.from(commands);
                        newCommands[index] = value;
                        onChanged(newCommands);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newCommands = List<String>.from(commands);
                      newCommands.removeAt(index);
                      onChanged(newCommands);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newCommands = List<String>.from(commands);
            newCommands.add('');
            onChanged(newCommands);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Command'),
        ),
      ],
    );
  }

  Widget _buildCommandMappingsField(
    BuildContext context,
    WidgetRef ref,
    Map<String, String> mappings,
    Function(Map<String, String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Command Mappings'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: mappings.length,
          itemBuilder: (context, index) {
            final key = mappings.keys.elementAt(index);
            final value = mappings[key];
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: key,
                      decoration: const InputDecoration(
                        labelText: 'Command',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (newKey) {
                        final newMappings = Map<String, String>.from(mappings);
                        newMappings.remove(key);
                        newMappings[newKey] = value ?? '';
                        onChanged(newMappings);
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextFormField(
                      initialValue: value,
                      decoration: const InputDecoration(
                        labelText: 'Action',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (newValue) {
                        final newMappings = Map<String, String>.from(mappings);
                        newMappings[key] = newValue;
                        onChanged(newMappings);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newMappings = Map<String, String>.from(mappings);
                      newMappings.remove(key);
                      onChanged(newMappings);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newMappings = Map<String, String>.from(mappings);
            newMappings['new_command'] = '';
            onChanged(newMappings);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Mapping'),
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    VoiceCommandConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateVoiceCommandConfig(newConfig);
  }
}
