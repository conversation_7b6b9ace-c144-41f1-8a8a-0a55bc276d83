import 'package:flutter/material.dart';
import 'package:shivish/shared/models/analytics/analytics_model.dart';
import 'package:fl_chart/fl_chart.dart';

class AnalyticsDrillDownView extends StatelessWidget {
  final String title;
  final String metricType;
  final AnalyticsData data;
  final VoidCallback onClose;

  const AnalyticsDrillDownView({
    super.key,
    required this.title,
    required this.metricType,
    required this.data,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onClose,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildDrillDownContent(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrillDownContent(BuildContext context) {
    switch (metricType) {
      case 'sales':
        return _buildSalesDrillDown();
      case 'products':
        return _buildProductsDrillDown();
      case 'customers':
        return _buildCustomersDrillDown();
      default:
        return const Center(child: Text('Invalid metric type'));
    }
  }

  Widget _buildSalesDrillDown() {
    return Column(
      children: [
        _buildSalesSummary(),
        const SizedBox(height: 24),
        Expanded(
          child: _buildSalesChart(),
        ),
      ],
    );
  }

  Widget _buildSalesSummary() {
    return GridView.count(
      shrinkWrap: true,
      crossAxisCount: 3,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          'Total Sales',
          '₹${data.totalSales.toStringAsFixed(2)}',
          Icons.currency_rupee,
          Colors.green,
        ),
        _buildSummaryCard(
          'Average Order Value',
          '₹${data.averageOrderValue.toStringAsFixed(2)}',
          Icons.trending_up,
          Colors.blue,
        ),
        _buildSummaryCard(
          'Total Orders',
          data.totalOrders.toString(),
          Icons.shopping_cart,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildSalesChart() {
    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= data.salesData.length) {
                  return const Text('');
                }
                return Text(data.salesData[value.toInt()].date);
              },
            ),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: data.salesData
                .asMap()
                .entries
                .map((e) => FlSpot(e.key.toDouble(), e.value.amount))
                .toList(),
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsDrillDown() {
    return Column(
      children: [
        _buildProductsSummary(),
        const SizedBox(height: 24),
        Expanded(
          child: _buildProductsList(),
        ),
      ],
    );
  }

  Widget _buildProductsSummary() {
    return GridView.count(
      shrinkWrap: true,
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          'Total Products',
          data.topProducts.length.toString(),
          Icons.inventory,
          Colors.purple,
        ),
        _buildSummaryCard(
          'Total Revenue',
          '₹${data.topProducts.fold(0.0, (sum, p) => sum + p.revenue).toStringAsFixed(2)}',
          Icons.currency_rupee,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildProductsList() {
    return ListView.builder(
      itemCount: data.topProducts.length,
      itemBuilder: (context, index) {
        final product = data.topProducts[index];
        return ListTile(
          leading: Image.network(
            product.imageUrl,
            width: 50,
            height: 50,
            fit: BoxFit.cover,
          ),
          title: Text(product.name),
          subtitle: Text('Quantity: ${product.quantity}'),
          trailing: Text(
            '₹${product.revenue.toStringAsFixed(2)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomersDrillDown() {
    return Column(
      children: [
        _buildCustomersSummary(),
        const SizedBox(height: 24),
        Expanded(
          child: _buildCustomerMetrics(),
        ),
      ],
    );
  }

  Widget _buildCustomersSummary() {
    return GridView.count(
      shrinkWrap: true,
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 2,
      children: [
        _buildSummaryCard(
          'New Customers',
          data.customerMetrics.newCustomers.toString(),
          Icons.person_add,
          Colors.blue,
        ),
        _buildSummaryCard(
          'Repeat Customers',
          data.customerMetrics.repeatCustomers.toString(),
          Icons.people,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildCustomerMetrics() {
    return ListView(
      children: [
        _buildCustomerMetricRow(
          'Customer Retention Rate',
          '${data.customerMetrics.customerRetentionRate.toStringAsFixed(1)}%',
          Icons.trending_up,
        ),
        _buildCustomerMetricRow(
          'Customer Satisfaction',
          '${data.customerMetrics.customerSatisfaction.toStringAsFixed(1)}%',
          Icons.sentiment_satisfied,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerMetricRow(
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue),
          const SizedBox(width: 8),
          Expanded(
            child: Text(label),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
