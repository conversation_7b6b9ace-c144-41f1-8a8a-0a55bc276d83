import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';

final _logger = getLogger('SaviourProfileChangeRequestsScreen');

/// Provider for pending saviour profile change requests
final profileChangeRequestsProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  return FirebaseFirestore.instance
      .collection('delivery_person_profile_requests')
      .where('status', isEqualTo: 'pending')
      .orderBy('requestedAt', descending: true)
      .snapshots()
      .map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();
      });
});

/// Screen for admin to approve saviour profile change requests
class SaviourProfileChangeRequestsScreen extends ConsumerStatefulWidget {
  const SaviourProfileChangeRequestsScreen({super.key});

  @override
  ConsumerState<SaviourProfileChangeRequestsScreen> createState() => _SaviourProfileChangeRequestsScreenState();
}

class _SaviourProfileChangeRequestsScreenState extends ConsumerState<SaviourProfileChangeRequestsScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final requestsAsync = ref.watch(profileChangeRequestsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Saviour Profile Change Requests'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by name, phone, vehicle type, changed fields...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
          ),
        ),
      ),
      body: requestsAsync.when(
        data: (requests) {
          // Filter requests based on search query
          final filteredRequests = _searchQuery.isEmpty
              ? requests
              : requests.where((request) {
                  final searchText = request['searchText']?.toString().toLowerCase() ?? '';
                  final changedFields = (request['changedFields'] as List<dynamic>?)?.join(' ') ?? '';
                  final deliveryPersonName = request['currentData']?['name']?.toString().toLowerCase() ?? '';
                  final phone = request['currentData']?['phone']?.toString() ?? '';
                  final vehicleType = request['currentData']?['vehicleType']?.toString().toLowerCase() ?? '';
                  
                  return searchText.contains(_searchQuery) ||
                         changedFields.toLowerCase().contains(_searchQuery) ||
                         deliveryPersonName.contains(_searchQuery) ||
                         phone.contains(_searchQuery) ||
                         vehicleType.contains(_searchQuery);
                }).toList();

          if (filteredRequests.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _searchQuery.isEmpty ? Icons.inbox : Icons.search_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _searchQuery.isEmpty 
                        ? 'No pending profile change requests'
                        : 'No requests found for "$_searchQuery"',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredRequests.length,
            itemBuilder: (context, index) {
              final request = filteredRequests[index];
              return _buildRequestCard(context, request);
            },
          );
        },
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) {
          _logger.severe('Error loading profile change requests: $error\n$stackTrace');
          return ErrorMessage(message: error.toString());
        },
      ),
    );
  }

  Widget _buildRequestCard(BuildContext context, Map<String, dynamic> request) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM d, yyyy HH:mm');
    
    // Get current and requested data
    final currentData = request['currentData'] as Map<String, dynamic>? ?? {};
    final requestedChanges = request['requestedChanges'] as Map<String, dynamic>? ?? {};
    final changedFields = (request['changedFields'] as List<dynamic>?)?.cast<String>() ?? [];
    
    // Format request date
    String requestDate = 'Unknown date';
    final requestedAt = request['requestedAt'];
    if (requestedAt is Timestamp) {
      requestDate = dateFormat.format(requestedAt.toDate());
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with saviour info
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    (currentData['name']?.toString().substring(0, 1).toUpperCase()) ?? 'S',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentData['name']?.toString() ?? 'Unknown Saviour',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Phone: ${currentData['phone']?.toString() ?? 'Not provided'}',
                        style: theme.textTheme.bodyMedium,
                      ),
                      Text(
                        'Vehicle: ${currentData['vehicleType']?.toString() ?? 'Not provided'}',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: const Text('Profile Change'),
                  backgroundColor: Colors.blue.shade100,
                  labelStyle: TextStyle(color: Colors.blue.shade800),
                ),
              ],
            ),
            const Divider(height: 24),
            
            // Changed fields section
            Text(
              'Requested Changes:',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            // Display each changed field
            ...changedFields.map((field) => _buildChangeRow(
              context,
              field,
              currentData[field]?.toString() ?? 'Not set',
              requestedChanges[field]?.toString() ?? 'Not set',
            )),
            
            const SizedBox(height: 16),
            Text(
              'Requested on: $requestDate',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 16),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => _showRejectDialog(context, request),
                  child: const Text('Reject'),
                ),
                const SizedBox(width: 8),
                FilledButton(
                  onPressed: () => _showApproveDialog(context, request),
                  child: const Text('Approve'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChangeRow(BuildContext context, String field, String currentValue, String newValue) {
    final theme = Theme.of(context);

    // Format field name for display
    String displayField = field.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(1)}',
    ).trim();
    displayField = displayField[0].toUpperCase() + displayField.substring(1);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              displayField,
              style: theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current:',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        currentValue,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_forward, size: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Requested:',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        newValue,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showApproveDialog(BuildContext context, Map<String, dynamic> request) {
    final notesController = TextEditingController();
    final currentData = request['currentData'] as Map<String, dynamic>? ?? {};
    final saviourName = currentData['name']?.toString() ?? 'Unknown Saviour';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Approve Profile Changes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to approve the profile changes for $saviourName?'),
            const SizedBox(height: 16),
            TextField(
              controller: notesController,
              decoration: const InputDecoration(
                labelText: 'Approval Notes (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                await _approveProfileChangeRequest(
                  request,
                  notes: notesController.text.isNotEmpty ? notesController.text : null,
                );

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Profile changes for $saviourName have been approved'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error approving profile change request: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  void _showRejectDialog(BuildContext context, Map<String, dynamic> request) {
    final reasonController = TextEditingController();
    final currentData = request['currentData'] as Map<String, dynamic>? ?? {};
    final saviourName = currentData['name']?.toString() ?? 'Unknown Saviour';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Profile Changes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Are you sure you want to reject the profile changes for $saviourName?'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason for Rejection (Required)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              if (reasonController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a reason for rejection'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context);

              try {
                await _rejectProfileChangeRequest(
                  request,
                  reason: reasonController.text,
                );

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Profile changes for $saviourName have been rejected'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error rejecting profile change request: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  Future<void> _approveProfileChangeRequest(Map<String, dynamic> request, {String? notes}) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final requestId = request['id'] as String;
      final deliveryPersonId = request['deliveryPersonId'] as String;
      final requestedChanges = request['requestedChanges'] as Map<String, dynamic>;

      // Update the delivery person with the requested changes
      final deliveryPersonUpdates = <String, dynamic>{
        ...requestedChanges,
        'isVerified': true,
        'isActive': true,
        'profileChangeRequested': false,
        'approvedBy': 'admin', // TODO: Get actual admin ID
        'approverRole': 'admin',
        'approvedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      if (notes != null) {
        deliveryPersonUpdates['approvalNotes'] = notes;
      }

      await firestore.collection('delivery_persons').doc(deliveryPersonId).update(deliveryPersonUpdates);

      // Update the request status
      await firestore.collection('delivery_person_profile_requests').doc(requestId).update({
        'status': 'approved',
        'approvedBy': 'admin', // TODO: Get actual admin ID
        'approverRole': 'admin',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvalNotes': notes,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      _logger.info('Profile change request $requestId approved for delivery person $deliveryPersonId');
    } catch (e) {
      _logger.severe('Error approving profile change request: $e');
      rethrow;
    }
  }

  Future<void> _rejectProfileChangeRequest(Map<String, dynamic> request, {required String reason}) async {
    try {
      final firestore = FirebaseFirestore.instance;
      final requestId = request['id'] as String;
      final deliveryPersonId = request['deliveryPersonId'] as String;

      // Reset delivery person status to verified/active (revert to previous state)
      await firestore.collection('delivery_persons').doc(deliveryPersonId).update({
        'isVerified': true,
        'isActive': true,
        'profileChangeRequested': false,
        'rejectedBy': 'admin', // TODO: Get actual admin ID
        'rejectorRole': 'admin',
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectionReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update the request status
      await firestore.collection('delivery_person_profile_requests').doc(requestId).update({
        'status': 'rejected',
        'rejectedBy': 'admin', // TODO: Get actual admin ID
        'rejectorRole': 'admin',
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectionReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      _logger.info('Profile change request $requestId rejected for delivery person $deliveryPersonId');
    } catch (e) {
      _logger.severe('Error rejecting profile change request: $e');
      rethrow;
    }
  }
}
