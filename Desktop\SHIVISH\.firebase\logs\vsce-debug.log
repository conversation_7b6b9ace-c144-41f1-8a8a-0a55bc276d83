[debug] [2025-04-29T15:10:56.054Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-04-29T15:11:05.472Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-04-29T15:11:05.475Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-04-29T15:11:05.481Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-04-29T15:11:05.487Z] Checked if tokens are valid: true, expires at: 1745942490042
[debug] [2025-04-29T15:11:05.490Z] Checked if tokens are valid: true, expires at: 1745942490042
[debug] [2025-04-29T15:11:05.496Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-04-29T15:11:05.500Z] Checked if tokens are valid: true, expires at: 1745942490042
[debug] [2025-04-29T15:11:05.503Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-04-29T15:11:05.583Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-04-29T15:11:07.732Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-04-29T15:11:07.733Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-04-30T03:08:15.809Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-04-30T03:08:32.303Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-04-30T03:08:32.327Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-04-30T03:08:32.329Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-04-30T03:08:32.374Z] Checked if tokens are valid: false, expires at: 1745942490042
[debug] [2025-04-30T03:08:32.376Z] Checked if tokens are valid: false, expires at: 1745942490042
[debug] [2025-04-30T03:08:32.386Z] > refreshing access token with scopes: []
[debug] [2025-04-30T03:08:32.402Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-04-30T03:08:32.414Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T03:08:32.468Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-04-30T03:08:33.190Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-04-30T03:08:33.191Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T03:08:33.206Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-04-30T03:08:33.208Z] Checked if tokens are valid: true, expires at: 1745986112192
[debug] [2025-04-30T03:08:33.209Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-04-30T03:08:35.993Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-04-30T03:08:35.994Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-04-30T08:44:13.251Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-04-30T08:44:35.753Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-04-30T08:44:35.764Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-04-30T08:44:35.765Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-04-30T08:44:35.819Z] Checked if tokens are valid: false, expires at: 1745986112192
[debug] [2025-04-30T08:44:35.820Z] Checked if tokens are valid: false, expires at: 1745986112192
[debug] [2025-04-30T08:44:35.821Z] > refreshing access token with scopes: []
[debug] [2025-04-30T08:44:35.836Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-04-30T08:44:35.840Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T08:44:35.889Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-04-30T08:44:57.764Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-04-30T08:44:57.766Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T08:44:57.795Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-04-30T08:44:57.799Z] Checked if tokens are valid: true, expires at: 1746006296772
[debug] [2025-04-30T08:44:57.804Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-04-30T08:45:03.934Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-04-30T08:45:03.935Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-04-30T12:10:39.433Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-04-30T12:10:50.587Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-04-30T12:10:50.627Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-04-30T12:10:50.628Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-04-30T12:10:50.634Z] Checked if tokens are valid: false, expires at: 1746006296772
[debug] [2025-04-30T12:10:50.634Z] Checked if tokens are valid: false, expires at: 1746006296772
[debug] [2025-04-30T12:10:50.636Z] > refreshing access token with scopes: []
[debug] [2025-04-30T12:10:50.639Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-04-30T12:10:50.640Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T12:10:54.173Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-04-30T12:10:54.174Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T12:10:54.189Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-04-30T12:10:54.191Z] Checked if tokens are valid: true, expires at: 1746018653175
[debug] [2025-04-30T12:10:54.192Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-04-30T12:10:57.000Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-04-30T12:10:57.000Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-04-30T15:20:06.136Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-04-30T15:20:15.174Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-04-30T15:20:15.177Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-04-30T15:20:15.178Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-04-30T15:20:15.184Z] Checked if tokens are valid: false, expires at: 1746018653175
[debug] [2025-04-30T15:20:15.185Z] Checked if tokens are valid: false, expires at: 1746018653175
[debug] [2025-04-30T15:20:15.186Z] > refreshing access token with scopes: []
[debug] [2025-04-30T15:20:15.189Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-04-30T15:20:15.190Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T15:20:15.218Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-04-30T15:20:25.162Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-04-30T15:20:25.162Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-04-30T15:20:25.180Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-04-30T15:20:25.183Z] Checked if tokens are valid: true, expires at: 1746030024163
[debug] [2025-04-30T15:20:25.184Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-04-30T15:20:26.036Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-04-30T15:20:26.036Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-02T04:23:09.773Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-02T04:24:08.541Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-02T04:24:08.566Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-02T04:24:08.571Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-02T04:24:08.607Z] Checked if tokens are valid: false, expires at: 1746030024163
[debug] [2025-05-02T04:24:08.609Z] Checked if tokens are valid: false, expires at: 1746030024163
[debug] [2025-05-02T04:24:08.613Z] > refreshing access token with scopes: []
[debug] [2025-05-02T04:24:08.625Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-02T04:24:08.626Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-02T04:24:08.703Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-02T04:24:09.706Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-02T04:24:09.708Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-02T04:24:09.976Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-02T04:24:09.981Z] Checked if tokens are valid: true, expires at: 1746163448711
[debug] [2025-05-02T04:24:09.984Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-02T04:24:36.200Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-02T04:24:36.204Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-02T14:20:11.920Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-02T14:20:32.716Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-02T14:20:32.720Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-02T14:20:32.721Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-02T14:20:32.733Z] Checked if tokens are valid: false, expires at: 1746191110848
[debug] [2025-05-02T14:20:32.733Z] Checked if tokens are valid: false, expires at: 1746191110848
[debug] [2025-05-02T14:20:32.735Z] > refreshing access token with scopes: []
[debug] [2025-05-02T14:20:32.739Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-02T14:20:32.741Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-02T14:20:32.773Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-02T14:20:33.321Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-02T14:20:33.322Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-02T14:20:33.460Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-02T14:20:33.463Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:20:33.465Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-02T14:20:34.855Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-02T14:20:34.860Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-02T14:34:44.083Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-02T14:35:16.005Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-02T14:35:16.009Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-02T14:35:16.011Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-02T14:35:16.020Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:35:16.022Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:35:16.023Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-02T14:35:16.028Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:35:16.034Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-02T14:35:16.060Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-02T14:35:17.229Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-02T14:35:17.304Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-02T14:35:17.305Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-02T14:44:20.913Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-02T14:44:45.302Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-02T14:44:45.316Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-02T14:44:45.319Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-02T14:44:45.330Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:44:45.333Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:44:45.335Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-02T14:44:45.354Z] Checked if tokens are valid: true, expires at: 1746199232378
[debug] [2025-05-02T14:44:45.361Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-02T14:44:45.404Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-02T14:44:46.794Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-02T14:44:46.882Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-02T14:44:46.882Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-03T07:59:21.242Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-03T07:59:46.467Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-03T07:59:46.471Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-03T07:59:46.473Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-03T07:59:46.563Z] Checked if tokens are valid: false, expires at: 1746199232378
[debug] [2025-05-03T07:59:46.565Z] Checked if tokens are valid: false, expires at: 1746199232378
[debug] [2025-05-03T07:59:46.567Z] > refreshing access token with scopes: []
[debug] [2025-05-03T07:59:46.584Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-03T07:59:46.586Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T07:59:46.633Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-03T07:59:47.062Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-03T07:59:47.063Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T07:59:47.074Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-03T07:59:47.076Z] Checked if tokens are valid: true, expires at: 1746262786064
[debug] [2025-05-03T07:59:47.077Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-03T07:59:47.521Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-03T07:59:48.308Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-03T07:59:48.309Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-03T12:20:19.876Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-03T12:20:48.411Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-03T12:20:48.437Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-03T12:20:48.439Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-03T12:20:48.446Z] Checked if tokens are valid: false, expires at: 1746262786064
[debug] [2025-05-03T12:20:48.450Z] Checked if tokens are valid: false, expires at: 1746262786064
[debug] [2025-05-03T12:20:48.453Z] > refreshing access token with scopes: []
[debug] [2025-05-03T12:20:48.459Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-03T12:20:48.461Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T12:20:48.487Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-03T12:20:49.107Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-03T12:20:49.108Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T12:20:49.162Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-03T12:20:49.166Z] Checked if tokens are valid: true, expires at: 1746278448150
[debug] [2025-05-03T12:20:49.168Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-03T12:20:49.866Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-03T12:20:50.420Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-03T12:20:50.421Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-03T13:49:17.851Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-03T13:49:38.499Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-03T13:49:38.508Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-03T13:49:38.509Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-03T13:49:38.518Z] Checked if tokens are valid: false, expires at: 1746278448150
[debug] [2025-05-03T13:49:38.520Z] Checked if tokens are valid: false, expires at: 1746278448150
[debug] [2025-05-03T13:49:38.522Z] > refreshing access token with scopes: []
[debug] [2025-05-03T13:49:38.526Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-03T13:49:38.528Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T13:49:38.559Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-03T13:49:39.789Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-03T13:49:39.790Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-03T13:49:39.805Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-03T13:49:39.808Z] Checked if tokens are valid: true, expires at: 1746283778792
[debug] [2025-05-03T13:49:39.809Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-03T13:49:40.470Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-03T13:49:41.331Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-03T13:49:41.334Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-04T04:01:14.995Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-04T04:01:51.124Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-04T04:01:51.138Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-04T04:01:51.140Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-04T04:01:51.163Z] Checked if tokens are valid: false, expires at: 1746283778792
[debug] [2025-05-04T04:01:51.167Z] Checked if tokens are valid: false, expires at: 1746283778792
[debug] [2025-05-04T04:01:51.169Z] > refreshing access token with scopes: []
[debug] [2025-05-04T04:01:51.173Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-04T04:01:51.174Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-04T04:01:51.247Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-04T04:01:52.176Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-04T04:01:52.177Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-04T04:01:52.193Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-04T04:01:52.197Z] Checked if tokens are valid: true, expires at: 1746334911178
[debug] [2025-05-04T04:01:52.199Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-04T04:01:54.813Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-04T04:01:55.696Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-04T04:01:55.698Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-04T18:19:57.068Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-04T18:20:14.064Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-04T18:20:14.066Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-04T18:20:14.067Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-04T18:20:14.072Z] Checked if tokens are valid: false, expires at: 1746343512689
[debug] [2025-05-04T18:20:14.072Z] Checked if tokens are valid: false, expires at: 1746343512689
[debug] [2025-05-04T18:20:14.073Z] > refreshing access token with scopes: []
[debug] [2025-05-04T18:20:14.076Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-04T18:20:14.077Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-04T18:20:14.092Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-04T18:20:17.504Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-04T18:20:22.237Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-04T18:20:22.238Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-04T18:20:22.252Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-04T18:20:22.254Z] Checked if tokens are valid: true, expires at: 1746386421238
[debug] [2025-05-04T18:20:22.256Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-04T18:20:23.411Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-04T18:20:23.412Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-04T18:30:10.423Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-04T18:30:56.507Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-04T18:30:56.530Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-04T18:30:56.534Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-04T18:30:56.544Z] Checked if tokens are valid: true, expires at: 1746386421238
[debug] [2025-05-04T18:30:56.551Z] Checked if tokens are valid: true, expires at: 1746386421238
[debug] [2025-05-04T18:30:56.560Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-04T18:30:56.565Z] Checked if tokens are valid: true, expires at: 1746386421238
[debug] [2025-05-04T18:30:56.570Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-04T18:30:56.621Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-04T18:30:58.333Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-04T18:30:58.432Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-04T18:30:58.433Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-05T05:05:14.093Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-05T05:05:45.839Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-05T05:05:45.843Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-05T05:05:45.844Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-05T05:05:45.854Z] Checked if tokens are valid: false, expires at: 1746386421238
[debug] [2025-05-05T05:05:45.855Z] Checked if tokens are valid: false, expires at: 1746386421238
[debug] [2025-05-05T05:05:45.856Z] > refreshing access token with scopes: []
[debug] [2025-05-05T05:05:45.859Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-05T05:05:45.860Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T05:05:45.884Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-05T05:05:46.623Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-05T05:05:46.626Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T05:05:46.663Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-05T05:05:46.667Z] Checked if tokens are valid: true, expires at: 1746425145627
[debug] [2025-05-05T05:05:46.669Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-05T05:05:47.460Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-05T05:05:49.103Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-05T05:05:49.104Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-05-05T07:54:15.329Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-05T07:54:28.342Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-05T07:54:28.348Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-05T07:54:28.350Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-05T07:54:28.357Z] Checked if tokens are valid: false, expires at: 1746425145627
[debug] [2025-05-05T07:54:28.358Z] Checked if tokens are valid: false, expires at: 1746425145627
[debug] [2025-05-05T07:54:28.359Z] > refreshing access token with scopes: []
[debug] [2025-05-05T07:54:28.364Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-05T07:54:28.365Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T07:54:29.158Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-05T07:54:29.159Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T07:54:29.180Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-05T07:54:29.183Z] Checked if tokens are valid: true, expires at: 1746435268160
[debug] [2025-05-05T07:54:29.185Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-05T07:54:29.725Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-05T07:54:30.333Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-05T07:54:30.334Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-05T11:06:23.322Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-05T11:07:01.740Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-05T11:07:01.749Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-05T11:07:01.750Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-05T11:07:01.759Z] Checked if tokens are valid: false, expires at: 1746435268160
[debug] [2025-05-05T11:07:01.765Z] Checked if tokens are valid: false, expires at: 1746435268160
[debug] [2025-05-05T11:07:01.766Z] > refreshing access token with scopes: []
[debug] [2025-05-05T11:07:01.772Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-05T11:07:01.773Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T11:07:01.817Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-05T11:07:02.291Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-05T11:07:02.292Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-05T11:07:02.331Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-05T11:07:02.337Z] Checked if tokens are valid: true, expires at: 1746446821293
[debug] [2025-05-05T11:07:02.338Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-05T11:07:03.228Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-05T11:07:03.229Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-05-05T11:07:05.521Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-06T04:10:38.848Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-06T04:10:59.955Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-06T04:10:59.959Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-06T04:10:59.960Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-06T04:10:59.969Z] Checked if tokens are valid: false, expires at: 1746446821293
[debug] [2025-05-06T04:10:59.970Z] Checked if tokens are valid: false, expires at: 1746446821293
[debug] [2025-05-06T04:10:59.972Z] > refreshing access token with scopes: []
[debug] [2025-05-06T04:10:59.977Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-06T04:10:59.978Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T04:11:00.021Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-06T04:11:01.351Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-06T04:11:01.423Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-06T04:11:01.424Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T04:11:01.447Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-06T04:11:01.450Z] Checked if tokens are valid: true, expires at: 1746508260425
[debug] [2025-05-06T04:11:01.451Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-06T04:11:02.200Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-06T04:11:02.200Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-06T06:45:34.653Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-06T06:46:05.082Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-06T06:46:05.094Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-06T06:46:05.098Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-06T06:46:05.107Z] Checked if tokens are valid: false, expires at: 1746508260425
[debug] [2025-05-06T06:46:05.108Z] Checked if tokens are valid: false, expires at: 1746508260425
[debug] [2025-05-06T06:46:05.110Z] > refreshing access token with scopes: []
[debug] [2025-05-06T06:46:05.119Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-06T06:46:05.120Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T06:46:05.147Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-06T06:46:06.645Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-06T06:46:06.751Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-06T06:46:06.752Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T06:46:06.767Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-06T06:46:06.770Z] Checked if tokens are valid: true, expires at: 1746517565753
[debug] [2025-05-06T06:46:06.772Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-06T06:46:07.492Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-06T06:46:07.493Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-06T08:29:08.345Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-06T08:29:52.246Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-06T08:29:52.249Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-06T08:29:52.250Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-06T08:29:52.258Z] Checked if tokens are valid: false, expires at: 1746517565753
[debug] [2025-05-06T08:29:52.259Z] Checked if tokens are valid: false, expires at: 1746517565753
[debug] [2025-05-06T08:29:52.260Z] > refreshing access token with scopes: []
[debug] [2025-05-06T08:29:52.264Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-06T08:29:52.265Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T08:29:52.332Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-06T08:29:55.740Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-06T08:30:02.190Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-06T08:30:02.196Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-06T08:30:02.323Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-06T08:30:02.326Z] Checked if tokens are valid: true, expires at: 1746523801198
[debug] [2025-05-06T08:30:02.328Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-06T08:30:03.473Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-06T08:30:03.474Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-08T09:30:00.441Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-08T09:30:52.199Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-08T09:30:52.222Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-08T09:30:52.232Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-08T09:30:52.251Z] Checked if tokens are valid: false, expires at: 1746523801198
[debug] [2025-05-08T09:30:52.252Z] Checked if tokens are valid: false, expires at: 1746523801198
[debug] [2025-05-08T09:30:52.254Z] > refreshing access token with scopes: []
[debug] [2025-05-08T09:30:52.263Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-08T09:30:52.265Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-08T09:30:52.313Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-08T09:30:54.666Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-08T09:30:55.194Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-08T09:30:55.195Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-08T09:30:55.226Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-08T09:30:55.230Z] Checked if tokens are valid: true, expires at: 1746700254196
[debug] [2025-05-08T09:30:55.232Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-08T09:30:55.837Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-08T09:30:55.837Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-10T02:25:49.919Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-10T02:26:22.740Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-10T02:26:22.745Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-10T02:26:22.746Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-10T02:26:22.754Z] Checked if tokens are valid: false, expires at: 1746700254196
[debug] [2025-05-10T02:26:22.754Z] Checked if tokens are valid: false, expires at: 1746700254196
[debug] [2025-05-10T02:26:22.758Z] > refreshing access token with scopes: []
[debug] [2025-05-10T02:26:22.763Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-10T02:26:22.764Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T02:26:22.812Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-10T02:26:25.101Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-10T02:26:25.105Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T02:26:25.127Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-10T02:26:25.135Z] Checked if tokens are valid: true, expires at: 1746847584105
[debug] [2025-05-10T02:26:25.136Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-10T02:26:32.143Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-10T02:26:35.838Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-10T02:26:35.839Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-05-10T02:28:17.524Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-10T02:28:58.366Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-10T02:28:58.372Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-10T02:28:58.373Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-10T02:28:58.397Z] Checked if tokens are valid: true, expires at: 1746847584105
[debug] [2025-05-10T02:28:58.399Z] Checked if tokens are valid: true, expires at: 1746847584105
[debug] [2025-05-10T02:28:58.400Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-10T02:28:58.405Z] Checked if tokens are valid: true, expires at: 1746847584105
[debug] [2025-05-10T02:28:58.416Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-10T02:29:05.932Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-10T02:29:05.933Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-05-10T02:29:07.816Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-10T05:11:57.984Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-10T05:12:20.889Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-10T05:12:20.904Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-10T05:12:20.914Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-10T05:12:20.923Z] Checked if tokens are valid: false, expires at: 1746847584105
[debug] [2025-05-10T05:12:20.925Z] Checked if tokens are valid: false, expires at: 1746847584105
[debug] [2025-05-10T05:12:20.927Z] > refreshing access token with scopes: []
[debug] [2025-05-10T05:12:20.930Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-10T05:12:20.931Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T05:12:20.965Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-10T05:12:21.967Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-10T05:12:26.631Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-10T05:12:26.633Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T05:12:26.653Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-10T05:12:26.658Z] Checked if tokens are valid: true, expires at: 1746857545635
[debug] [2025-05-10T05:12:26.661Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-10T05:12:29.804Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-10T05:12:29.805Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-10T07:54:49.719Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-10T07:55:13.980Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-10T07:55:14.005Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-10T07:55:14.006Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-10T07:55:14.015Z] Checked if tokens are valid: false, expires at: 1746857545635
[debug] [2025-05-10T07:55:14.016Z] Checked if tokens are valid: false, expires at: 1746857545635
[debug] [2025-05-10T07:55:14.023Z] > refreshing access token with scopes: []
[debug] [2025-05-10T07:55:14.030Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-10T07:55:14.030Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T07:55:14.069Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-10T07:55:15.529Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-10T07:55:16.220Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-10T07:55:16.221Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T07:55:16.264Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-10T07:55:16.267Z] Checked if tokens are valid: true, expires at: 1746867315222
[debug] [2025-05-10T07:55:16.268Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-10T07:55:17.203Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-10T07:55:17.203Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-10T16:14:24.852Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-10T16:15:22.728Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-10T16:15:22.752Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-10T16:15:22.754Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-10T16:15:22.775Z] Checked if tokens are valid: false, expires at: 1746867315222
[debug] [2025-05-10T16:15:22.779Z] Checked if tokens are valid: false, expires at: 1746867315222
[debug] [2025-05-10T16:15:22.782Z] > refreshing access token with scopes: []
[debug] [2025-05-10T16:15:22.794Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-10T16:15:22.797Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T16:15:22.856Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-10T16:16:01.788Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-10T16:16:02.493Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-10T16:16:02.495Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-10T16:16:02.579Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-10T16:16:02.599Z] Checked if tokens are valid: true, expires at: 1746897361496
[debug] [2025-05-10T16:16:02.607Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-10T16:16:08.093Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-10T16:16:08.100Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-05-11T02:54:07.267Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-05-11T02:54:39.286Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-05-11T02:54:39.290Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-11T02:54:39.292Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-05-11T02:54:39.308Z] Checked if tokens are valid: false, expires at: 1746897361496
[debug] [2025-05-11T02:54:39.309Z] Checked if tokens are valid: false, expires at: 1746897361496
[debug] [2025-05-11T02:54:39.310Z] > refreshing access token with scopes: []
[debug] [2025-05-11T02:54:39.314Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-11T02:54:39.316Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-11T02:54:39.342Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-05-11T02:54:40.625Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-05-11T02:54:40.666Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-11T02:54:40.667Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-11T02:54:40.681Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-05-11T02:54:40.684Z] Checked if tokens are valid: true, expires at: 1746935679667
[debug] [2025-05-11T02:54:40.685Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-05-11T02:54:41.742Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-05-11T02:54:41.742Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-09T06:37:46.578Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-09T06:38:02.395Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-09T06:38:02.410Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-09T06:38:02.415Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-09T06:38:02.444Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:38:02.452Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:38:02.456Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-09T06:38:02.476Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:38:02.491Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-09T06:38:02.612Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-09T06:38:17.920Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-09T06:40:37.468Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-09T06:40:40.333Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-09T06:40:40.340Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-09T06:40:40.342Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-09T06:40:40.352Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:40:40.353Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:40:40.355Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-09T06:40:40.361Z] Checked if tokens are valid: true, expires at: 1749453141470
[debug] [2025-06-09T06:40:40.367Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-09T06:40:40.404Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-09T06:40:42.037Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-09T06:40:42.385Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-09T06:40:42.387Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-10T04:30:15.642Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-10T04:30:20.924Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-10T04:30:20.927Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-10T04:30:20.928Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-10T04:30:20.934Z] Checked if tokens are valid: false, expires at: 1749453141470
[debug] [2025-06-10T04:30:20.935Z] Checked if tokens are valid: false, expires at: 1749453141470
[debug] [2025-06-10T04:30:20.937Z] > refreshing access token with scopes: []
[debug] [2025-06-10T04:30:20.940Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-10T04:30:20.942Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-10T04:30:20.966Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-10T04:30:21.681Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-10T04:30:21.823Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-10T04:30:21.824Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-10T04:30:21.839Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-10T04:30:21.842Z] Checked if tokens are valid: true, expires at: 1749533420825
[debug] [2025-06-10T04:30:21.844Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-10T04:30:22.475Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-10T04:30:22.475Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-10T16:04:35.211Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-10T16:04:40.475Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-10T16:04:40.481Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-10T16:04:40.482Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-10T16:04:40.499Z] Checked if tokens are valid: false, expires at: 1749533420825
[debug] [2025-06-10T16:04:40.501Z] Checked if tokens are valid: false, expires at: 1749533420825
[debug] [2025-06-10T16:04:40.502Z] > refreshing access token with scopes: []
[debug] [2025-06-10T16:04:40.513Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-10T16:04:40.514Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-10T16:04:40.544Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-10T16:04:40.816Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-10T16:04:40.816Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-10T16:04:40.829Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-10T16:04:40.832Z] Checked if tokens are valid: true, expires at: 1749575079817
[debug] [2025-06-10T16:04:40.833Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-10T16:04:41.663Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-10T16:04:42.700Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-10T16:04:42.701Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-11T08:05:44.017Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-11T08:05:50.441Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-11T08:05:50.446Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-11T08:05:50.447Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-11T08:05:50.457Z] Checked if tokens are valid: false, expires at: 1749575079817
[debug] [2025-06-11T08:05:50.458Z] Checked if tokens are valid: false, expires at: 1749575079817
[debug] [2025-06-11T08:05:50.459Z] > refreshing access token with scopes: []
[debug] [2025-06-11T08:05:50.462Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-11T08:05:50.463Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-11T08:05:50.491Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-11T08:05:50.714Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-11T08:05:50.715Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-11T08:05:50.747Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-11T08:05:50.750Z] Checked if tokens are valid: true, expires at: 1749632749716
[debug] [2025-06-11T08:05:50.754Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-11T08:05:51.782Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-11T08:05:53.055Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-11T08:05:53.056Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-12T09:54:31.656Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-12T09:54:38.656Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-12T09:54:38.659Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-12T09:54:38.660Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-12T09:54:38.738Z] Checked if tokens are valid: false, expires at: 1749632749716
[debug] [2025-06-12T09:54:38.739Z] Checked if tokens are valid: false, expires at: 1749632749716
[debug] [2025-06-12T09:54:38.740Z] > refreshing access token with scopes: []
[debug] [2025-06-12T09:54:38.743Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-12T09:54:38.743Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-12T09:54:38.805Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-12T09:54:40.729Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-12T09:54:40.873Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-12T09:54:40.874Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-12T09:54:40.888Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-12T09:54:40.892Z] Checked if tokens are valid: true, expires at: 1749725679874
[debug] [2025-06-12T09:54:40.894Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-12T09:54:42.612Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-12T09:54:42.614Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-15T13:55:41.499Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-15T13:55:47.221Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-15T13:55:47.225Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-15T13:55:47.226Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-15T13:55:47.232Z] Checked if tokens are valid: false, expires at: 1749816404024
[debug] [2025-06-15T13:55:47.233Z] Checked if tokens are valid: false, expires at: 1749816404024
[debug] [2025-06-15T13:55:47.234Z] > refreshing access token with scopes: []
[debug] [2025-06-15T13:55:47.237Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-15T13:55:47.238Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T13:55:47.263Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-15T13:55:47.702Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-15T13:55:47.702Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T13:55:47.717Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-15T13:55:47.719Z] Checked if tokens are valid: true, expires at: 1749999346703
[debug] [2025-06-15T13:55:47.721Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-15T13:55:48.611Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-15T13:55:48.653Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-15T13:55:48.654Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-15T15:09:07.621Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-15T15:09:14.786Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-15T15:09:14.803Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-15T15:09:14.805Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-15T15:09:14.840Z] Checked if tokens are valid: false, expires at: 1749999346703
[debug] [2025-06-15T15:09:14.841Z] Checked if tokens are valid: false, expires at: 1749999346703
[debug] [2025-06-15T15:09:14.843Z] > refreshing access token with scopes: []
[debug] [2025-06-15T15:09:14.851Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-15T15:09:14.854Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T15:09:14.887Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-15T15:09:15.383Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-15T15:09:15.384Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T15:09:15.399Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-15T15:09:15.401Z] Checked if tokens are valid: true, expires at: 1750003754385
[debug] [2025-06-15T15:09:15.403Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-15T15:09:16.470Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-15T15:09:16.568Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-15T15:09:16.569Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-15T15:57:46.474Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-15T15:57:49.448Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-15T15:57:49.494Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-15T15:57:49.503Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-15T15:57:49.671Z] Checked if tokens are valid: false, expires at: 1750003754385
[debug] [2025-06-15T15:57:49.686Z] Checked if tokens are valid: false, expires at: 1750003754385
[debug] [2025-06-15T15:57:49.702Z] > refreshing access token with scopes: []
[debug] [2025-06-15T15:57:49.760Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-15T15:57:49.761Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T15:57:49.843Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-15T15:57:53.428Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-15T15:57:53.429Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-15T15:57:53.450Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-15T15:57:53.454Z] Checked if tokens are valid: true, expires at: 1750006672430
[debug] [2025-06-15T15:57:53.456Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-15T15:58:03.258Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-15T15:58:05.100Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-15T15:58:05.101Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-17T13:03:25.134Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-17T13:03:31.026Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T13:03:31.029Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T13:03:31.030Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T13:03:31.035Z] Checked if tokens are valid: false, expires at: 1750006672430
[debug] [2025-06-17T13:03:31.036Z] Checked if tokens are valid: false, expires at: 1750006672430
[debug] [2025-06-17T13:03:31.037Z] > refreshing access token with scopes: []
[debug] [2025-06-17T13:03:31.041Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-17T13:03:31.042Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T13:03:31.080Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-17T13:03:31.826Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-17T13:03:31.827Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-17T13:03:31.839Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T13:03:31.841Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:03:31.843Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T13:03:32.935Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T13:03:32.935Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-17T13:24:05.533Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-17T13:24:14.541Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T13:24:14.557Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T13:24:14.558Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T13:24:14.565Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:24:14.566Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:24:14.566Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T13:24:14.595Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:24:14.599Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T13:24:14.620Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-17T13:24:16.689Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-17T13:24:16.880Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T13:24:16.880Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-17T13:47:08.804Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-17T13:47:16.823Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-17T13:47:16.826Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-17T13:47:16.827Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-17T13:47:16.833Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:47:16.834Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:47:16.835Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-17T13:47:16.839Z] Checked if tokens are valid: true, expires at: 1750169010827
[debug] [2025-06-17T13:47:16.842Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-17T13:47:16.863Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-17T13:47:17.679Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-17T13:47:17.679Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[debug] [2025-06-17T13:47:19.024Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[info] [Firebase Plugin] Stopping Data Connect toolkit
[debug] [2025-06-17T13:59:33.993Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-19T06:47:47.486Z] [Firebase Plugin] Activating Firebase extension.
[debug] [2025-06-19T06:47:54.190Z] [Firebase Plugin] requireAuthWrapper
[debug] [2025-06-19T06:47:54.194Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-19T06:47:54.197Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-19T06:47:54.210Z] Checked if tokens are valid: false, expires at: 1750169010827
[debug] [2025-06-19T06:47:54.213Z] Checked if tokens are valid: false, expires at: 1750169010827
[debug] [2025-06-19T06:47:54.214Z] > refreshing access token with scopes: []
[debug] [2025-06-19T06:47:54.229Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-19T06:47:54.230Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-19T06:47:54.259Z] [Firebase Plugin] VSCode notification server listening on port 40001
[debug] [2025-06-19T06:47:56.064Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-06-19T06:47:56.217Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-19T06:47:56.217Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-19T06:47:56.250Z] [Firebase Plugin] User found:  <EMAIL>
[info] [Firebase Plugin] (Core:Project) New user detected, fetching projects
[debug] [2025-06-19T06:47:56.253Z] Checked if tokens are valid: true, expires at: 1750319274218
[debug] [2025-06-19T06:47:56.255Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=1000
[debug] [2025-06-19T06:47:57.894Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-06-19T06:47:57.895Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
[info] [Firebase Plugin] Stopping Data Connect toolkit
