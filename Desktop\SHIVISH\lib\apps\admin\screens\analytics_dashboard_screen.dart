import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/analytics/analytics_bloc.dart';
import 'package:shivish/apps/admin/bloc/analytics/analytics_event.dart';
import 'package:shivish/apps/admin/bloc/analytics/analytics_state.dart';
import 'package:shivish/apps/admin/widgets/analytics_filter_dialog.dart';
import 'package:shivish/apps/admin/widgets/analytics_drill_down_view.dart';
import 'package:shivish/shared/models/analytics/analytics_model.dart';
import 'package:shivish/shared/services/export/export_service.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:share_plus/share_plus.dart';

class AnalyticsDashboardScreen extends StatefulWidget {
  const AnalyticsDashboardScreen({super.key});

  @override
  State<AnalyticsDashboardScreen> createState() =>
      _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
  String _selectedTimeRange = 'week';
  final _exportService = ExportService();
  AnalyticsFilterData? _currentFilters;

  @override
  void initState() {
    super.initState();
    context.read<AnalyticsBloc>().add(
          const AnalyticsEvent.loadData(timeRange: 'week'),
        );
  }

  Future<void> _exportData(AnalyticsData data, String format) async {
    try {
      String filePath;
      if (format == 'csv') {
        filePath = await _exportService.exportAnalyticsDataToCsv(data);
      } else {
        filePath = await _exportService.exportAnalyticsDataToJson(data);
      }
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showExportDialog(AnalyticsData data) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Analytics Data'),
        content: const Text('Choose the export format:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportData(data, 'csv');
            },
            child: const Text('CSV'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _exportData(data, 'json');
            },
            child: const Text('JSON'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AnalyticsFilterDialog(
        currentFilters: _currentFilters ?? const AnalyticsFilterData(),
        onApply: (filters) {
          setState(() => _currentFilters = filters);
          context.read<AnalyticsBloc>().add(
                AnalyticsEvent.applyFilters(filters),
              );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
            tooltip: 'Filter Analytics',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: () {
              final state = context.read<AnalyticsBloc>().state;
              state.maybeWhen(
                loaded: (data, timeRange, filters) => _showExportDialog(data),
                orElse: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please wait for data to load'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                },
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context
                  .read<AnalyticsBloc>()
                  .add(const AnalyticsEvent.refreshData());
            },
          ),
        ],
      ),
      body: BlocBuilder<AnalyticsBloc, AnalyticsState>(
        builder: (context, state) {
          return state.when(
            initial: () => const Center(child: Text('Select a time range')),
            loading: () => const Center(child: CircularProgressIndicator()),
            loaded: (data, timeRange, filters) => _buildDashboard(context, data,
                timeRange: timeRange, filters: filters),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDashboard(
    BuildContext context,
    AnalyticsData data, {
    String timeRange = 'week',
    AnalyticsFilterData? filters,
  }) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<AnalyticsBloc>().add(const AnalyticsEvent.refreshData());
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTimeRangeSelector(context),
            const SizedBox(height: 24),
            _buildMetricsGrid(data),
            const SizedBox(height: 24),
            _buildSalesChart(data.salesData),
            const SizedBox(height: 24),
            _buildTopProducts(data.topProducts),
            const SizedBox(height: 24),
            _buildCustomerMetrics(data.customerMetrics),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRangeSelector(BuildContext context) {
    return Row(
      children: [
        const Text('Time Range: '),
        const SizedBox(width: 8),
        DropdownButton<String>(
          value: _selectedTimeRange,
          items: const [
            DropdownMenuItem(value: 'day', child: Text('Today')),
            DropdownMenuItem(value: 'week', child: Text('This Week')),
            DropdownMenuItem(value: 'month', child: Text('This Month')),
            DropdownMenuItem(value: 'year', child: Text('This Year')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedTimeRange = value);
              context.read<AnalyticsBloc>().add(
                    AnalyticsEvent.updateTimeRange(timeRange: value),
                  );
            }
          },
        ),
      ],
    );
  }

  Widget _buildMetricsGrid(AnalyticsData data) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'Total Sales',
          '₹${data.totalSales.toStringAsFixed(2)}',
          Icons.currency_rupee,
          Colors.green,
          onTap: () => _showDrillDownView(
            context,
            'Sales Analytics',
            'sales',
            data,
          ),
        ),
        _buildMetricCard(
          'Total Orders',
          data.totalOrders.toString(),
          Icons.shopping_cart,
          Colors.blue,
          onTap: () => _showDrillDownView(
            context,
            'Product Analytics',
            'products',
            data,
          ),
        ),
        _buildMetricCard(
          'Average Order Value',
          '₹${data.averageOrderValue.toStringAsFixed(2)}',
          Icons.trending_up,
          Colors.orange,
          onTap: () => _showDrillDownView(
            context,
            'Sales Analytics',
            'sales',
            data,
          ),
        ),
        _buildMetricCard(
          'Customer Retention',
          '${data.customerMetrics.customerRetentionRate.toStringAsFixed(1)}%',
          Icons.people,
          Colors.purple,
          onTap: () => _showDrillDownView(
            context,
            'Customer Analytics',
            'customers',
            data,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showDrillDownView(
    BuildContext context,
    String title,
    String metricType,
    AnalyticsData data,
  ) {
    showDialog(
      context: context,
      builder: (context) => AnalyticsDrillDownView(
        title: title,
        metricType: metricType,
        data: data,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }

  Widget _buildSalesChart(List<SalesDataPoint> salesData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Sales Trend',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= salesData.length) {
                            return const Text('');
                          }
                          return Text(salesData[value.toInt()].date);
                        },
                      ),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: salesData
                          .asMap()
                          .entries
                          .map((e) => FlSpot(e.key.toDouble(), e.value.amount))
                          .toList(),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 3,
                      dotData: const FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProducts(List<TopProduct> topProducts) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top Products',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: topProducts.length,
              itemBuilder: (context, index) {
                final product = topProducts[index];
                return ListTile(
                  leading: Image.network(
                    product.imageUrl,
                    width: 50,
                    height: 50,
                    fit: BoxFit.cover,
                  ),
                  title: Text(product.name),
                  subtitle: Text('Quantity: ${product.quantity}'),
                  trailing: Text(
                    '₹${product.revenue.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerMetrics(CustomerMetrics metrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Customer Metrics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildCustomerMetricRow(
              'New Customers',
              metrics.newCustomers.toString(),
              Icons.person_add,
            ),
            _buildCustomerMetricRow(
              'Repeat Customers',
              metrics.repeatCustomers.toString(),
              Icons.people,
            ),
            _buildCustomerMetricRow(
              'Customer Retention Rate',
              '${metrics.customerRetentionRate.toStringAsFixed(1)}%',
              Icons.trending_up,
            ),
            _buildCustomerMetricRow(
              'Customer Satisfaction',
              '${metrics.customerSatisfaction.toStringAsFixed(1)}%',
              Icons.sentiment_satisfied,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerMetricRow(
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue),
          const SizedBox(width: 8),
          Expanded(
            child: Text(label),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
