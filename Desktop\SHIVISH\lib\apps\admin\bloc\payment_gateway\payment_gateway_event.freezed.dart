// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_gateway_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentGatewayEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentGatewayEventCopyWith<$Res> {
  factory $PaymentGatewayEventCopyWith(
          PaymentGatewayEvent value, $Res Function(PaymentGatewayEvent) then) =
      _$PaymentGatewayEventCopyWithImpl<$Res, PaymentGatewayEvent>;
}

/// @nodoc
class _$PaymentGatewayEventCopyWithImpl<$Res, $Val extends PaymentGatewayEvent>
    implements $PaymentGatewayEventCopyWith<$Res> {
  _$PaymentGatewayEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadGatewaysImplCopyWith<$Res> {
  factory _$$LoadGatewaysImplCopyWith(
          _$LoadGatewaysImpl value, $Res Function(_$LoadGatewaysImpl) then) =
      __$$LoadGatewaysImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadGatewaysImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$LoadGatewaysImpl>
    implements _$$LoadGatewaysImplCopyWith<$Res> {
  __$$LoadGatewaysImplCopyWithImpl(
      _$LoadGatewaysImpl _value, $Res Function(_$LoadGatewaysImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadGatewaysImpl implements LoadGateways {
  const _$LoadGatewaysImpl();

  @override
  String toString() {
    return 'PaymentGatewayEvent.loadGateways()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadGatewaysImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return loadGateways();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return loadGateways?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (loadGateways != null) {
      return loadGateways();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return loadGateways(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return loadGateways?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (loadGateways != null) {
      return loadGateways(this);
    }
    return orElse();
  }
}

abstract class LoadGateways implements PaymentGatewayEvent {
  const factory LoadGateways() = _$LoadGatewaysImpl;
}

/// @nodoc
abstract class _$$CreatePaymentGatewayImplCopyWith<$Res> {
  factory _$$CreatePaymentGatewayImplCopyWith(_$CreatePaymentGatewayImpl value,
          $Res Function(_$CreatePaymentGatewayImpl) then) =
      __$$CreatePaymentGatewayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PaymentGateway gateway});

  $PaymentGatewayCopyWith<$Res> get gateway;
}

/// @nodoc
class __$$CreatePaymentGatewayImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$CreatePaymentGatewayImpl>
    implements _$$CreatePaymentGatewayImplCopyWith<$Res> {
  __$$CreatePaymentGatewayImplCopyWithImpl(_$CreatePaymentGatewayImpl _value,
      $Res Function(_$CreatePaymentGatewayImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gateway = null,
  }) {
    return _then(_$CreatePaymentGatewayImpl(
      null == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as PaymentGateway,
    ));
  }

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentGatewayCopyWith<$Res> get gateway {
    return $PaymentGatewayCopyWith<$Res>(_value.gateway, (value) {
      return _then(_value.copyWith(gateway: value));
    });
  }
}

/// @nodoc

class _$CreatePaymentGatewayImpl implements CreatePaymentGateway {
  const _$CreatePaymentGatewayImpl(this.gateway);

  @override
  final PaymentGateway gateway;

  @override
  String toString() {
    return 'PaymentGatewayEvent.createPaymentGateway(gateway: $gateway)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreatePaymentGatewayImpl &&
            (identical(other.gateway, gateway) || other.gateway == gateway));
  }

  @override
  int get hashCode => Object.hash(runtimeType, gateway);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreatePaymentGatewayImplCopyWith<_$CreatePaymentGatewayImpl>
      get copyWith =>
          __$$CreatePaymentGatewayImplCopyWithImpl<_$CreatePaymentGatewayImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return createPaymentGateway(gateway);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return createPaymentGateway?.call(gateway);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (createPaymentGateway != null) {
      return createPaymentGateway(gateway);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return createPaymentGateway(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return createPaymentGateway?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (createPaymentGateway != null) {
      return createPaymentGateway(this);
    }
    return orElse();
  }
}

abstract class CreatePaymentGateway implements PaymentGatewayEvent {
  const factory CreatePaymentGateway(final PaymentGateway gateway) =
      _$CreatePaymentGatewayImpl;

  PaymentGateway get gateway;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreatePaymentGatewayImplCopyWith<_$CreatePaymentGatewayImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePaymentGatewayImplCopyWith<$Res> {
  factory _$$UpdatePaymentGatewayImplCopyWith(_$UpdatePaymentGatewayImpl value,
          $Res Function(_$UpdatePaymentGatewayImpl) then) =
      __$$UpdatePaymentGatewayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({PaymentGateway gateway});

  $PaymentGatewayCopyWith<$Res> get gateway;
}

/// @nodoc
class __$$UpdatePaymentGatewayImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$UpdatePaymentGatewayImpl>
    implements _$$UpdatePaymentGatewayImplCopyWith<$Res> {
  __$$UpdatePaymentGatewayImplCopyWithImpl(_$UpdatePaymentGatewayImpl _value,
      $Res Function(_$UpdatePaymentGatewayImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gateway = null,
  }) {
    return _then(_$UpdatePaymentGatewayImpl(
      null == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as PaymentGateway,
    ));
  }

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentGatewayCopyWith<$Res> get gateway {
    return $PaymentGatewayCopyWith<$Res>(_value.gateway, (value) {
      return _then(_value.copyWith(gateway: value));
    });
  }
}

/// @nodoc

class _$UpdatePaymentGatewayImpl implements UpdatePaymentGateway {
  const _$UpdatePaymentGatewayImpl(this.gateway);

  @override
  final PaymentGateway gateway;

  @override
  String toString() {
    return 'PaymentGatewayEvent.updatePaymentGateway(gateway: $gateway)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePaymentGatewayImpl &&
            (identical(other.gateway, gateway) || other.gateway == gateway));
  }

  @override
  int get hashCode => Object.hash(runtimeType, gateway);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePaymentGatewayImplCopyWith<_$UpdatePaymentGatewayImpl>
      get copyWith =>
          __$$UpdatePaymentGatewayImplCopyWithImpl<_$UpdatePaymentGatewayImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return updatePaymentGateway(gateway);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return updatePaymentGateway?.call(gateway);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updatePaymentGateway != null) {
      return updatePaymentGateway(gateway);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return updatePaymentGateway(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return updatePaymentGateway?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updatePaymentGateway != null) {
      return updatePaymentGateway(this);
    }
    return orElse();
  }
}

abstract class UpdatePaymentGateway implements PaymentGatewayEvent {
  const factory UpdatePaymentGateway(final PaymentGateway gateway) =
      _$UpdatePaymentGatewayImpl;

  PaymentGateway get gateway;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePaymentGatewayImplCopyWith<_$UpdatePaymentGatewayImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeletePaymentGatewayImplCopyWith<$Res> {
  factory _$$DeletePaymentGatewayImplCopyWith(_$DeletePaymentGatewayImpl value,
          $Res Function(_$DeletePaymentGatewayImpl) then) =
      __$$DeletePaymentGatewayImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeletePaymentGatewayImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$DeletePaymentGatewayImpl>
    implements _$$DeletePaymentGatewayImplCopyWith<$Res> {
  __$$DeletePaymentGatewayImplCopyWithImpl(_$DeletePaymentGatewayImpl _value,
      $Res Function(_$DeletePaymentGatewayImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeletePaymentGatewayImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeletePaymentGatewayImpl implements DeletePaymentGateway {
  const _$DeletePaymentGatewayImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'PaymentGatewayEvent.deletePaymentGateway(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeletePaymentGatewayImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeletePaymentGatewayImplCopyWith<_$DeletePaymentGatewayImpl>
      get copyWith =>
          __$$DeletePaymentGatewayImplCopyWithImpl<_$DeletePaymentGatewayImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return deletePaymentGateway(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return deletePaymentGateway?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (deletePaymentGateway != null) {
      return deletePaymentGateway(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return deletePaymentGateway(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return deletePaymentGateway?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (deletePaymentGateway != null) {
      return deletePaymentGateway(this);
    }
    return orElse();
  }
}

abstract class DeletePaymentGateway implements PaymentGatewayEvent {
  const factory DeletePaymentGateway(final String id) =
      _$DeletePaymentGatewayImpl;

  String get id;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeletePaymentGatewayImplCopyWith<_$DeletePaymentGatewayImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateGatewayStatusImplCopyWith<$Res> {
  factory _$$UpdateGatewayStatusImplCopyWith(_$UpdateGatewayStatusImpl value,
          $Res Function(_$UpdateGatewayStatusImpl) then) =
      __$$UpdateGatewayStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, bool isActive});
}

/// @nodoc
class __$$UpdateGatewayStatusImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$UpdateGatewayStatusImpl>
    implements _$$UpdateGatewayStatusImplCopyWith<$Res> {
  __$$UpdateGatewayStatusImplCopyWithImpl(_$UpdateGatewayStatusImpl _value,
      $Res Function(_$UpdateGatewayStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isActive = null,
  }) {
    return _then(_$UpdateGatewayStatusImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UpdateGatewayStatusImpl implements UpdateGatewayStatus {
  const _$UpdateGatewayStatusImpl(this.id, this.isActive);

  @override
  final String id;
  @override
  final bool isActive;

  @override
  String toString() {
    return 'PaymentGatewayEvent.updateGatewayStatus(id: $id, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateGatewayStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, isActive);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateGatewayStatusImplCopyWith<_$UpdateGatewayStatusImpl> get copyWith =>
      __$$UpdateGatewayStatusImplCopyWithImpl<_$UpdateGatewayStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return updateGatewayStatus(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return updateGatewayStatus?.call(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayStatus != null) {
      return updateGatewayStatus(id, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return updateGatewayStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return updateGatewayStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayStatus != null) {
      return updateGatewayStatus(this);
    }
    return orElse();
  }
}

abstract class UpdateGatewayStatus implements PaymentGatewayEvent {
  const factory UpdateGatewayStatus(final String id, final bool isActive) =
      _$UpdateGatewayStatusImpl;

  String get id;
  bool get isActive;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateGatewayStatusImplCopyWith<_$UpdateGatewayStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateGatewayCredentialsImplCopyWith<$Res> {
  factory _$$UpdateGatewayCredentialsImplCopyWith(
          _$UpdateGatewayCredentialsImpl value,
          $Res Function(_$UpdateGatewayCredentialsImpl) then) =
      __$$UpdateGatewayCredentialsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> credentials});
}

/// @nodoc
class __$$UpdateGatewayCredentialsImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res,
        _$UpdateGatewayCredentialsImpl>
    implements _$$UpdateGatewayCredentialsImplCopyWith<$Res> {
  __$$UpdateGatewayCredentialsImplCopyWithImpl(
      _$UpdateGatewayCredentialsImpl _value,
      $Res Function(_$UpdateGatewayCredentialsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? credentials = null,
  }) {
    return _then(_$UpdateGatewayCredentialsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == credentials
          ? _value._credentials
          : credentials // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateGatewayCredentialsImpl implements UpdateGatewayCredentials {
  const _$UpdateGatewayCredentialsImpl(
      this.id, final Map<String, dynamic> credentials)
      : _credentials = credentials;

  @override
  final String id;
  final Map<String, dynamic> _credentials;
  @override
  Map<String, dynamic> get credentials {
    if (_credentials is EqualUnmodifiableMapView) return _credentials;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_credentials);
  }

  @override
  String toString() {
    return 'PaymentGatewayEvent.updateGatewayCredentials(id: $id, credentials: $credentials)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateGatewayCredentialsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._credentials, _credentials));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_credentials));

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateGatewayCredentialsImplCopyWith<_$UpdateGatewayCredentialsImpl>
      get copyWith => __$$UpdateGatewayCredentialsImplCopyWithImpl<
          _$UpdateGatewayCredentialsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return updateGatewayCredentials(id, credentials);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return updateGatewayCredentials?.call(id, credentials);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayCredentials != null) {
      return updateGatewayCredentials(id, credentials);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return updateGatewayCredentials(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return updateGatewayCredentials?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayCredentials != null) {
      return updateGatewayCredentials(this);
    }
    return orElse();
  }
}

abstract class UpdateGatewayCredentials implements PaymentGatewayEvent {
  const factory UpdateGatewayCredentials(
          final String id, final Map<String, dynamic> credentials) =
      _$UpdateGatewayCredentialsImpl;

  String get id;
  Map<String, dynamic> get credentials;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateGatewayCredentialsImplCopyWith<_$UpdateGatewayCredentialsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateGatewayFeesImplCopyWith<$Res> {
  factory _$$UpdateGatewayFeesImplCopyWith(_$UpdateGatewayFeesImpl value,
          $Res Function(_$UpdateGatewayFeesImpl) then) =
      __$$UpdateGatewayFeesImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> fees});
}

/// @nodoc
class __$$UpdateGatewayFeesImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res, _$UpdateGatewayFeesImpl>
    implements _$$UpdateGatewayFeesImplCopyWith<$Res> {
  __$$UpdateGatewayFeesImplCopyWithImpl(_$UpdateGatewayFeesImpl _value,
      $Res Function(_$UpdateGatewayFeesImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fees = null,
  }) {
    return _then(_$UpdateGatewayFeesImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == fees
          ? _value._fees
          : fees // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateGatewayFeesImpl implements UpdateGatewayFees {
  const _$UpdateGatewayFeesImpl(this.id, final Map<String, dynamic> fees)
      : _fees = fees;

  @override
  final String id;
  final Map<String, dynamic> _fees;
  @override
  Map<String, dynamic> get fees {
    if (_fees is EqualUnmodifiableMapView) return _fees;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_fees);
  }

  @override
  String toString() {
    return 'PaymentGatewayEvent.updateGatewayFees(id: $id, fees: $fees)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateGatewayFeesImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._fees, _fees));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, const DeepCollectionEquality().hash(_fees));

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateGatewayFeesImplCopyWith<_$UpdateGatewayFeesImpl> get copyWith =>
      __$$UpdateGatewayFeesImplCopyWithImpl<_$UpdateGatewayFeesImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return updateGatewayFees(id, fees);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return updateGatewayFees?.call(id, fees);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayFees != null) {
      return updateGatewayFees(id, fees);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return updateGatewayFees(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return updateGatewayFees?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (updateGatewayFees != null) {
      return updateGatewayFees(this);
    }
    return orElse();
  }
}

abstract class UpdateGatewayFees implements PaymentGatewayEvent {
  const factory UpdateGatewayFees(
          final String id, final Map<String, dynamic> fees) =
      _$UpdateGatewayFeesImpl;

  String get id;
  Map<String, dynamic> get fees;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateGatewayFeesImplCopyWith<_$UpdateGatewayFeesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CalculateTransactionFeeImplCopyWith<$Res> {
  factory _$$CalculateTransactionFeeImplCopyWith(
          _$CalculateTransactionFeeImpl value,
          $Res Function(_$CalculateTransactionFeeImpl) then) =
      __$$CalculateTransactionFeeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, double amount});
}

/// @nodoc
class __$$CalculateTransactionFeeImplCopyWithImpl<$Res>
    extends _$PaymentGatewayEventCopyWithImpl<$Res,
        _$CalculateTransactionFeeImpl>
    implements _$$CalculateTransactionFeeImplCopyWith<$Res> {
  __$$CalculateTransactionFeeImplCopyWithImpl(
      _$CalculateTransactionFeeImpl _value,
      $Res Function(_$CalculateTransactionFeeImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? amount = null,
  }) {
    return _then(_$CalculateTransactionFeeImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

class _$CalculateTransactionFeeImpl implements CalculateTransactionFee {
  const _$CalculateTransactionFeeImpl(this.id, this.amount);

  @override
  final String id;
  @override
  final double amount;

  @override
  String toString() {
    return 'PaymentGatewayEvent.calculateTransactionFee(id: $id, amount: $amount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalculateTransactionFeeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.amount, amount) || other.amount == amount));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, amount);

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalculateTransactionFeeImplCopyWith<_$CalculateTransactionFeeImpl>
      get copyWith => __$$CalculateTransactionFeeImplCopyWithImpl<
          _$CalculateTransactionFeeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadGateways,
    required TResult Function(PaymentGateway gateway) createPaymentGateway,
    required TResult Function(PaymentGateway gateway) updatePaymentGateway,
    required TResult Function(String id) deletePaymentGateway,
    required TResult Function(String id, bool isActive) updateGatewayStatus,
    required TResult Function(String id, Map<String, dynamic> credentials)
        updateGatewayCredentials,
    required TResult Function(String id, Map<String, dynamic> fees)
        updateGatewayFees,
    required TResult Function(String id, double amount) calculateTransactionFee,
  }) {
    return calculateTransactionFee(id, amount);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadGateways,
    TResult? Function(PaymentGateway gateway)? createPaymentGateway,
    TResult? Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult? Function(String id)? deletePaymentGateway,
    TResult? Function(String id, bool isActive)? updateGatewayStatus,
    TResult? Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult? Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult? Function(String id, double amount)? calculateTransactionFee,
  }) {
    return calculateTransactionFee?.call(id, amount);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadGateways,
    TResult Function(PaymentGateway gateway)? createPaymentGateway,
    TResult Function(PaymentGateway gateway)? updatePaymentGateway,
    TResult Function(String id)? deletePaymentGateway,
    TResult Function(String id, bool isActive)? updateGatewayStatus,
    TResult Function(String id, Map<String, dynamic> credentials)?
        updateGatewayCredentials,
    TResult Function(String id, Map<String, dynamic> fees)? updateGatewayFees,
    TResult Function(String id, double amount)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (calculateTransactionFee != null) {
      return calculateTransactionFee(id, amount);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadGateways value) loadGateways,
    required TResult Function(CreatePaymentGateway value) createPaymentGateway,
    required TResult Function(UpdatePaymentGateway value) updatePaymentGateway,
    required TResult Function(DeletePaymentGateway value) deletePaymentGateway,
    required TResult Function(UpdateGatewayStatus value) updateGatewayStatus,
    required TResult Function(UpdateGatewayCredentials value)
        updateGatewayCredentials,
    required TResult Function(UpdateGatewayFees value) updateGatewayFees,
    required TResult Function(CalculateTransactionFee value)
        calculateTransactionFee,
  }) {
    return calculateTransactionFee(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadGateways value)? loadGateways,
    TResult? Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult? Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult? Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult? Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult? Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult? Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult? Function(CalculateTransactionFee value)? calculateTransactionFee,
  }) {
    return calculateTransactionFee?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadGateways value)? loadGateways,
    TResult Function(CreatePaymentGateway value)? createPaymentGateway,
    TResult Function(UpdatePaymentGateway value)? updatePaymentGateway,
    TResult Function(DeletePaymentGateway value)? deletePaymentGateway,
    TResult Function(UpdateGatewayStatus value)? updateGatewayStatus,
    TResult Function(UpdateGatewayCredentials value)? updateGatewayCredentials,
    TResult Function(UpdateGatewayFees value)? updateGatewayFees,
    TResult Function(CalculateTransactionFee value)? calculateTransactionFee,
    required TResult orElse(),
  }) {
    if (calculateTransactionFee != null) {
      return calculateTransactionFee(this);
    }
    return orElse();
  }
}

abstract class CalculateTransactionFee implements PaymentGatewayEvent {
  const factory CalculateTransactionFee(final String id, final double amount) =
      _$CalculateTransactionFeeImpl;

  String get id;
  double get amount;

  /// Create a copy of PaymentGatewayEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalculateTransactionFeeImplCopyWith<_$CalculateTransactionFeeImpl>
      get copyWith => throw _privateConstructorUsedError;
}
