// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'technician_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TechnicianEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TechnicianEventCopyWith<$Res> {
  factory $TechnicianEventCopyWith(
          TechnicianEvent value, $Res Function(TechnicianEvent) then) =
      _$TechnicianEventCopyWithImpl<$Res, TechnicianEvent>;
}

/// @nodoc
class _$TechnicianEventCopyWithImpl<$Res, $Val extends TechnicianEvent>
    implements $TechnicianEventCopyWith<$Res> {
  _$TechnicianEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadTechniciansImplCopyWith<$Res> {
  factory _$$LoadTechniciansImplCopyWith(_$LoadTechniciansImpl value,
          $Res Function(_$LoadTechniciansImpl) then) =
      __$$LoadTechniciansImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadTechniciansImplCopyWithImpl<$Res>
    extends _$TechnicianEventCopyWithImpl<$Res, _$LoadTechniciansImpl>
    implements _$$LoadTechniciansImplCopyWith<$Res> {
  __$$LoadTechniciansImplCopyWithImpl(
      _$LoadTechniciansImpl _value, $Res Function(_$LoadTechniciansImpl) _then)
      : super(_value, _then);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadTechniciansImpl implements _LoadTechnicians {
  const _$LoadTechniciansImpl();

  @override
  String toString() {
    return 'TechnicianEvent.loadTechnicians()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadTechniciansImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return loadTechnicians();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return loadTechnicians?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadTechnicians != null) {
      return loadTechnicians();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return loadTechnicians(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return loadTechnicians?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadTechnicians != null) {
      return loadTechnicians(this);
    }
    return orElse();
  }
}

abstract class _LoadTechnicians implements TechnicianEvent {
  const factory _LoadTechnicians() = _$LoadTechniciansImpl;
}

/// @nodoc
abstract class _$$LoadMoreTechniciansImplCopyWith<$Res> {
  factory _$$LoadMoreTechniciansImplCopyWith(_$LoadMoreTechniciansImpl value,
          $Res Function(_$LoadMoreTechniciansImpl) then) =
      __$$LoadMoreTechniciansImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreTechniciansImplCopyWithImpl<$Res>
    extends _$TechnicianEventCopyWithImpl<$Res, _$LoadMoreTechniciansImpl>
    implements _$$LoadMoreTechniciansImplCopyWith<$Res> {
  __$$LoadMoreTechniciansImplCopyWithImpl(_$LoadMoreTechniciansImpl _value,
      $Res Function(_$LoadMoreTechniciansImpl) _then)
      : super(_value, _then);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreTechniciansImpl implements _LoadMoreTechnicians {
  const _$LoadMoreTechniciansImpl();

  @override
  String toString() {
    return 'TechnicianEvent.loadMoreTechnicians()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadMoreTechniciansImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return loadMoreTechnicians();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return loadMoreTechnicians?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadMoreTechnicians != null) {
      return loadMoreTechnicians();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return loadMoreTechnicians(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return loadMoreTechnicians?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadMoreTechnicians != null) {
      return loadMoreTechnicians(this);
    }
    return orElse();
  }
}

abstract class _LoadMoreTechnicians implements TechnicianEvent {
  const factory _LoadMoreTechnicians() = _$LoadMoreTechniciansImpl;
}

/// @nodoc
abstract class _$$UpdateTechnicianStatusImplCopyWith<$Res> {
  factory _$$UpdateTechnicianStatusImplCopyWith(
          _$UpdateTechnicianStatusImpl value,
          $Res Function(_$UpdateTechnicianStatusImpl) then) =
      __$$UpdateTechnicianStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Technician technician, String status, String? notes, bool isActive});

  $TechnicianCopyWith<$Res> get technician;
}

/// @nodoc
class __$$UpdateTechnicianStatusImplCopyWithImpl<$Res>
    extends _$TechnicianEventCopyWithImpl<$Res, _$UpdateTechnicianStatusImpl>
    implements _$$UpdateTechnicianStatusImplCopyWith<$Res> {
  __$$UpdateTechnicianStatusImplCopyWithImpl(
      _$UpdateTechnicianStatusImpl _value,
      $Res Function(_$UpdateTechnicianStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? technician = null,
    Object? status = null,
    Object? notes = freezed,
    Object? isActive = null,
  }) {
    return _then(_$UpdateTechnicianStatusImpl(
      technician: null == technician
          ? _value.technician
          : technician // ignore: cast_nullable_to_non_nullable
              as Technician,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TechnicianCopyWith<$Res> get technician {
    return $TechnicianCopyWith<$Res>(_value.technician, (value) {
      return _then(_value.copyWith(technician: value));
    });
  }
}

/// @nodoc

class _$UpdateTechnicianStatusImpl implements _UpdateTechnicianStatus {
  const _$UpdateTechnicianStatusImpl(
      {required this.technician,
      required this.status,
      this.notes,
      required this.isActive});

  @override
  final Technician technician;
  @override
  final String status;
  @override
  final String? notes;
  @override
  final bool isActive;

  @override
  String toString() {
    return 'TechnicianEvent.updateTechnicianStatus(technician: $technician, status: $status, notes: $notes, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTechnicianStatusImpl &&
            (identical(other.technician, technician) ||
                other.technician == technician) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, technician, status, notes, isActive);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTechnicianStatusImplCopyWith<_$UpdateTechnicianStatusImpl>
      get copyWith => __$$UpdateTechnicianStatusImplCopyWithImpl<
          _$UpdateTechnicianStatusImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return updateTechnicianStatus(technician, status, notes, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return updateTechnicianStatus?.call(technician, status, notes, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateTechnicianStatus != null) {
      return updateTechnicianStatus(technician, status, notes, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return updateTechnicianStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return updateTechnicianStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateTechnicianStatus != null) {
      return updateTechnicianStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdateTechnicianStatus implements TechnicianEvent {
  const factory _UpdateTechnicianStatus(
      {required final Technician technician,
      required final String status,
      final String? notes,
      required final bool isActive}) = _$UpdateTechnicianStatusImpl;

  Technician get technician;
  String get status;
  String? get notes;
  bool get isActive;

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTechnicianStatusImplCopyWith<_$UpdateTechnicianStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteTechnicianImplCopyWith<$Res> {
  factory _$$DeleteTechnicianImplCopyWith(_$DeleteTechnicianImpl value,
          $Res Function(_$DeleteTechnicianImpl) then) =
      __$$DeleteTechnicianImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteTechnicianImplCopyWithImpl<$Res>
    extends _$TechnicianEventCopyWithImpl<$Res, _$DeleteTechnicianImpl>
    implements _$$DeleteTechnicianImplCopyWith<$Res> {
  __$$DeleteTechnicianImplCopyWithImpl(_$DeleteTechnicianImpl _value,
      $Res Function(_$DeleteTechnicianImpl) _then)
      : super(_value, _then);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteTechnicianImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteTechnicianImpl implements _DeleteTechnician {
  const _$DeleteTechnicianImpl({required this.id});

  @override
  final String id;

  @override
  String toString() {
    return 'TechnicianEvent.deleteTechnician(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteTechnicianImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteTechnicianImplCopyWith<_$DeleteTechnicianImpl> get copyWith =>
      __$$DeleteTechnicianImplCopyWithImpl<_$DeleteTechnicianImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return deleteTechnician(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return deleteTechnician?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (deleteTechnician != null) {
      return deleteTechnician(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return deleteTechnician(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return deleteTechnician?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (deleteTechnician != null) {
      return deleteTechnician(this);
    }
    return orElse();
  }
}

abstract class _DeleteTechnician implements TechnicianEvent {
  const factory _DeleteTechnician({required final String id}) =
      _$DeleteTechnicianImpl;

  String get id;

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteTechnicianImplCopyWith<_$DeleteTechnicianImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApplyFiltersImplCopyWith<$Res> {
  factory _$$ApplyFiltersImplCopyWith(
          _$ApplyFiltersImpl value, $Res Function(_$ApplyFiltersImpl) then) =
      __$$ApplyFiltersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, dynamic> filters});
}

/// @nodoc
class __$$ApplyFiltersImplCopyWithImpl<$Res>
    extends _$TechnicianEventCopyWithImpl<$Res, _$ApplyFiltersImpl>
    implements _$$ApplyFiltersImplCopyWith<$Res> {
  __$$ApplyFiltersImplCopyWithImpl(
      _$ApplyFiltersImpl _value, $Res Function(_$ApplyFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_$ApplyFiltersImpl(
      filters: null == filters
          ? _value._filters
          : filters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$ApplyFiltersImpl implements _ApplyFilters {
  const _$ApplyFiltersImpl({required final Map<String, dynamic> filters})
      : _filters = filters;

  final Map<String, dynamic> _filters;
  @override
  Map<String, dynamic> get filters {
    if (_filters is EqualUnmodifiableMapView) return _filters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_filters);
  }

  @override
  String toString() {
    return 'TechnicianEvent.applyFilters(filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplyFiltersImpl &&
            const DeepCollectionEquality().equals(other._filters, _filters));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_filters));

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      __$$ApplyFiltersImplCopyWithImpl<_$ApplyFiltersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadTechnicians,
    required TResult Function() loadMoreTechnicians,
    required TResult Function(
            Technician technician, String status, String? notes, bool isActive)
        updateTechnicianStatus,
    required TResult Function(String id) deleteTechnician,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return applyFilters(filters);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadTechnicians,
    TResult? Function()? loadMoreTechnicians,
    TResult? Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult? Function(String id)? deleteTechnician,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return applyFilters?.call(filters);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadTechnicians,
    TResult Function()? loadMoreTechnicians,
    TResult Function(
            Technician technician, String status, String? notes, bool isActive)?
        updateTechnicianStatus,
    TResult Function(String id)? deleteTechnician,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(filters);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadTechnicians value) loadTechnicians,
    required TResult Function(_LoadMoreTechnicians value) loadMoreTechnicians,
    required TResult Function(_UpdateTechnicianStatus value)
        updateTechnicianStatus,
    required TResult Function(_DeleteTechnician value) deleteTechnician,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return applyFilters(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadTechnicians value)? loadTechnicians,
    TResult? Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult? Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult? Function(_DeleteTechnician value)? deleteTechnician,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return applyFilters?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadTechnicians value)? loadTechnicians,
    TResult Function(_LoadMoreTechnicians value)? loadMoreTechnicians,
    TResult Function(_UpdateTechnicianStatus value)? updateTechnicianStatus,
    TResult Function(_DeleteTechnician value)? deleteTechnician,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(this);
    }
    return orElse();
  }
}

abstract class _ApplyFilters implements TechnicianEvent {
  const factory _ApplyFilters({required final Map<String, dynamic> filters}) =
      _$ApplyFiltersImpl;

  Map<String, dynamic> get filters;

  /// Create a copy of TechnicianEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
