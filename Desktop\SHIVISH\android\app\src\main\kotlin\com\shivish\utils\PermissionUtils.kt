﻿package com.shivish.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * Utility class for handling Android permissions safely
 */
object PermissionUtils {
    private const val TAG = "PermissionUtils"

    // Common permission request codes
    const val REQUEST_CODE_STORAGE = 1001
    const val REQUEST_CODE_CAMERA = 1002
    const val REQUEST_CODE_MICROPHONE = 1003
    const val REQUEST_CODE_LOCATION = 1004

    /**
     * Check if a permission is granted
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return try {
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        } catch (e: Exception) {
            Log.e(TAG, "Error checking permission $permission: ${e.message}")
            false
        }
    }

    /**
     * Check if multiple permissions are granted
     */
    fun arePermissionsGranted(context: Context, permissions: Array<String>): Bo<PERSON>an {
        return try {
            permissions.all { isPermissionGranted(context, it) }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking multiple permissions: ${e.message}")
            false
        }
    }

    /**
     * Request a single permission
     */
    fun requestPermission(activity: Activity, permission: String, requestCode: Int) {
        try {
            ActivityCompat.requestPermissions(activity, arrayOf(permission), requestCode)
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting permission $permission: ${e.message}")
        }
    }

    /**
     * Request multiple permissions
     */
    fun requestPermissions(activity: Activity, permissions: Array<String>, requestCode: Int) {
        try {
            ActivityCompat.requestPermissions(activity, permissions, requestCode)
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting multiple permissions: ${e.message}")
        }
    }
}
