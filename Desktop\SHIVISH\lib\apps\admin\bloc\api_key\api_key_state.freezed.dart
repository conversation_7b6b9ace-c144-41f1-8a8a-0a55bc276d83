// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_key_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ApiKeyState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<ApiKey> apiKeys) loaded,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<ApiKey> apiKeys)? loaded,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<ApiKey> apiKeys)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ApiKeyInitial value) initial,
    required TResult Function(ApiKeyLoading value) loading,
    required TResult Function(ApiKeyLoaded value) loaded,
    required TResult Function(ApiKeyError value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ApiKeyInitial value)? initial,
    TResult? Function(ApiKeyLoading value)? loading,
    TResult? Function(ApiKeyLoaded value)? loaded,
    TResult? Function(ApiKeyError value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ApiKeyInitial value)? initial,
    TResult Function(ApiKeyLoading value)? loading,
    TResult Function(ApiKeyLoaded value)? loaded,
    TResult Function(ApiKeyError value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiKeyStateCopyWith<$Res> {
  factory $ApiKeyStateCopyWith(
          ApiKeyState value, $Res Function(ApiKeyState) then) =
      _$ApiKeyStateCopyWithImpl<$Res, ApiKeyState>;
}

/// @nodoc
class _$ApiKeyStateCopyWithImpl<$Res, $Val extends ApiKeyState>
    implements $ApiKeyStateCopyWith<$Res> {
  _$ApiKeyStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ApiKeyInitialImplCopyWith<$Res> {
  factory _$$ApiKeyInitialImplCopyWith(
          _$ApiKeyInitialImpl value, $Res Function(_$ApiKeyInitialImpl) then) =
      __$$ApiKeyInitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ApiKeyInitialImplCopyWithImpl<$Res>
    extends _$ApiKeyStateCopyWithImpl<$Res, _$ApiKeyInitialImpl>
    implements _$$ApiKeyInitialImplCopyWith<$Res> {
  __$$ApiKeyInitialImplCopyWithImpl(
      _$ApiKeyInitialImpl _value, $Res Function(_$ApiKeyInitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ApiKeyInitialImpl implements ApiKeyInitial {
  const _$ApiKeyInitialImpl();

  @override
  String toString() {
    return 'ApiKeyState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ApiKeyInitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<ApiKey> apiKeys) loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<ApiKey> apiKeys)? loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<ApiKey> apiKeys)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ApiKeyInitial value) initial,
    required TResult Function(ApiKeyLoading value) loading,
    required TResult Function(ApiKeyLoaded value) loaded,
    required TResult Function(ApiKeyError value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ApiKeyInitial value)? initial,
    TResult? Function(ApiKeyLoading value)? loading,
    TResult? Function(ApiKeyLoaded value)? loaded,
    TResult? Function(ApiKeyError value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ApiKeyInitial value)? initial,
    TResult Function(ApiKeyLoading value)? loading,
    TResult Function(ApiKeyLoaded value)? loaded,
    TResult Function(ApiKeyError value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class ApiKeyInitial implements ApiKeyState {
  const factory ApiKeyInitial() = _$ApiKeyInitialImpl;
}

/// @nodoc
abstract class _$$ApiKeyLoadingImplCopyWith<$Res> {
  factory _$$ApiKeyLoadingImplCopyWith(
          _$ApiKeyLoadingImpl value, $Res Function(_$ApiKeyLoadingImpl) then) =
      __$$ApiKeyLoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ApiKeyLoadingImplCopyWithImpl<$Res>
    extends _$ApiKeyStateCopyWithImpl<$Res, _$ApiKeyLoadingImpl>
    implements _$$ApiKeyLoadingImplCopyWith<$Res> {
  __$$ApiKeyLoadingImplCopyWithImpl(
      _$ApiKeyLoadingImpl _value, $Res Function(_$ApiKeyLoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ApiKeyLoadingImpl implements ApiKeyLoading {
  const _$ApiKeyLoadingImpl();

  @override
  String toString() {
    return 'ApiKeyState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ApiKeyLoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<ApiKey> apiKeys) loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<ApiKey> apiKeys)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<ApiKey> apiKeys)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ApiKeyInitial value) initial,
    required TResult Function(ApiKeyLoading value) loading,
    required TResult Function(ApiKeyLoaded value) loaded,
    required TResult Function(ApiKeyError value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ApiKeyInitial value)? initial,
    TResult? Function(ApiKeyLoading value)? loading,
    TResult? Function(ApiKeyLoaded value)? loaded,
    TResult? Function(ApiKeyError value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ApiKeyInitial value)? initial,
    TResult Function(ApiKeyLoading value)? loading,
    TResult Function(ApiKeyLoaded value)? loaded,
    TResult Function(ApiKeyError value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class ApiKeyLoading implements ApiKeyState {
  const factory ApiKeyLoading() = _$ApiKeyLoadingImpl;
}

/// @nodoc
abstract class _$$ApiKeyLoadedImplCopyWith<$Res> {
  factory _$$ApiKeyLoadedImplCopyWith(
          _$ApiKeyLoadedImpl value, $Res Function(_$ApiKeyLoadedImpl) then) =
      __$$ApiKeyLoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<ApiKey> apiKeys});
}

/// @nodoc
class __$$ApiKeyLoadedImplCopyWithImpl<$Res>
    extends _$ApiKeyStateCopyWithImpl<$Res, _$ApiKeyLoadedImpl>
    implements _$$ApiKeyLoadedImplCopyWith<$Res> {
  __$$ApiKeyLoadedImplCopyWithImpl(
      _$ApiKeyLoadedImpl _value, $Res Function(_$ApiKeyLoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiKeys = null,
  }) {
    return _then(_$ApiKeyLoadedImpl(
      null == apiKeys
          ? _value._apiKeys
          : apiKeys // ignore: cast_nullable_to_non_nullable
              as List<ApiKey>,
    ));
  }
}

/// @nodoc

class _$ApiKeyLoadedImpl implements ApiKeyLoaded {
  const _$ApiKeyLoadedImpl(final List<ApiKey> apiKeys) : _apiKeys = apiKeys;

  final List<ApiKey> _apiKeys;
  @override
  List<ApiKey> get apiKeys {
    if (_apiKeys is EqualUnmodifiableListView) return _apiKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_apiKeys);
  }

  @override
  String toString() {
    return 'ApiKeyState.loaded(apiKeys: $apiKeys)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiKeyLoadedImpl &&
            const DeepCollectionEquality().equals(other._apiKeys, _apiKeys));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_apiKeys));

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiKeyLoadedImplCopyWith<_$ApiKeyLoadedImpl> get copyWith =>
      __$$ApiKeyLoadedImplCopyWithImpl<_$ApiKeyLoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<ApiKey> apiKeys) loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(apiKeys);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<ApiKey> apiKeys)? loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(apiKeys);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<ApiKey> apiKeys)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(apiKeys);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ApiKeyInitial value) initial,
    required TResult Function(ApiKeyLoading value) loading,
    required TResult Function(ApiKeyLoaded value) loaded,
    required TResult Function(ApiKeyError value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ApiKeyInitial value)? initial,
    TResult? Function(ApiKeyLoading value)? loading,
    TResult? Function(ApiKeyLoaded value)? loaded,
    TResult? Function(ApiKeyError value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ApiKeyInitial value)? initial,
    TResult Function(ApiKeyLoading value)? loading,
    TResult Function(ApiKeyLoaded value)? loaded,
    TResult Function(ApiKeyError value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class ApiKeyLoaded implements ApiKeyState {
  const factory ApiKeyLoaded(final List<ApiKey> apiKeys) = _$ApiKeyLoadedImpl;

  List<ApiKey> get apiKeys;

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiKeyLoadedImplCopyWith<_$ApiKeyLoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApiKeyErrorImplCopyWith<$Res> {
  factory _$$ApiKeyErrorImplCopyWith(
          _$ApiKeyErrorImpl value, $Res Function(_$ApiKeyErrorImpl) then) =
      __$$ApiKeyErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ApiKeyErrorImplCopyWithImpl<$Res>
    extends _$ApiKeyStateCopyWithImpl<$Res, _$ApiKeyErrorImpl>
    implements _$$ApiKeyErrorImplCopyWith<$Res> {
  __$$ApiKeyErrorImplCopyWithImpl(
      _$ApiKeyErrorImpl _value, $Res Function(_$ApiKeyErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ApiKeyErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ApiKeyErrorImpl implements ApiKeyError {
  const _$ApiKeyErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'ApiKeyState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApiKeyErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApiKeyErrorImplCopyWith<_$ApiKeyErrorImpl> get copyWith =>
      __$$ApiKeyErrorImplCopyWithImpl<_$ApiKeyErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<ApiKey> apiKeys) loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<ApiKey> apiKeys)? loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<ApiKey> apiKeys)? loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ApiKeyInitial value) initial,
    required TResult Function(ApiKeyLoading value) loading,
    required TResult Function(ApiKeyLoaded value) loaded,
    required TResult Function(ApiKeyError value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ApiKeyInitial value)? initial,
    TResult? Function(ApiKeyLoading value)? loading,
    TResult? Function(ApiKeyLoaded value)? loaded,
    TResult? Function(ApiKeyError value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ApiKeyInitial value)? initial,
    TResult Function(ApiKeyLoading value)? loading,
    TResult Function(ApiKeyLoaded value)? loaded,
    TResult Function(ApiKeyError value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class ApiKeyError implements ApiKeyState {
  const factory ApiKeyError(final String message) = _$ApiKeyErrorImpl;

  String get message;

  /// Create a copy of ApiKeyState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApiKeyErrorImplCopyWith<_$ApiKeyErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
