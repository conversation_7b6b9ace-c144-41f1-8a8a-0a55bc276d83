import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_bloc.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_event.dart';
import 'package:shivish/apps/admin/bloc/user_management/user_management_state.dart';
import 'package:shivish/apps/admin/models/user.dart';
import 'package:shivish/apps/admin/widgets/user_dialogs.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/models/user/user_model.dart';

class UserManagementScreen extends StatelessWidget {
  const UserManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
      ),
      body: BlocBuilder<UserManagementBloc, UserManagementState>(
        builder: (context, state) {
          return state.when(
            initial: () => const SizedBox(),
            loading: () => const LoadingIndicator(),
            loaded: (users) => _UserList(users: users),
            error: (message) => Center(child: ErrorMessage(message: message)),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddUserDialog(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  Future<void> _showAddUserDialog(BuildContext context) async {
    final user = await showDialog<User>(
      context: context,
      builder: (context) => const AddUserDialog(),
    );

    if (user != null) {
      context.read<UserManagementBloc>().add(UserManagementEvent.addUser(user));
    }
  }

  Future<void> _showFilterDialog(BuildContext context) async {
    UserRole? selectedRole;
    UserStatus? selectedStatus;
    bool? isActive;

    await showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Filter Users'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<UserRole>(
                value: selectedRole,
                decoration: const InputDecoration(
                  labelText: 'Role',
                  border: OutlineInputBorder(),
                ),
                items: UserRole.values.map((role) {
                  return DropdownMenuItem(
                    value: role,
                    child: Text(role.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedRole = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<UserStatus>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Status',
                  border: OutlineInputBorder(),
                ),
                items: UserStatus.values.map((status) {
                  return DropdownMenuItem(
                    value: status,
                    child: Text(status.name),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedStatus = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<bool>(
                value: isActive,
                decoration: const InputDecoration(
                  labelText: 'Active Status',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: true,
                    child: Text('Active'),
                  ),
                  DropdownMenuItem(
                    value: false,
                    child: Text('Inactive'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    isActive = value;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<UserManagementBloc>().add(
                      UserManagementEvent.filterUsers(
                        role: selectedRole,
                        status: selectedStatus,
                        isActive: isActive,
                      ),
                    );
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }
}

class _UserList extends StatelessWidget {
  const _UserList({required this.users});

  final List<User> users;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundImage: user.profileImage != null
                  ? NetworkImage(user.profileImage!)
                  : null,
              child: user.profileImage == null
                  ? Text(user.name[0].toUpperCase())
                  : null,
            ),
            title: Text(user.name),
            subtitle: Text(user.email),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: () => _showEditUserDialog(context, user),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: () => _showDeleteUserDialog(context, user),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _showEditUserDialog(BuildContext context, User user) async {
    final updatedUser = await showDialog<User>(
      context: context,
      builder: (context) => EditUserDialog(user: user),
    );

    if (updatedUser != null) {
      context
          .read<UserManagementBloc>()
          .add(UserManagementEvent.updateUser(updatedUser));
    }
  }

  Future<void> _showDeleteUserDialog(BuildContext context, User user) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteUserDialog(user: user),
    );

    if (shouldDelete == true) {
      context
          .read<UserManagementBloc>()
          .add(UserManagementEvent.deleteUser(user.id));
    }
  }
}
