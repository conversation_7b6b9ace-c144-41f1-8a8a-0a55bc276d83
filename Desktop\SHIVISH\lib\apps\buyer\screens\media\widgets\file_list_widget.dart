import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../../shared/models/file/file_model.dart';

class FileListWidget extends HookConsumerWidget {
  final List<FileModel> files;
  final FileSort sortBy;
  final Function(FileModel) onTap;
  final Function(FileModel) onDelete;
  final Function(FileModel) onShare;
  final Function(FileModel) onDownload;

  const FileListWidget({
    super.key,
    required this.files,
    required this.sortBy,
    required this.onTap,
    required this.onDelete,
    required this.onShare,
    required this.onDownload,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortedFiles = useMemoized(() {
      final sorted = List<FileModel>.from(files);
      switch (sortBy) {
        case FileSort.nameAsc:
          sorted.sort((a, b) => a.name.compareTo(b.name));
          break;
        case FileSort.nameDesc:
          sorted.sort((a, b) => b.name.compareTo(a.name));
          break;
        case FileSort.dateAsc:
          sorted.sort((a, b) => a.createdAt.compareTo(b.createdAt));
          break;
        case FileSort.dateDesc:
          sorted.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          break;
        case FileSort.sizeAsc:
          sorted.sort((a, b) => a.size.compareTo(b.size));
          break;
        case FileSort.sizeDesc:
          sorted.sort((a, b) => b.size.compareTo(a.size));
          break;
      }
      return sorted;
    }, [files, sortBy]);

    return ListView.builder(
      itemCount: sortedFiles.length,
      itemBuilder: (context, index) {
        final file = sortedFiles[index];
        return ListTile(
          leading: _buildFileIcon(file),
          title: Text(file.name),
          subtitle: Text(
            '${_formatFileSize(file.size)} • ${_formatDate(file.createdAt)}',
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.download),
                onPressed: () => onDownload(file),
              ),
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () => onShare(file),
              ),
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => onDelete(file),
              ),
            ],
          ),
          onTap: () => onTap(file),
        );
      },
    );
  }

  Widget _buildFileIcon(FileModel file) {
    if (file.mimeType.startsWith('image/')) {
      return const Icon(Icons.image);
    } else if (file.mimeType.startsWith('video/')) {
      return const Icon(Icons.video_file);
    } else if (file.mimeType.startsWith('audio/')) {
      return const Icon(Icons.audio_file);
    } else if (file.mimeType == 'application/pdf') {
      return const Icon(Icons.picture_as_pdf);
    } else {
      return const Icon(Icons.insert_drive_file);
    }
  }

  String _formatFileSize(int size) {
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)} KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('MMM d, y').format(date);
  }
}
