// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'list_submission_order.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ListSubmissionOrder _$ListSubmissionOrderFromJson(Map<String, dynamic> json) {
  return _ListSubmissionOrder.fromJson(json);
}

/// @nodoc
mixin _$ListSubmissionOrder {
  String get id => throw _privateConstructorUsedError;
  String get submissionId => throw _privateConstructorUsedError;
  String get buyerId => throw _privateConstructorUsedError;
  String get sellerId => throw _privateConstructorUsedError;
  double get totalAmount => throw _privateConstructorUsedError;
  List<ListSubmissionOrderItem> get items => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  bool get isPaid => throw _privateConstructorUsedError;
  String? get paymentId => throw _privateConstructorUsedError;
  String? get deliveryId => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  /// Serializes this ListSubmissionOrder to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ListSubmissionOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ListSubmissionOrderCopyWith<ListSubmissionOrder> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListSubmissionOrderCopyWith<$Res> {
  factory $ListSubmissionOrderCopyWith(
          ListSubmissionOrder value, $Res Function(ListSubmissionOrder) then) =
      _$ListSubmissionOrderCopyWithImpl<$Res, ListSubmissionOrder>;
  @useResult
  $Res call(
      {String id,
      String submissionId,
      String buyerId,
      String sellerId,
      double totalAmount,
      List<ListSubmissionOrderItem> items,
      DateTime createdAt,
      bool isPaid,
      String? paymentId,
      String? deliveryId,
      String status});
}

/// @nodoc
class _$ListSubmissionOrderCopyWithImpl<$Res, $Val extends ListSubmissionOrder>
    implements $ListSubmissionOrderCopyWith<$Res> {
  _$ListSubmissionOrderCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ListSubmissionOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? submissionId = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? totalAmount = null,
    Object? items = null,
    Object? createdAt = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? deliveryId = freezed,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      submissionId: null == submissionId
          ? _value.submissionId
          : submissionId // ignore: cast_nullable_to_non_nullable
              as String,
      buyerId: null == buyerId
          ? _value.buyerId
          : buyerId // ignore: cast_nullable_to_non_nullable
              as String,
      sellerId: null == sellerId
          ? _value.sellerId
          : sellerId // ignore: cast_nullable_to_non_nullable
              as String,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ListSubmissionOrderItem>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isPaid: null == isPaid
          ? _value.isPaid
          : isPaid // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentId: freezed == paymentId
          ? _value.paymentId
          : paymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListSubmissionOrderImplCopyWith<$Res>
    implements $ListSubmissionOrderCopyWith<$Res> {
  factory _$$ListSubmissionOrderImplCopyWith(_$ListSubmissionOrderImpl value,
          $Res Function(_$ListSubmissionOrderImpl) then) =
      __$$ListSubmissionOrderImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String submissionId,
      String buyerId,
      String sellerId,
      double totalAmount,
      List<ListSubmissionOrderItem> items,
      DateTime createdAt,
      bool isPaid,
      String? paymentId,
      String? deliveryId,
      String status});
}

/// @nodoc
class __$$ListSubmissionOrderImplCopyWithImpl<$Res>
    extends _$ListSubmissionOrderCopyWithImpl<$Res, _$ListSubmissionOrderImpl>
    implements _$$ListSubmissionOrderImplCopyWith<$Res> {
  __$$ListSubmissionOrderImplCopyWithImpl(_$ListSubmissionOrderImpl _value,
      $Res Function(_$ListSubmissionOrderImpl) _then)
      : super(_value, _then);

  /// Create a copy of ListSubmissionOrder
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? submissionId = null,
    Object? buyerId = null,
    Object? sellerId = null,
    Object? totalAmount = null,
    Object? items = null,
    Object? createdAt = null,
    Object? isPaid = null,
    Object? paymentId = freezed,
    Object? deliveryId = freezed,
    Object? status = null,
  }) {
    return _then(_$ListSubmissionOrderImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      submissionId: null == submissionId
          ? _value.submissionId
          : submissionId // ignore: cast_nullable_to_non_nullable
              as String,
      buyerId: null == buyerId
          ? _value.buyerId
          : buyerId // ignore: cast_nullable_to_non_nullable
              as String,
      sellerId: null == sellerId
          ? _value.sellerId
          : sellerId // ignore: cast_nullable_to_non_nullable
              as String,
      totalAmount: null == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double,
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ListSubmissionOrderItem>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isPaid: null == isPaid
          ? _value.isPaid
          : isPaid // ignore: cast_nullable_to_non_nullable
              as bool,
      paymentId: freezed == paymentId
          ? _value.paymentId
          : paymentId // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryId: freezed == deliveryId
          ? _value.deliveryId
          : deliveryId // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionOrderImpl
    with DiagnosticableTreeMixin
    implements _ListSubmissionOrder {
  const _$ListSubmissionOrderImpl(
      {required this.id,
      required this.submissionId,
      required this.buyerId,
      required this.sellerId,
      required this.totalAmount,
      required final List<ListSubmissionOrderItem> items,
      required this.createdAt,
      this.isPaid = false,
      this.paymentId,
      this.deliveryId,
      this.status = 'pending'})
      : _items = items;

  factory _$ListSubmissionOrderImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListSubmissionOrderImplFromJson(json);

  @override
  final String id;
  @override
  final String submissionId;
  @override
  final String buyerId;
  @override
  final String sellerId;
  @override
  final double totalAmount;
  final List<ListSubmissionOrderItem> _items;
  @override
  List<ListSubmissionOrderItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final bool isPaid;
  @override
  final String? paymentId;
  @override
  final String? deliveryId;
  @override
  @JsonKey()
  final String status;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ListSubmissionOrder(id: $id, submissionId: $submissionId, buyerId: $buyerId, sellerId: $sellerId, totalAmount: $totalAmount, items: $items, createdAt: $createdAt, isPaid: $isPaid, paymentId: $paymentId, deliveryId: $deliveryId, status: $status)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ListSubmissionOrder'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('submissionId', submissionId))
      ..add(DiagnosticsProperty('buyerId', buyerId))
      ..add(DiagnosticsProperty('sellerId', sellerId))
      ..add(DiagnosticsProperty('totalAmount', totalAmount))
      ..add(DiagnosticsProperty('items', items))
      ..add(DiagnosticsProperty('createdAt', createdAt))
      ..add(DiagnosticsProperty('isPaid', isPaid))
      ..add(DiagnosticsProperty('paymentId', paymentId))
      ..add(DiagnosticsProperty('deliveryId', deliveryId))
      ..add(DiagnosticsProperty('status', status));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionOrderImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.submissionId, submissionId) ||
                other.submissionId == submissionId) &&
            (identical(other.buyerId, buyerId) || other.buyerId == buyerId) &&
            (identical(other.sellerId, sellerId) ||
                other.sellerId == sellerId) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.isPaid, isPaid) || other.isPaid == isPaid) &&
            (identical(other.paymentId, paymentId) ||
                other.paymentId == paymentId) &&
            (identical(other.deliveryId, deliveryId) ||
                other.deliveryId == deliveryId) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      submissionId,
      buyerId,
      sellerId,
      totalAmount,
      const DeepCollectionEquality().hash(_items),
      createdAt,
      isPaid,
      paymentId,
      deliveryId,
      status);

  /// Create a copy of ListSubmissionOrder
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionOrderImplCopyWith<_$ListSubmissionOrderImpl> get copyWith =>
      __$$ListSubmissionOrderImplCopyWithImpl<_$ListSubmissionOrderImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionOrderImplToJson(
      this,
    );
  }
}

abstract class _ListSubmissionOrder implements ListSubmissionOrder {
  const factory _ListSubmissionOrder(
      {required final String id,
      required final String submissionId,
      required final String buyerId,
      required final String sellerId,
      required final double totalAmount,
      required final List<ListSubmissionOrderItem> items,
      required final DateTime createdAt,
      final bool isPaid,
      final String? paymentId,
      final String? deliveryId,
      final String status}) = _$ListSubmissionOrderImpl;

  factory _ListSubmissionOrder.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionOrderImpl.fromJson;

  @override
  String get id;
  @override
  String get submissionId;
  @override
  String get buyerId;
  @override
  String get sellerId;
  @override
  double get totalAmount;
  @override
  List<ListSubmissionOrderItem> get items;
  @override
  DateTime get createdAt;
  @override
  bool get isPaid;
  @override
  String? get paymentId;
  @override
  String? get deliveryId;
  @override
  String get status;

  /// Create a copy of ListSubmissionOrder
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionOrderImplCopyWith<_$ListSubmissionOrderImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ListSubmissionOrderItem _$ListSubmissionOrderItemFromJson(
    Map<String, dynamic> json) {
  return _ListSubmissionOrderItem.fromJson(json);
}

/// @nodoc
mixin _$ListSubmissionOrderItem {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  int get quantity => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  String? get notes => throw _privateConstructorUsedError;

  /// Serializes this ListSubmissionOrderItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ListSubmissionOrderItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ListSubmissionOrderItemCopyWith<ListSubmissionOrderItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ListSubmissionOrderItemCopyWith<$Res> {
  factory $ListSubmissionOrderItemCopyWith(ListSubmissionOrderItem value,
          $Res Function(ListSubmissionOrderItem) then) =
      _$ListSubmissionOrderItemCopyWithImpl<$Res, ListSubmissionOrderItem>;
  @useResult
  $Res call(
      {String id, String name, int quantity, double price, String? notes});
}

/// @nodoc
class _$ListSubmissionOrderItemCopyWithImpl<$Res,
        $Val extends ListSubmissionOrderItem>
    implements $ListSubmissionOrderItemCopyWith<$Res> {
  _$ListSubmissionOrderItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ListSubmissionOrderItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? quantity = null,
    Object? price = null,
    Object? notes = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ListSubmissionOrderItemImplCopyWith<$Res>
    implements $ListSubmissionOrderItemCopyWith<$Res> {
  factory _$$ListSubmissionOrderItemImplCopyWith(
          _$ListSubmissionOrderItemImpl value,
          $Res Function(_$ListSubmissionOrderItemImpl) then) =
      __$$ListSubmissionOrderItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String name, int quantity, double price, String? notes});
}

/// @nodoc
class __$$ListSubmissionOrderItemImplCopyWithImpl<$Res>
    extends _$ListSubmissionOrderItemCopyWithImpl<$Res,
        _$ListSubmissionOrderItemImpl>
    implements _$$ListSubmissionOrderItemImplCopyWith<$Res> {
  __$$ListSubmissionOrderItemImplCopyWithImpl(
      _$ListSubmissionOrderItemImpl _value,
      $Res Function(_$ListSubmissionOrderItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of ListSubmissionOrderItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? quantity = null,
    Object? price = null,
    Object? notes = freezed,
  }) {
    return _then(_$ListSubmissionOrderItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      notes: freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ListSubmissionOrderItemImpl
    with DiagnosticableTreeMixin
    implements _ListSubmissionOrderItem {
  const _$ListSubmissionOrderItemImpl(
      {required this.id,
      required this.name,
      required this.quantity,
      required this.price,
      this.notes});

  factory _$ListSubmissionOrderItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$ListSubmissionOrderItemImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final int quantity;
  @override
  final double price;
  @override
  final String? notes;

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ListSubmissionOrderItem(id: $id, name: $name, quantity: $quantity, price: $price, notes: $notes)';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'ListSubmissionOrderItem'))
      ..add(DiagnosticsProperty('id', id))
      ..add(DiagnosticsProperty('name', name))
      ..add(DiagnosticsProperty('quantity', quantity))
      ..add(DiagnosticsProperty('price', price))
      ..add(DiagnosticsProperty('notes', notes));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ListSubmissionOrderItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.notes, notes) || other.notes == notes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, quantity, price, notes);

  /// Create a copy of ListSubmissionOrderItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ListSubmissionOrderItemImplCopyWith<_$ListSubmissionOrderItemImpl>
      get copyWith => __$$ListSubmissionOrderItemImplCopyWithImpl<
          _$ListSubmissionOrderItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ListSubmissionOrderItemImplToJson(
      this,
    );
  }
}

abstract class _ListSubmissionOrderItem implements ListSubmissionOrderItem {
  const factory _ListSubmissionOrderItem(
      {required final String id,
      required final String name,
      required final int quantity,
      required final double price,
      final String? notes}) = _$ListSubmissionOrderItemImpl;

  factory _ListSubmissionOrderItem.fromJson(Map<String, dynamic> json) =
      _$ListSubmissionOrderItemImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  int get quantity;
  @override
  double get price;
  @override
  String? get notes;

  /// Create a copy of ListSubmissionOrderItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ListSubmissionOrderItemImplCopyWith<_$ListSubmissionOrderItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}
