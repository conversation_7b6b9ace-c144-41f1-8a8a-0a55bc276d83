package com.shivish.utils

import android.speech.tts.UtteranceProgressListener
import android.util.Log

/**
 * Custom implementation of UtteranceProgressListener that implements only the required abstract methods
 */
class CustomUtteranceProgressListener(
    private val onComplete: ((Boolean) -> Unit)? = null
) : UtteranceProgressListener() {

    private val TAG = "CustomUtteranceProgressListener"

    override fun onStart(utteranceId: String?) {
        Log.d(TAG, "TTS started: $utteranceId")
    }

    override fun onDone(utteranceId: String?) {
        Log.d(TAG, "TTS done: $utteranceId")
        onComplete?.invoke(true)
    }

    override fun onError(utteranceId: String?) {
        Log.e(TAG, "TTS error: $utteranceId")
        onComplete?.invoke(false)
    }

    override fun onError(utteranceId: String?, errorCode: Int) {
        Log.e(TAG, "TTS error: $utteranceId, code: $errorCode")
        onComplete?.invoke(false)
    }

    override fun onStop(utteranceId: String?, interrupted: Boolean) {
        Log.d(TAG, "TTS stopped: $utteranceId, interrupted: $interrupted")
        onComplete?.invoke(false)
    }

    override fun onBeginSynthesis(utteranceId: String?, sampleRateInHz: Int, audioFormat: Int, channelCount: Int) {
        Log.d(TAG, "TTS synthesis began: $utteranceId")
    }

    override fun onRangeStart(utteranceId: String?, start: Int, end: Int, frame: Int) {
        Log.d(TAG, "TTS range start: $utteranceId, $start-$end")
    }

    // The following methods are not abstract in the parent class, so we don't need to override them
    // If needed, we can implement them with the correct signatures

    // For API level 26+
    // override fun onRangeStart(utteranceId: String?, start: Int, end: Int, frame: Int) {}

    // For API level 24+
    // override fun onAudioAvailable(utteranceId: String?, audio: ByteArray) {}
    // override fun onBeginSynthesis(utteranceId: String?, sampleRateInHz: Int, audioFormat: Int, channelCount: Int) {}
}