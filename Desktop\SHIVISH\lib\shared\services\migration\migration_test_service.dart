import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import '../auth/supabase_auth_service.dart';
import '../storage/aws_s3_storage_provider.dart';
import '../../core/auth/models/login_request.dart';
import '../../core/auth/models/register_request.dart';
import '../../models/user/user_model.dart';
import '../../utils/logger.dart';
import '../../config/environment_config.dart';
import '../supabase_initialization_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';


/// Service to test the migration setup
class MigrationTestService {
  static final Logger logger = getLogger('MigrationTestService');
  
  /// Run comprehensive migration tests
  static Future<Map<String, bool>> runAllTests() async {
    final results = <String, bool>{};
    
    try {
      logger.info('Starting migration tests...');
      
      // Test 1: Environment Configuration
      results['environment_config'] = await _testEnvironmentConfig();
      
      // Test 2: Supabase Initialization
      results['supabase_initialization'] = await _testSupabaseInitialization();
      
      // Test 3: Supabase Authentication
      results['supabase_auth'] = await _testSupabaseAuth();
      
      // Test 4: AWS S3 Storage
      results['aws_s3_storage'] = await _testAWSS3Storage();
      
      // Test 5: Database Operations
      results['database_operations'] = await _testDatabaseOperations();
      
      final passedTests = results.values.where((passed) => passed).length;
      final totalTests = results.length;
      
      logger.info('Migration tests completed: $passedTests/$totalTests passed');
      
      if (passedTests == totalTests) {
        logger.info('🎉 All tests passed! Migration setup is ready.');
      } else {
        logger.warning('⚠️ Some tests failed. Check the logs for details.');
      }
      
      return results;
    } catch (e) {
      logger.error('Migration tests failed: $e');
      return {'error': false};
    }
  }
  
  /// Test environment configuration
  static Future<bool> _testEnvironmentConfig() async {
    try {
      logger.info('Testing environment configuration...');
      
      await EnvironmentConfig.initialize();
      
      // Check Supabase config
      if (!EnvironmentConfig.isSupabaseConfigured) {
        logger.error('Supabase configuration is incomplete');
        return false;
      }
      
      // Check AWS config
      if (!EnvironmentConfig.isAWSConfigured) {
        logger.error('AWS configuration is incomplete');
        return false;
      }
      
      logger.info('✅ Environment configuration test passed');
      return true;
    } catch (e) {
      logger.error('❌ Environment configuration test failed: $e');
      return false;
    }
  }
  
  /// Test Supabase initialization
  static Future<bool> _testSupabaseInitialization() async {
    try {
      logger.info('Testing Supabase initialization...');
      
      await SupabaseInitializationService.initialize();
      
      if (!SupabaseInitializationService.isInitialized) {
        logger.error('Supabase initialization failed');
        return false;
      }
      
      // Test connection
      final connectionTest = await SupabaseInitializationService.testConnection();
      if (!connectionTest) {
        logger.error('Supabase connection test failed');
        return false;
      }
      
      logger.info('✅ Supabase initialization test passed');
      return true;
    } catch (e) {
      logger.error('❌ Supabase initialization test failed: $e');
      return false;
    }
  }
  
  /// Test Supabase authentication
  static Future<bool> _testSupabaseAuth() async {
    try {
      logger.info('Testing Supabase authentication...');
      
      final authService = SupabaseSupabaseAuthService.instance;
      final testEmail = 'test_${DateTime.now().millisecondsSinceEpoch}@example.com';
      const testPassword = 'TestPassword123!';
      
      // Test registration
      try {
        final user = await authService.register(RegisterRequest(
          email: testEmail,
          password: testPassword,
          displayName: 'Test User',
          role: UserRole.buyer,
        ));
        
        if (user.id.isEmpty) {
          logger.error('Registration returned incorrect user');
          return false;
        }
        
        logger.info('Registration test passed');
      } catch (e) {
        logger.error('Registration test failed: $e');
        return false;
      }
      
      // Test sign out
      try {
        await authService.signOut();
        logger.info('Sign out test passed');
      } catch (e) {
        logger.error('Sign out test failed: $e');
        return false;
      }
      
      // Test sign in
      try {
        final user = await authService.signInWithPassword(
          LoginRequest(email: testEmail, password: testPassword),
        );
        
        if (user.email != testEmail) {
          logger.error('Sign in returned incorrect user');
          return false;
        }
        
        logger.info('Sign in test passed');
      } catch (e) {
        logger.error('Sign in test failed: $e');
        return false;
      }
      
      // Clean up - sign out
      await authService.signOut();
      
      logger.info('✅ Supabase authentication test passed');
      return true;
    } catch (e) {
      logger.error('❌ Supabase authentication test failed: $e');
      return false;
    }
  }
  
  /// Test AWS S3 storage
  static Future<bool> _testAWSS3Storage() async {
    try {
      logger.info('Testing AWS S3 storage...');
      
      final storageProvider = AWSS3StorageProvider();
      
      // Create test data
      final testData = Uint8List.fromList('Hello, AWS S3! This is a test file.'.codeUnits);
      final testFileName = 'test_${DateTime.now().millisecondsSinceEpoch}.txt';
      const testStoragePath = 'test';
      
      // Test upload
      String uploadUrl;
      try {
        uploadUrl = await storageProvider.uploadData(
          data: testData,
          fileName: testFileName,
          storagePath: testStoragePath,
        );
        
        if (uploadUrl.isEmpty) {
          logger.error('Upload returned empty URL');
          return false;
        }
        
        logger.info('Upload test passed: $uploadUrl');
      } catch (e) {
        logger.error('Upload test failed: $e');
        return false;
      }
      
      // Test download
      try {
        final downloadedData = await storageProvider.downloadFile(uploadUrl);
        
        if (downloadedData.length != testData.length) {
          logger.error('Downloaded data size mismatch');
          return false;
        }
        
        final downloadedString = String.fromCharCodes(downloadedData);
        final originalString = String.fromCharCodes(testData);
        
        if (downloadedString != originalString) {
          logger.error('Downloaded data content mismatch');
          return false;
        }
        
        logger.info('Download test passed');
      } catch (e) {
        logger.error('Download test failed: $e');
        return false;
      }
      
      // Test file existence
      try {
        final exists = await storageProvider.fileExists('$testStoragePath/$testFileName');
        
        if (!exists) {
          logger.error('File existence check failed');
          return false;
        }
        
        logger.info('File existence test passed');
      } catch (e) {
        logger.error('File existence test failed: $e');
        return false;
      }
      
      // Test delete
      try {
        await storageProvider.deleteFile('$testStoragePath/$testFileName');
        logger.info('Delete test passed');
      } catch (e) {
        logger.error('Delete test failed: $e');
        return false;
      }
      
      logger.info('✅ AWS S3 storage test passed');
      return true;
    } catch (e) {
      logger.error('❌ AWS S3 storage test failed: $e');
      return false;
    }
  }
  
  /// Test database operations
  static Future<bool> _testDatabaseOperations() async {
    try {
      logger.info('Testing database operations...');
      
      final client = SupabaseInitializationService.client;
      
      // Test app config read
      try {
        final response = await client.from('app_config').select('id').limit(1);
        logger.info('App config read test passed');
      } catch (e) {
        logger.error('App config read test failed: $e');
        return false;
      }
      
      // Test user profiles read
      try {
        final response = await client.from('user_profiles').select('id').limit(1);
        logger.info('User profiles read test passed');
      } catch (e) {
        logger.error('User profiles read test failed: $e');
        return false;
      }
      
      logger.info('✅ Database operations test passed');
      return true;
    } catch (e) {
      logger.error('❌ Database operations test failed: $e');
      return false;
    }
  }
  
  /// Generate test report
  static String generateTestReport(Map<String, bool> results) {
    final buffer = StringBuffer();
    buffer.writeln('🧪 Migration Test Report');
    buffer.writeln('=' * 50);
    buffer.writeln();
    
    for (final entry in results.entries) {
      final status = entry.value ? '✅ PASS' : '❌ FAIL';
      buffer.writeln('${entry.key.toUpperCase().replaceAll('_', ' ')}: $status');
    }
    
    buffer.writeln();
    final passedCount = results.values.where((passed) => passed).length;
    final totalCount = results.length;
    
    if (passedCount == totalCount) {
      buffer.writeln('🎉 All tests passed! Your migration setup is ready.');
      buffer.writeln();
      buffer.writeln('Next steps:');
      buffer.writeln('1. Run data migration from Firebase to Supabase');
      buffer.writeln('2. Run storage migration from Firebase Storage to AWS S3');
      buffer.writeln('3. Update your app to use the new services');
      buffer.writeln('4. Test thoroughly in a staging environment');
      buffer.writeln('5. Deploy to production');
    } else {
      buffer.writeln('⚠️ Some tests failed. Please fix the issues before proceeding.');
      buffer.writeln();
      buffer.writeln('Common solutions:');
      buffer.writeln('- Check your .env file configuration');
      buffer.writeln('- Verify Supabase project settings');
      buffer.writeln('- Confirm AWS S3 bucket permissions');
      buffer.writeln('- Ensure network connectivity');
    }
    
    return buffer.toString();
  }
}
