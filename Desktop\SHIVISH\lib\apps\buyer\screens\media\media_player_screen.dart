import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../providers/media_provider.dart';

class MediaPlayerScreen extends ConsumerStatefulWidget {
  final String mediaId;

  const MediaPlayerScreen({
    super.key,
    required this.mediaId,
  });

  @override
  ConsumerState<MediaPlayerScreen> createState() => _MediaPlayerScreenState();
}

class _MediaPlayerScreenState extends ConsumerState<MediaPlayerScreen> {
  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      ref.read(mediaProvider.notifier).loadMedia(widget.mediaId);
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaState = ref.watch(mediaProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Now Playing'),
        actions: [
          IconButton(
            icon: Icon(
              mediaState.isFavorite ? Icons.favorite : Icons.favorite_border,
            ),
            onPressed: () {
              ref.read(mediaProvider.notifier).toggleFavorite();
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              ref.read(mediaProvider.notifier).showOptionsMenu();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (mediaState.type == 'Video' &&
                      mediaState.thumbnail != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Image.network(
                        mediaState.thumbnail!,
                        width: 300,
                        height: 300,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 300,
                            height: 300,
                            color: theme.colorScheme.surfaceContainerHighest,
                            child: Icon(
                              Icons.video_library,
                              size: 64,
                              color: theme.colorScheme.primary,
                            ),
                          );
                        },
                      ),
                    )
                  else
                    Container(
                      width: 300,
                      height: 300,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        mediaState.type == 'Audio'
                            ? Icons.audio_file
                            : Icons.video_library,
                        size: 64,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  const SizedBox(height: 32),
                  Text(
                    mediaState.title,
                    style: theme.textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    mediaState.artist,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              children: [
                Slider(
                  value: mediaState.progress,
                  onChanged: (value) {
                    ref.read(mediaProvider.notifier).seekTo(value);
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '4:30',
                      style: theme.textTheme.bodySmall,
                    ),
                    Text(
                      mediaState.duration,
                      style: theme.textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.shuffle,
                        color: mediaState.isShuffleEnabled
                            ? theme.colorScheme.primary
                            : null,
                      ),
                      onPressed: () {
                        ref.read(mediaProvider.notifier).toggleShuffle();
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.skip_previous),
                      onPressed: () {
                        ref.read(mediaProvider.notifier).playPrevious();
                      },
                    ),
                    FloatingActionButton(
                      onPressed: () {
                        ref.read(mediaProvider.notifier).togglePlay();
                      },
                      child: Icon(
                        mediaState.isPlaying ? Icons.pause : Icons.play_arrow,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.skip_next),
                      onPressed: () {
                        ref.read(mediaProvider.notifier).playNext();
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.repeat,
                        color: mediaState.isRepeatEnabled
                            ? theme.colorScheme.primary
                            : null,
                      ),
                      onPressed: () {
                        ref.read(mediaProvider.notifier).toggleRepeat();
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Icon(
                      Icons.volume_up,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    Expanded(
                      child: Slider(
                        value: mediaState.volume,
                        onChanged: (value) {
                          ref.read(mediaProvider.notifier).setVolume(value);
                        },
                      ),
                    ),
                    Icon(
                      Icons.volume_down,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
