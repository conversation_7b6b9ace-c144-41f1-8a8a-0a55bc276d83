import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/home/<USER>';
import 'package:shivish/apps/admin/bloc/home/<USER>';
import 'package:shivish/apps/admin/admin_routes.dart';
import 'package:shivish/shared/core/navigation/widgets/admin_drawer.dart';
import 'package:shivish/shared/services/notification_service.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

@injectable
class HomeScreen extends StatelessWidget {
  final NotificationService? _notificationService;

  const HomeScreen([this._notificationService, Key? key]) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HomeCubit(_notificationService)..loadDashboardData(),
      child: Scaffold(
          drawer: const AdminDrawer(),
          appBar: AppBar(
            title: const Text('Admin Dashboard'),
            leading: Builder(
              builder: (context) => IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () {
                  Scaffold.of(context).openDrawer();
                },
              ),
            ),
            actions: [
              Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    onPressed: () {
                      context.push(AdminRoutes.notifications);
                      context.read<HomeCubit>().markNotificationsAsRead();
                    },
                  ),
                  BlocBuilder<HomeCubit, HomeState>(
                    builder: (context, state) {
                      if (state.unreadNotificationsCount > 0) {
                        return Positioned(
                          right: 8,
                          top: 8,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: Text(
                              state.unreadNotificationsCount.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
              IconButton(
                icon: const Icon(Icons.person_outline),
                onPressed: () {
                  context.push(AdminRoutes.profile);
                },
              ),
            ],
          ),
          body: SafeArea(
            child: BlocBuilder<HomeCubit, HomeState>(
              builder: (context, state) {
                if (state.isLoading) {
                  return const LoadingIndicator();
                }

                if (state.error != null) {
                  return ErrorMessage(message: state.error!);
                }

                return GridView.count(
                  padding: const EdgeInsets.all(10),
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 10,
                  children: [
                    _DashboardCard(
                      icon: Icons.approval_outlined,
                      title: 'Executor Requests',
                      subtitle: 'Approve new executors',
                      onTap: () => context.push(AdminRoutes.executorRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.store_mall_directory_outlined,
                      title: 'Seller Requests',
                      subtitle: 'Approve new sellers',
                      onTap: () => context.push(AdminRoutes.sellerRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.temple_hindu_outlined,
                      title: 'Priest Requests',
                      subtitle: 'Approve new priests',
                      onTap: () => context.push(AdminRoutes.priestRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.home_repair_service_outlined,
                      title: 'Technician Requests',
                      subtitle: 'Approve new technicians',
                      onTap: () => context.push(AdminRoutes.technicianRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.delivery_dining_outlined,
                      title: 'Delivery Partner Requests',
                      subtitle: 'Approve new delivery partners',
                      onTap: () => context.push(AdminRoutes.deliveryPartnerRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.edit_outlined,
                      title: 'Saviour Profile Changes',
                      subtitle: 'Approve profile change requests',
                      onTap: () => context.push(AdminRoutes.saviourProfileChangeRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.local_hospital_outlined,
                      title: 'Hospital Requests',
                      subtitle: 'Approve new hospitals',
                      onTap: () => context.push(AdminRoutes.hospitalRequests),
                    ),
                    _DashboardCard(
                      icon: Icons.people_outline,
                      title: 'Users',
                      subtitle: 'Manage all users',
                      onTap: () => context.push(AdminRoutes.users),
                    ),
                    _DashboardCard(
                      icon: Icons.admin_panel_settings_outlined,
                      title: 'Executors',
                      subtitle: 'Manage executors',
                      onTap: () => context.push(AdminRoutes.executors),
                    ),
                    _DashboardCard(
                      icon: Icons.store_outlined,
                      title: 'Sellers',
                      subtitle: 'Manage sellers',
                      onTap: () => context.push(AdminRoutes.sellers),
                    ),
                    _DashboardCard(
                      icon: Icons.temple_hindu_outlined,
                      title: 'Priests',
                      subtitle: 'Manage priests',
                      onTap: () => context.push(AdminRoutes.priests),
                    ),
                    _DashboardCard(
                      icon: Icons.handyman_outlined,
                      title: 'Technicians',
                      subtitle: 'Manage technicians',
                      onTap: () => context.push(AdminRoutes.technicians),
                    ),
                    _DashboardCard(
                      icon: Icons.inventory_2_outlined,
                      title: 'Products',
                      subtitle: 'Manage products',
                      onTap: () => context.push(AdminRoutes.products),
                    ),
                    _DashboardCard(
                      icon: Icons.analytics_outlined,
                      title: 'Analytics',
                      subtitle: 'View analytics and reports',
                      onTap: () => context.push(AdminRoutes.analytics),
                    ),
                    _DashboardCard(
                      icon: Icons.settings_outlined,
                      title: 'Settings',
                      subtitle: 'Configure system settings',
                      onTap: () => context.push(AdminRoutes.settings),
                    ),
                    _DashboardCard(
                      icon: Icons.event_outlined,
                      title: 'Events',
                      subtitle: 'Manage public events',
                      onTap: () => context.push(AdminRoutes.events),
                    ),
                    _DashboardCard(
                      icon: Icons.perm_media_outlined,
                      title: 'Media',
                      subtitle: 'Manage media content',
                      onTap: () => context.push(AdminRoutes.media),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
    );
  }
}

class _DashboardCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;

  const _DashboardCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
