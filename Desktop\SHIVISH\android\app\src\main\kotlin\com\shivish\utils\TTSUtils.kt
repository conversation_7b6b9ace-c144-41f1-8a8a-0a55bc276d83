package com.shivish.utils

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import java.util.*

/**
 * Utility class for Text-to-Speech operations
 */
object TTSUtils {
    private const val TAG = "TTSUtils"
    
    /**
     * Check if TTS is available on the device
     */
    fun isTTSAvailable(context: Context): Boolean {
        return try {
            val tts = TextToSpeech(context, null)
            val result = tts.engines.isNotEmpty()
            tts.shutdown()
            result
        } catch (e: Exception) {
            Log.e(TAG, "Error checking TTS availability: ${e.message}")
            false
        }
    }

    /**
     * Get available TTS engines
     */
    fun getAvailableEngines(context: Context): List<TextToSpeech.EngineInfo> {
        return try {
            val tts = TextToSpeech(context, null)
            val engines = tts.engines
            tts.shutdown()
            engines
        } catch (e: Exception) {
            Log.e(TAG, "Error getting TTS engines: ${e.message}")
            emptyList()
        }
    }

    /**
     * Check if a specific language is supported
     */
    fun isLanguageSupported(tts: TextToSpeech?, locale: Locale): Boolean {
        return try {
            if (tts == null) return false
            val result = tts.isLanguageAvailable(locale)
            result == TextToSpeech.LANG_AVAILABLE || 
            result == TextToSpeech.LANG_COUNTRY_AVAILABLE ||
            result == TextToSpeech.LANG_COUNTRY_VAR_AVAILABLE
        } catch (e: Exception) {
            Log.e(TAG, "Error checking language support: ${e.message}")
            false
        }
    }

    /**
     * Get supported languages
     */
    fun getSupportedLanguages(tts: TextToSpeech?): Set<Locale> {
        return try {
            if (tts == null) return emptySet()
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                tts.availableLanguages ?: emptySet()
            } else {
                // For older versions, check common languages
                val commonLanguages = listOf(
                    Locale.US, Locale.UK, Locale.CANADA,
                    Locale.GERMAN, Locale.FRENCH, Locale.ITALIAN,
                    Locale.JAPANESE, Locale.KOREAN, Locale.CHINESE
                )
                commonLanguages.filter { isLanguageSupported(tts, it) }.toSet()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting supported languages: ${e.message}")
            emptySet()
        }
    }

    /**
     * Create a safe utterance progress listener
     */
    fun createUtteranceProgressListener(
        onStart: ((String) -> Unit)? = null,
        onDone: ((String) -> Unit)? = null,
        onError: ((String, Int) -> Unit)? = null
    ): UtteranceProgressListener {
        return object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                try {
                    utteranceId?.let { onStart?.invoke(it) }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in onStart callback: ${e.message}")
                }
            }

            override fun onDone(utteranceId: String?) {
                try {
                    utteranceId?.let { onDone?.invoke(it) }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in onDone callback: ${e.message}")
                }
            }

            override fun onError(utteranceId: String?) {
                try {
                    utteranceId?.let { onError?.invoke(it, -1) }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in onError callback: ${e.message}")
                }
            }

            override fun onError(utteranceId: String?, errorCode: Int) {
                try {
                    utteranceId?.let { onError?.invoke(it, errorCode) }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in onError callback: ${e.message}")
                }
            }
        }
    }

    /**
     * Safely speak text with error handling
     */
    fun speakText(
        tts: TextToSpeech?,
        text: String,
        queueMode: Int = TextToSpeech.QUEUE_FLUSH,
        utteranceId: String = "utterance_${System.currentTimeMillis()}"
    ): Boolean {
        return try {
            if (tts == null) {
                Log.w(TAG, "TTS is null, cannot speak text")
                return false
            }
            
            val result = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                tts.speak(text, queueMode, null, utteranceId)
            } else {
                @Suppress("DEPRECATION")
                tts.speak(text, queueMode, hashMapOf(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID to utteranceId))
            }
            
            result == TextToSpeech.SUCCESS
        } catch (e: Exception) {
            Log.e(TAG, "Error speaking text: ${e.message}")
            false
        }
    }
}
