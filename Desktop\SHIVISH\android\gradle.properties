org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Force Java 17 for all modules including plugins
org.gradle.java.home=D:\\SHIVISH\\Java\\jdk-17.0.14+7
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Suppress Java compilation warnings globally
org.gradle.warning.mode=none
android.suppressUnsupportedCompileSdk=35

# Additional Java compilation settings
org.gradle.java.installations.auto-detect=false
org.gradle.java.installations.auto-download=false
org.gradle.jvm.version=17

# Java compiler arguments to suppress warnings
systemProp.java.compiler.args=-Xlint:-options,-Xlint:-deprecation,-Xlint:-unchecked
