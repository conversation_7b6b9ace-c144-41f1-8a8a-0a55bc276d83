// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'banner_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BannerModel _$BannerModelFromJson(Map<String, dynamic> json) {
  return _BannerModel.fromJson(json);
}

/// @nodoc
mixin _$BannerModel {
  /// Unique identifier for the banner
  String get id => throw _privateConstructorUsedError;

  /// Image URL for the banner
  String get imageUrl => throw _privateConstructorUsedError;

  /// Title of the banner
  String get title => throw _privateConstructorUsedError;

  /// Description of the banner
  String? get description => throw _privateConstructorUsedError;

  /// Action type for the banner (e.g., 'product', 'category', 'url')
  String get actionType => throw _privateConstructorUsedError;

  /// Action data for the banner (e.g., product ID, category ID, URL)
  String? get actionData => throw _privateConstructorUsedError;

  /// Priority order for the banner
  int get priority => throw _privateConstructorUsedError;

  /// Whether the banner is active
  bool get isActive => throw _privateConstructorUsedError;

  /// Start date for the banner
  DateTime get startDate => throw _privateConstructorUsedError;

  /// End date for the banner
  DateTime get endDate => throw _privateConstructorUsedError;

  /// Created at timestamp
  DateTime? get createdAt => throw _privateConstructorUsedError;

  /// Updated at timestamp
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this BannerModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BannerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BannerModelCopyWith<BannerModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BannerModelCopyWith<$Res> {
  factory $BannerModelCopyWith(
          BannerModel value, $Res Function(BannerModel) then) =
      _$BannerModelCopyWithImpl<$Res, BannerModel>;
  @useResult
  $Res call(
      {String id,
      String imageUrl,
      String title,
      String? description,
      String actionType,
      String? actionData,
      int priority,
      bool isActive,
      DateTime startDate,
      DateTime endDate,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class _$BannerModelCopyWithImpl<$Res, $Val extends BannerModel>
    implements $BannerModelCopyWith<$Res> {
  _$BannerModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BannerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? imageUrl = null,
    Object? title = null,
    Object? description = freezed,
    Object? actionType = null,
    Object? actionData = freezed,
    Object? priority = null,
    Object? isActive = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      actionType: null == actionType
          ? _value.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as String,
      actionData: freezed == actionData
          ? _value.actionData
          : actionData // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BannerModelImplCopyWith<$Res>
    implements $BannerModelCopyWith<$Res> {
  factory _$$BannerModelImplCopyWith(
          _$BannerModelImpl value, $Res Function(_$BannerModelImpl) then) =
      __$$BannerModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String imageUrl,
      String title,
      String? description,
      String actionType,
      String? actionData,
      int priority,
      bool isActive,
      DateTime startDate,
      DateTime endDate,
      DateTime? createdAt,
      DateTime? updatedAt});
}

/// @nodoc
class __$$BannerModelImplCopyWithImpl<$Res>
    extends _$BannerModelCopyWithImpl<$Res, _$BannerModelImpl>
    implements _$$BannerModelImplCopyWith<$Res> {
  __$$BannerModelImplCopyWithImpl(
      _$BannerModelImpl _value, $Res Function(_$BannerModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BannerModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? imageUrl = null,
    Object? title = null,
    Object? description = freezed,
    Object? actionType = null,
    Object? actionData = freezed,
    Object? priority = null,
    Object? isActive = null,
    Object? startDate = null,
    Object? endDate = null,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$BannerModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      actionType: null == actionType
          ? _value.actionType
          : actionType // ignore: cast_nullable_to_non_nullable
              as String,
      actionData: freezed == actionData
          ? _value.actionData
          : actionData // ignore: cast_nullable_to_non_nullable
              as String?,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BannerModelImpl implements _BannerModel {
  const _$BannerModelImpl(
      {required this.id,
      required this.imageUrl,
      required this.title,
      this.description,
      required this.actionType,
      this.actionData,
      required this.priority,
      required this.isActive,
      required this.startDate,
      required this.endDate,
      this.createdAt,
      this.updatedAt});

  factory _$BannerModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BannerModelImplFromJson(json);

  /// Unique identifier for the banner
  @override
  final String id;

  /// Image URL for the banner
  @override
  final String imageUrl;

  /// Title of the banner
  @override
  final String title;

  /// Description of the banner
  @override
  final String? description;

  /// Action type for the banner (e.g., 'product', 'category', 'url')
  @override
  final String actionType;

  /// Action data for the banner (e.g., product ID, category ID, URL)
  @override
  final String? actionData;

  /// Priority order for the banner
  @override
  final int priority;

  /// Whether the banner is active
  @override
  final bool isActive;

  /// Start date for the banner
  @override
  final DateTime startDate;

  /// End date for the banner
  @override
  final DateTime endDate;

  /// Created at timestamp
  @override
  final DateTime? createdAt;

  /// Updated at timestamp
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'BannerModel(id: $id, imageUrl: $imageUrl, title: $title, description: $description, actionType: $actionType, actionData: $actionData, priority: $priority, isActive: $isActive, startDate: $startDate, endDate: $endDate, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BannerModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.actionType, actionType) ||
                other.actionType == actionType) &&
            (identical(other.actionData, actionData) ||
                other.actionData == actionData) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      imageUrl,
      title,
      description,
      actionType,
      actionData,
      priority,
      isActive,
      startDate,
      endDate,
      createdAt,
      updatedAt);

  /// Create a copy of BannerModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BannerModelImplCopyWith<_$BannerModelImpl> get copyWith =>
      __$$BannerModelImplCopyWithImpl<_$BannerModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BannerModelImplToJson(
      this,
    );
  }
}

abstract class _BannerModel implements BannerModel {
  const factory _BannerModel(
      {required final String id,
      required final String imageUrl,
      required final String title,
      final String? description,
      required final String actionType,
      final String? actionData,
      required final int priority,
      required final bool isActive,
      required final DateTime startDate,
      required final DateTime endDate,
      final DateTime? createdAt,
      final DateTime? updatedAt}) = _$BannerModelImpl;

  factory _BannerModel.fromJson(Map<String, dynamic> json) =
      _$BannerModelImpl.fromJson;

  /// Unique identifier for the banner
  @override
  String get id;

  /// Image URL for the banner
  @override
  String get imageUrl;

  /// Title of the banner
  @override
  String get title;

  /// Description of the banner
  @override
  String? get description;

  /// Action type for the banner (e.g., 'product', 'category', 'url')
  @override
  String get actionType;

  /// Action data for the banner (e.g., product ID, category ID, URL)
  @override
  String? get actionData;

  /// Priority order for the banner
  @override
  int get priority;

  /// Whether the banner is active
  @override
  bool get isActive;

  /// Start date for the banner
  @override
  DateTime get startDate;

  /// End date for the banner
  @override
  DateTime get endDate;

  /// Created at timestamp
  @override
  DateTime? get createdAt;

  /// Updated at timestamp
  @override
  DateTime? get updatedAt;

  /// Create a copy of BannerModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BannerModelImplCopyWith<_$BannerModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
