import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'admin_routes.dart';

/// Navigation helper class for the Admin app
class AdminNavigation {
  // Navigation helpers
  static void navigateToLogin(BuildContext context) => context.go(AdminRoutes.login);
  static void navigateToHome(BuildContext context) => context.go(AdminRoutes.home);
  static void navigateToProfile(BuildContext context) => context.go(AdminRoutes.profile);
  static void navigateToUsers(BuildContext context) => context.go(AdminRoutes.users);
  static void navigateToExecutors(BuildContext context) => context.go(AdminRoutes.executors);
  static void navigateToSellers(BuildContext context) => context.go(AdminRoutes.sellers);
  static void navigateToPriests(BuildContext context) => context.go(AdminRoutes.priests);
  static void navigateToTechnicians(BuildContext context) => context.go(AdminRoutes.technicians);
  static void navigateToProducts(BuildContext context) => context.go(AdminRoutes.products);
  static void navigateToEvents(BuildContext context) => context.go(AdminRoutes.events);
  static void navigateToEventsCreate(BuildContext context) => context.go(AdminRoutes.eventsCreate);
  static void navigateToEventsEdit(BuildContext context, String id) => context.go('${AdminRoutes.eventsEdit}/$id');
  static void navigateToMedia(BuildContext context) => context.go(AdminRoutes.media);
  static void navigateToBanners(BuildContext context) => context.go(AdminRoutes.banners);
  static void navigateToSettings(BuildContext context) => context.go(AdminRoutes.settings);
  static void navigateToAnalytics(BuildContext context) => context.go(AdminRoutes.analytics);
  static void navigateToRefunds(BuildContext context) => context.go(AdminRoutes.refunds);
  static void navigateToCommission(BuildContext context) => context.go(AdminRoutes.commission);
  static void navigateToApiKeys(BuildContext context) => context.go(AdminRoutes.apiKeys);
  static void navigateToBackup(BuildContext context) => context.go(AdminRoutes.backup);
  static void navigateToAiSettings(BuildContext context) => context.go(AdminRoutes.aiSettings);
  static void navigateToVoiceCommandTraining(BuildContext context) => context.go(AdminRoutes.voiceCommandTraining);
  static void navigateToNotifications(BuildContext context) => context.go(AdminRoutes.notifications);
  static void navigateToIconManagement(BuildContext context) => context.go(AdminRoutes.iconManagement);
  static void navigateToBannerPricing(BuildContext context) => context.go(AdminRoutes.bannerPricing);
  static void navigateToRazorpaySettings(BuildContext context) => context.go(AdminRoutes.razorpaySettings);
}
