import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shivish/apps/admin/bloc/payment_gateway/payment_gateway_analytics_bloc.dart';
import 'package:shivish/shared/models/payment_gateway.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class PaymentAnalyticsDashboard extends StatefulWidget {
  final PaymentGateway gateway;

  const PaymentAnalyticsDashboard({
    super.key,
    required this.gateway,
  });

  @override
  State<PaymentAnalyticsDashboard> createState() =>
      _PaymentAnalyticsDashboardState();
}

class _PaymentAnalyticsDashboardState extends State<PaymentAnalyticsDashboard> {
  late DateTimeRange _dateRange;

  @override
  void initState() {
    super.initState();
    _dateRange = DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    );
    _loadAnalytics();
  }

  void _loadAnalytics() {
    context.read<PaymentGatewayAnalyticsBloc>().add(
          PaymentGatewayAnalyticsEvent.loadAnalytics(
            gateway: widget.gateway,
            dateRange: _dateRange,
          ),
        );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? newRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );

    if (newRange != null) {
      setState(() {
        _dateRange = newRange;
      });
      context.read<PaymentGatewayAnalyticsBloc>().add(
            PaymentGatewayAnalyticsEvent.updateDateRange(
              dateRange: newRange,
            ),
          );
    }
  }

  Widget _buildMetricCard(String title, String value, String subtitle) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineChart(List<Map<String, dynamic>> dailyCounts) {
    final spots = dailyCounts.map((data) {
      final date = data['date'] as DateTime;
      final count = data['count'] as int;
      final x = date.difference(_dateRange.start).inDays.toDouble();
      return FlSpot(x, count.toDouble());
    }).toList();

    return SizedBox(
      height: 200,
      child: LineChart(
        LineChartData(
          gridData: const FlGridData(show: true),
          titlesData: FlTitlesData(
            leftTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: true),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                interval: 5,
                getTitlesWidget: (value, meta) {
                  final date =
                      _dateRange.start.add(Duration(days: value.toInt()));
                  return Text(
                    '${date.day}/${date.month}',
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
            rightTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: true),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: Theme.of(context).primaryColor,
              barWidth: 2,
              dotData: const FlDotData(show: false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPieChart(List<Map<String, dynamic>> statusDistribution) {
    final total = statusDistribution.fold<int>(
      0,
      (sum, item) => sum + (item['count'] as int),
    );

    final sections = statusDistribution.map((item) {
      final status = item['status'] as String;
      final count = item['count'] as int;
      final percentage = total > 0 ? (count / total) * 100 : 0;

      Color color;
      switch (status) {
        case 'success':
          color = Colors.green;
          break;
        case 'failed':
          color = Colors.red;
          break;
        default:
          color = Colors.orange;
      }

      return PieChartSectionData(
        value: percentage.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        color: color,
        radius: 100,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return SizedBox(
      height: 200,
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: 40,
          sectionsSpace: 2,
        ),
      ),
    );
  }

  Widget _buildContent(PaymentGatewayAnalyticsState state) {
    if (state is Initial || state is Loading) {
      return const LoadingIndicator();
    } else if (state is Loaded) {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Total Transactions',
                    state.summary['totalTransactions'].toString(),
                    'All time',
                  ),
                ),
                Expanded(
                  child: _buildMetricCard(
                    'Total Amount',
                    '₹${state.summary['totalAmount'].toStringAsFixed(2)}',
                    'All time',
                  ),
                ),
                Expanded(
                  child: _buildMetricCard(
                    'Success Rate',
                    '${state.summary['successRate'].toStringAsFixed(1)}%',
                    'All time',
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Volume',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildLineChart(state.dailyCounts),
                  ],
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transaction Status Distribution',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    _buildPieChart(state.statusDistribution),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else if (state is Error) {
      return ErrorMessage(message: state.message);
    }
    return const LoadingIndicator();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Payment Analytics',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              TextButton.icon(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.calendar_today),
                label: Text(
                  '${_dateRange.start.day}/${_dateRange.start.month} - '
                  '${_dateRange.end.day}/${_dateRange.end.month}',
                ),
              ),
            ],
          ),
        ),
        BlocBuilder<PaymentGatewayAnalyticsBloc, PaymentGatewayAnalyticsState>(
          builder: (context, state) => _buildContent(state),
        ),
      ],
    );
  }
}
