import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/backup.dart';

part 'backup_state.freezed.dart';

@freezed
class BackupState with _$BackupState {
  const factory BackupState.initial() = Initial;
  const factory BackupState.loading() = Loading;
  const factory BackupState.loaded(List<Backup> backups) = Loaded;
  const factory BackupState.error(String message) = Error;
}
