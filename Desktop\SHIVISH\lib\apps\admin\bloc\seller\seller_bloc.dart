import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/seller.dart';
import 'package:shivish/shared/services/seller_service.dart';

part 'seller_event.dart';
part 'seller_state.dart';
part 'seller_bloc.freezed.dart';

@injectable
class SellerBloc extends Bloc<SellerEvent, SellerState> {
  final SellerService _sellerService;
  StreamSubscription<List<Seller>>? _sellerSubscription;
  List<Seller> _cachedSellers = [];
  bool _isRealtimeEnabled = false;

  SellerBloc(this._sellerService) : super(const SellerState()) {
    on<LoadSellers>(_onLoadSellers);
    on<LoadMoreSellers>(_onLoadMoreSellers);
    on<UpdateSellerStatus>(_onUpdateSellerStatus);
    on<UpdateSellerPerformance>(_onUpdateSellerPerformance);
    on<DeleteSeller>(_onDeleteSeller);
    on<CreateSeller>(_onCreateSeller);
    on<StartRealtimeUpdates>(_onStartRealtimeUpdates);
    on<StopRealtimeUpdates>(_onStopRealtimeUpdates);
  }

  Future<void> _onLoadSellers(
    LoadSellers event,
    Emitter<SellerState> emit,
  ) async {
    try {
      emit(state.copyWith(isLoading: true, error: null));
      final sellers = await _sellerService.getSellers();
      emit(state.copyWith(
        isLoading: false,
        sellers: sellers,
        hasMore: sellers.length >= 20,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onLoadMoreSellers(
    LoadMoreSellers event,
    Emitter<SellerState> emit,
  ) async {
    try {
      if (!state.hasMore || state.isLoading) return;
      emit(state.copyWith(isLoading: true, error: null));

      // Get the last seller's document snapshot
      final lastSeller = state.sellers.last;
      final lastDoc = await _sellerService.getSellerDocument(lastSeller.id);

      final moreSellers = await _sellerService.getSellers(
        startAfter: lastDoc,
      );

      emit(state.copyWith(
        isLoading: false,
        sellers: [...state.sellers, ...moreSellers],
        hasMore: moreSellers.length >= 20,
      ));
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        error: e.toString(),
      ));
    }
  }

  Future<void> _onUpdateSellerStatus(
    UpdateSellerStatus event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      final updatedSeller = event.seller.copyWith(
        isActive: event.isActive,
        isSuspended: event.isSuspended,
        updatedAt: DateTime.now(),
      );

      final updatedSellers = currentState.sellers.map((seller) {
        return seller.id == updatedSeller.id ? updatedSeller : seller;
      }).toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.updateSeller(updatedSeller);
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onUpdateSellerPerformance(
    UpdateSellerPerformance event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      final updatedSeller = event.seller.copyWith(
        rating: event.rating,
        totalReviews: event.totalReviews,
        totalOrders: event.totalOrders,
        totalProducts: event.totalProducts,
        totalRevenue: event.totalRevenue,
        updatedAt: DateTime.now(),
      );

      final updatedSellers = currentState.sellers.map((seller) {
        return seller.id == updatedSeller.id ? updatedSeller : seller;
      }).toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.updateSeller(updatedSeller);
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onDeleteSeller(
    DeleteSeller event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      final updatedSellers = currentState.sellers
          .where((seller) => seller.id != event.seller.id)
          .toList();

      emit(currentState.copyWith(sellers: updatedSellers));

      await _sellerService.deleteSeller(event.seller.id);
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onCreateSeller(
    CreateSeller event,
    Emitter<SellerState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! Loaded) return;

      final newSeller = await _sellerService.createSeller(event.seller);
      final updatedSellers = [newSeller, ...currentState.sellers];

      emit(currentState.copyWith(sellers: updatedSellers));
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onStartRealtimeUpdates(
    StartRealtimeUpdates event,
    Emitter<SellerState> emit,
  ) async {
    if (_isRealtimeEnabled) return;
    _isRealtimeEnabled = true;

    try {
      final currentState = state;
      if (currentState is Loaded) {
        _cachedSellers = List.from(currentState.sellers);
      }

      _sellerSubscription = _sellerService.watchSellers().listen(
        (sellers) {
          add(const LoadSellers());
        },
        onError: (error) {
          if (_cachedSellers.isNotEmpty) {
            emit(state.copyWith(
              sellers: _cachedSellers,
              error: 'Real-time updates error: $error',
            ));
          } else {
            emit(state.copyWith(error: 'Real-time updates error: $error'));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(error: e.toString()));
    }
  }

  Future<void> _onStopRealtimeUpdates(
    StopRealtimeUpdates event,
    Emitter<SellerState> emit,
  ) async {
    _isRealtimeEnabled = false;
    await _sellerSubscription?.cancel();
    _sellerSubscription = null;
  }

  @override
  Future<void> close() async {
    await _sellerSubscription?.cancel();
    return super.close();
  }
}
