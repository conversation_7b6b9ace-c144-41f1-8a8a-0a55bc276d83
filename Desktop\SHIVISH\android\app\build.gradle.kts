import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    id("com.google.firebase.firebase-perf")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

val localProperties = Properties()
val localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.reader().use { localProperties.load(it) }
}

val flutterVersionCode: String = localProperties.getProperty("flutter.versionCode") ?: "1"
val flutterVersionName: String = localProperties.getProperty("flutter.versionName") ?: "1.0"
val mapsApiKey: String = localProperties.getProperty("MAPS_API_KEY") ?: ""
val razorpayKeyId: String = localProperties.getProperty("razorpayKeyId") ?: "rzp_test_YOUR_KEY_ID"

val keystoreProperties = Properties()
val keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    FileInputStream(keystorePropertiesFile).use { keystoreProperties.load(it) }
}

android {
    namespace = "com.shivish"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = "17"
        freeCompilerArgs += listOf(
            "-Xlint:-options",
            "-Xlint:-deprecation",
            "-Xlint:-unchecked",
            "-Xjvm-default=all",
            "-Xsuppress-version-warnings"
        )
    }

    lint {
        checkReleaseBuilds = false
        disable += listOf(
            "InvalidPackage",
            "ObsoleteLintCustomCheck",
            "UncheckedCast",
            "Deprecation",
            "NewerVersionAvailable",
            "GradleDependency"
        )
        abortOnError = false
        warningsAsErrors = false
    }

    sourceSets {
        getByName("main") {
            java.srcDirs("src/main/kotlin")
        }
        getByName("test") {
            java.srcDirs("src/test/kotlin")
        }
        getByName("androidTest") {
            java.srcDirs("src/androidTest/kotlin")
        }
    }

    defaultConfig {
        minSdk = 30
        targetSdk = 35
        versionCode = flutterVersionCode.toInt()
        versionName = flutterVersionName
        multiDexEnabled = true
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        manifestPlaceholders["MAPS_API_KEY"] = mapsApiKey

        // Add Razorpay key to BuildConfig
        buildConfigField("String", "razorpayKeyId", "\"$razorpayKeyId\"")

        // Add NDK configuration here to ensure it's applied to all variants
        ndk {
            abiFilters += listOf("armeabi-v7a", "arm64-v8a", "x86_64")
        }

        // Add OpenGL ES configuration
        renderscriptTargetApi = 21
        renderscriptSupportModeEnabled = true
    }

    signingConfigs {
        getByName("debug") {
            storeFile = file("debug.keystore")
            storePassword = "android"
            keyAlias = "androiddebugkey"
            keyPassword = "android"
        }
        create("release") {
            if (keystoreProperties.containsKey("storeFile")) {
                storeFile = file(keystoreProperties.getProperty("storeFile"))
                storePassword = keystoreProperties.getProperty("storePassword")
                keyAlias = keystoreProperties.getProperty("keyAlias")
                keyPassword = keystoreProperties.getProperty("keyPassword")
            }
        }
    }

    buildTypes {
        getByName("release") {
            signingConfig = signingConfigs.getByName("release")
            isMinifyEnabled = false
            isShrinkResources = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
        getByName("debug") {
            signingConfig = signingConfigs.getByName("debug")
        }
    }

    buildFeatures {
        buildConfig = true
    }

    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }

    flavorDimensions += "app"
    productFlavors {
        create("buyer") {
            dimension = "app"
            applicationId = "com.shivish.buyer"
            versionNameSuffix = "-buyer"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_buyer"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_buyer"
        }
        create("seller") {
            dimension = "app"
            applicationId = "com.shivish.seller"
            versionNameSuffix = "-seller"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_seller"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_seller"
        }
        create("priest") {
            dimension = "app"
            applicationId = "com.shivish.priest"
            versionNameSuffix = "-priest"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_priest"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_priest"
        }
        create("technician") {
            dimension = "app"
            applicationId = "com.shivish.technician"
            versionNameSuffix = "-technician"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_technician"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_technician"
        }
        create("executor") {
            dimension = "app"
            applicationId = "com.shivish.executor"
            versionNameSuffix = "-executor"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_executor"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_executor"
        }
        create("admin") {
            dimension = "app"
            applicationId = "com.shivish.admin"
            versionNameSuffix = "-admin"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_admin"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_admin"
        }
        create("saviour") {
            dimension = "app"
            applicationId = "com.shivish.saviour"
            versionNameSuffix = "-saviour"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_saviour"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_saviour"
        }
        create("hospital") {
            dimension = "app"
            applicationId = "com.shivish.hospital"
            versionNameSuffix = "-hospital"
            manifestPlaceholders["ic_launcher"] = "@mipmap/ic_launcher_hospital"
            manifestPlaceholders["ic_launcher_round"] = "@mipmap/ic_launcher_round_hospital"
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.5")
    implementation(platform("com.google.firebase:firebase-bom:33.11.0"))
    implementation("com.google.firebase:firebase-analytics-ktx")
    implementation("com.google.firebase:firebase-auth-ktx")
    implementation("com.google.firebase:firebase-firestore-ktx")
    implementation("com.google.firebase:firebase-storage-ktx")
    implementation("com.google.firebase:firebase-messaging-ktx")
    implementation("com.google.firebase:firebase-crashlytics-ktx")
    implementation("com.google.firebase:firebase-perf-ktx")

    implementation("androidx.multidex:multidex:2.0.1")
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.appcompat:appcompat:1.6.1")
    implementation("com.google.android.material:material:1.11.0")
    implementation("androidx.constraintlayout:constraintlayout:2.1.4")
    implementation("com.google.android.gms:play-services-auth:20.7.0")
    implementation("androidx.work:work-runtime-ktx:2.9.0")
    implementation("androidx.startup:startup-runtime:1.1.1")

    // Razorpay SDK
    implementation("com.razorpay:checkout:1.6.33")

    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}
