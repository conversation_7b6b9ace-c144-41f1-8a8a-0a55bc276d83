import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/storage_config_model.dart';
import '../../../shared/services/storage/storage_config_service.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/widgets/app_loading_indicator.dart';
import '../../../shared/widgets/app_error_widget.dart';
import '../models/collection_config_edit.dart';
import '../widgets/storage_provider_dialog.dart';

final storageConfigProvider = FutureProvider<StorageConfigModel>((ref) async {
  final service = StorageConfigService();
  return service.getStorageConfig();
});

/// Admin screen for managing storage settings
class StorageSettingsScreen extends ConsumerStatefulWidget {
  const StorageSettingsScreen({super.key});

  @override
  ConsumerState<StorageSettingsScreen> createState() => _StorageSettingsScreenState();
}

class _StorageSettingsScreenState extends ConsumerState<StorageSettingsScreen> {
  final _logger = getLogger('StorageSettingsScreen');
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _errorMessage;

  // Map to store temporary edits for collection configs
  final Map<ContentCollectionType, CollectionConfigEdit> _collectionEdits = {};

  @override
  Widget build(BuildContext context) {
    final storageConfigAsync = ref.watch(storageConfigProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Storage Settings'),
      ),
      body: storageConfigAsync.when(
        data: (config) => _buildContent(context, config),
        loading: () => const Center(child: AppLoadingIndicator()),
        error: (error, stackTrace) {
          _logger.error('Error loading storage config: $error\n$stackTrace');
          return AppErrorWidget(
            message: 'Failed to load storage settings: $error',
            onRetry: () => ref.refresh(storageConfigProvider),
          );
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, StorageConfigModel config) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  _errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),

            // Debug mode settings
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Debug Settings',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Skip Document Uploads in Debug Mode'),
                      subtitle: const Text(
                        'When enabled, document uploads will be skipped in debug mode',
                      ),
                      value: config.debugSkipUploads,
                      onChanged: _isLoading
                          ? null
                          : (value) => _updateDebugSkipUploads(value),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Storage providers
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Storage Providers',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: _isLoading ? null : _addStorageProvider,
                          tooltip: 'Add Storage Provider',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ...config.providers.map((provider) => _buildProviderItem(provider)),
                    if (config.providers.isEmpty)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Text('No storage providers configured'),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Collection configurations
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'Collection Storage Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ..._getSortedCollectionConfigs(config.collectionConfigs).map((collectionConfig) =>
                  _buildCollectionConfigItem(collectionConfig, config.providers)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderItem(StorageProviderConfig provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withAlpha(30),
        ),
      ),
      child: Column(
        children: [
          ListTile(
            title: Row(
              children: [
                Text(provider.name),
                if (ref.watch(storageConfigProvider).value?.defaultProvider == provider.name)
                  Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 20,
                    ),
                  ),
              ],
            ),
            subtitle: Text(provider.type.toString().split('.').last),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Switch(
                  value: provider.isEnabled,
                  onChanged: _isLoading
                      ? null
                      : (value) => _updateProviderStatus(provider, value),
                ),
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _isLoading ? null : () => _editProvider(provider),
                ),
                IconButton(
                  icon: const Icon(Icons.delete),
                  onPressed: _isLoading ? null : () => _deleteProvider(provider),
                ),
              ],
            ),
            onTap: _isLoading ? null : () => _editProvider(provider),
          ),
          if (ref.watch(storageConfigProvider).value?.defaultProvider != provider.name)
            Padding(
              padding: const EdgeInsets.only(bottom: 8, left: 16, right: 16),
              child: Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  icon: const Icon(Icons.star_outline, color: Colors.amber),
                  label: const Text('Set as Default'),
                  onPressed: _isLoading ? null : () => _setDefaultProvider(provider),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.amber,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCollectionConfigItem(
    CollectionStorageConfig collectionConfig,
    List<StorageProviderConfig> providers,
  ) {
    // Get or create edit model for this collection
    CollectionConfigEdit edit = _collectionEdits[collectionConfig.collectionType] ??=
        CollectionConfigEdit.fromConfig(collectionConfig);

    final availableProviders = providers.where((p) => p.isEnabled).toList();

    // Get collection type name for display
    final collectionTypeName = collectionConfig.collectionType.toString().split('.').last;

    // Default base path suggestion based on collection type
    final defaultBasePath = collectionTypeName.toLowerCase();

    // Get provider icon based on type
    Widget getProviderIcon(String providerName) {
      final provider = providers.firstWhere(
        (p) => p.name == providerName,
        orElse: () => providers.first,
      );

      switch (provider.type) {
        case StorageProviderType.local:
          return const Icon(Icons.folder, size: 20, color: Colors.amber);
        case StorageProviderType.firebase:
          return const Icon(Icons.cloud, size: 20, color: Colors.blue);
        case StorageProviderType.googleDrive:
          return const Icon(Icons.drive_folder_upload, size: 20, color: Colors.green);
        default:
          return const Icon(Icons.storage, size: 20);
      }
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shadowColor: Theme.of(context).colorScheme.shadow.withAlpha(76),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outlineVariant.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with collection type and enabled switch
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withAlpha(76),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Icon(
                  _getCollectionIcon(collectionConfig.collectionType),
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  collectionTypeName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Row(
                  children: [
                    Text(
                      edit.isEnabled ? 'Enabled' : 'Disabled',
                      style: TextStyle(
                        fontSize: 12,
                        color: edit.isEnabled
                            ? Colors.green
                            : Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    Switch(
                      value: edit.isEnabled,
                      activeColor: Theme.of(context).colorScheme.primary,
                      onChanged: _isLoading
                          ? null
                          : (value) {
                              setState(() {
                                edit.isEnabled = value;
                                edit.hasChanges = true;
                              });
                            },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Primary provider selection with modern dropdown
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.storage, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          'Primary Storage Provider',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withAlpha(128),
                        ),
                      ),
                      child: Builder(
                        builder: (context) {
                          // Check if the current provider exists in available providers
                          final providerExists = availableProviders.any(
                            (p) => p.name == edit.primaryProviderName
                          );

                          // If not, use the first available provider or show a placeholder
                          final effectiveValue = providerExists
                              ? edit.primaryProviderName
                              : availableProviders.isNotEmpty
                                  ? availableProviders.first.name
                                  : null;

                          return DropdownButtonFormField<String>(
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              isDense: true,
                              prefixIcon: Padding(
                                padding: const EdgeInsets.only(left: 12),
                                child: providerExists
                                    ? getProviderIcon(edit.primaryProviderName)
                                    : const Icon(Icons.warning, color: Colors.orange),
                              ),
                            ),
                            value: effectiveValue,
                            icon: const Icon(Icons.arrow_drop_down_rounded),
                            borderRadius: BorderRadius.circular(12),
                            items: availableProviders.map((provider) {
                              return DropdownMenuItem(
                                value: provider.name,
                                child: Row(
                                  children: [
                                    getProviderIcon(provider.name),
                                    const SizedBox(width: 8),
                                    Text(provider.name),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: _isLoading || availableProviders.isEmpty
                                ? null
                                : (value) {
                                    setState(() {
                                      edit.primaryProviderName = value!;
                                      edit.hasChanges = true;
                                    });
                                  },
                            disabledHint: availableProviders.isEmpty
                                ? const Text('No providers available')
                                : null,
                          );
                        }
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Base path field with modern styling
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.folder_open, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          'Base Path',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    InputDecorator(
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.outline.withAlpha(128),
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: Theme.of(context).colorScheme.outline.withAlpha(128),
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        prefixIcon: const Icon(Icons.folder_open),
                        suffixIcon: Tooltip(
                          message: 'This is the fixed directory where files will be stored',
                          child: Icon(
                            Icons.info_outline,
                            size: 16,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Text(
                            defaultBasePath,
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          const Spacer(),
                          if (edit.basePath != defaultBasePath)
                            TextButton(
                              onPressed: _isLoading
                                ? null
                                : () {
                                    setState(() {
                                      edit.basePath = defaultBasePath;
                                      edit.hasChanges = true;
                                    });
                                  },
                              child: const Text('Fix Path'),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Load balancing strategy with modern styling
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.balance, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          'Load Balancing Strategy',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withAlpha(128),
                        ),
                      ),
                      child: DropdownButtonFormField<BalancingStrategy>(
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          isDense: true,
                        ),
                        value: edit.balancingStrategy,
                        icon: const Icon(Icons.arrow_drop_down_rounded),
                        borderRadius: BorderRadius.circular(12),
                        items: BalancingStrategy.values.map((strategy) {
                          IconData icon;
                          switch (strategy) {
                            case BalancingStrategy.singleProvider:
                              icon = Icons.looks_one;
                              break;
                            case BalancingStrategy.roundRobin:
                              icon = Icons.loop;
                              break;
                            case BalancingStrategy.random:
                              icon = Icons.shuffle;
                              break;
                          }

                          return DropdownMenuItem(
                            value: strategy,
                            child: Row(
                              children: [
                                Icon(icon, size: 18),
                                const SizedBox(width: 8),
                                Text(_getBalancingStrategyName(strategy)),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: _isLoading
                            ? null
                            : (value) {
                                setState(() {
                                  edit.balancingStrategy = value!;
                                  edit.hasChanges = true;
                                });
                              },
                      ),
                    ),
                  ],
                ),

                // Additional providers section with modern styling
                if (edit.balancingStrategy != BalancingStrategy.singleProvider) ...[
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Icon(Icons.add_circle_outline, size: 16, color: Colors.grey),
                      const SizedBox(width: 4),
                      Text(
                        'Additional Providers',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (edit.additionalProviderNames.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(76),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withAlpha(51),
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_circle_outline,
                            size: 24,
                            color: Theme.of(context).colorScheme.primary.withAlpha(179),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'No additional providers',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.add, size: 16),
                            label: const Text('Add Provider'),
                            onPressed: _isLoading
                                ? null
                                : () => _showAddProviderDialog(edit, availableProviders),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                              foregroundColor: Theme.of(context).colorScheme.onPrimaryContainer,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(76),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withAlpha(51),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              ...edit.additionalProviderNames.map((providerName) =>
                                Chip(
                                  avatar: getProviderIcon(providerName),
                                  label: Text(providerName),
                                  deleteIcon: const Icon(Icons.close, size: 16),
                                  backgroundColor: Theme.of(context).colorScheme.surface,
                                  side: BorderSide(
                                    color: Theme.of(context).colorScheme.outline.withAlpha(76),
                                  ),
                                  onDeleted: _isLoading
                                      ? null
                                      : () {
                                          setState(() {
                                            edit.additionalProviderNames.remove(providerName);
                                            edit.hasChanges = true;
                                          });
                                        },
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Align(
                            alignment: Alignment.centerRight,
                            child: TextButton.icon(
                              icon: const Icon(Icons.add, size: 16),
                              label: const Text('Add Provider'),
                              onPressed: _isLoading
                                  ? null
                                  : () => _showAddProviderDialog(edit, availableProviders),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ],
            ),
          ),

          // Save button at the bottom if there are changes
          if (edit.hasChanges)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(50),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _isLoading
                        ? null
                        : () {
                            setState(() {
                              // Reset the edit to match the original config
                              _collectionEdits[collectionConfig.collectionType] =
                                  CollectionConfigEdit.fromConfig(collectionConfig);
                            });
                          },
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.save),
                    label: const Text('Save Changes'),
                    onPressed: _isLoading
                        ? null
                        : () => _saveCollectionChanges(edit, collectionConfig),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  IconData _getCollectionIcon(ContentCollectionType type) {
    switch (type) {
      case ContentCollectionType.images:
        return Icons.image;
      case ContentCollectionType.documents:
        return Icons.description;
      case ContentCollectionType.media:
        return Icons.video_library;
      case ContentCollectionType.backups:
        return Icons.backup;
      case ContentCollectionType.userFiles:
        return Icons.folder_shared;
      default:
        return Icons.folder;
    }
  }

  // Methods to handle user actions
  void _addStorageProvider() async {
    final result = await showDialog<StorageProviderConfig>(
      context: context,
      builder: (context) => const StorageProviderDialog(),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final service = StorageConfigService();
        await service.addOrUpdateProvider(result);
        final _ = ref.refresh(storageConfigProvider);
      } catch (e) {
        _logger.error('Error adding storage provider: $e');
        setState(() {
          _errorMessage = 'Failed to add storage provider: $e';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _editProvider(StorageProviderConfig provider) async {
    final result = await showDialog<StorageProviderConfig>(
      context: context,
      builder: (context) => StorageProviderDialog(provider: provider),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final service = StorageConfigService();
        await service.addOrUpdateProvider(result);
        final _ = ref.refresh(storageConfigProvider);
      } catch (e) {
        _logger.error('Error updating storage provider: $e');
        setState(() {
          _errorMessage = 'Failed to update storage provider: $e';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _deleteProvider(StorageProviderConfig provider) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Provider'),
        content: Text('Are you sure you want to delete ${provider.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      try {
        final service = StorageConfigService();
        final config = await service.getStorageConfig();

        // Remove the provider
        final providers = config.providers
            .where((p) => p.name != provider.name)
            .toList();

        // Update any collection configs using this provider to use the default
        final collectionConfigs = config.collectionConfigs.map((c) {
          if (c.primaryProviderName == provider.name) {
            return c.copyWith(
              primaryProviderName: config.defaultProvider == provider.name
                  ? (providers.isNotEmpty ? providers.first.name : 'local')
                  : config.defaultProvider,
            );
          }
          return c;
        }).toList();

        // Update default provider if needed
        final defaultProvider = config.defaultProvider == provider.name
            ? (providers.isNotEmpty ? providers.first.name : 'local')
            : config.defaultProvider;

        // Save updated config
        await service.saveStorageConfig(config.copyWith(
          providers: providers,
          collectionConfigs: collectionConfigs,
          defaultProvider: defaultProvider,
        ));

        final _ = ref.refresh(storageConfigProvider);
      } catch (e) {
        _logger.error('Error deleting storage provider: $e');
        setState(() {
          _errorMessage = 'Failed to delete storage provider: $e';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _updateProviderStatus(StorageProviderConfig provider, bool isEnabled) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = StorageConfigService();
      final updatedProvider = provider.copyWith(isEnabled: isEnabled);
      await service.addOrUpdateProvider(updatedProvider);
      final _ = ref.refresh(storageConfigProvider);
    } catch (e) {
      _logger.error('Error updating provider status: $e');
      setState(() {
        _errorMessage = 'Failed to update provider status: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  void _updateDebugSkipUploads(bool skipUploads) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = StorageConfigService();
      await service.setDebugSkipUploads(skipUploads);
      final _ = ref.refresh(storageConfigProvider);
    } catch (e) {
      _logger.error('Error updating debug skip uploads: $e');
      setState(() {
        _errorMessage = 'Failed to update debug skip uploads: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }



  String _getBalancingStrategyName(BalancingStrategy strategy) {
    switch (strategy) {
      case BalancingStrategy.singleProvider:
        return 'Single Provider (No Load Balancing)';
      case BalancingStrategy.roundRobin:
        return 'Round Robin';
      case BalancingStrategy.random:
        return 'Random';
    }
  }

  void _setDefaultProvider(StorageProviderConfig provider) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = StorageConfigService();
      await service.setDefaultProvider(provider.name);
      final _ = ref.refresh(storageConfigProvider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${provider.name} set as default storage provider'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.error('Error setting default provider: $e');
      setState(() {
        _errorMessage = 'Failed to set default provider: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to set default provider: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Show dialog to add a provider to the additional providers list
  void _showAddProviderDialog(CollectionConfigEdit edit, List<StorageProviderConfig> availableProviders) async {
    // Filter out providers that are already in the list or are the primary provider
    final filteredProviders = availableProviders
        .where((p) =>
            p.name != edit.primaryProviderName &&
            !edit.additionalProviderNames.contains(p.name))
        .toList();

    if (filteredProviders.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No additional providers available'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Provider'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: filteredProviders.length,
            itemBuilder: (context, index) {
              final provider = filteredProviders[index];
              return ListTile(
                leading: _getProviderIcon(provider),
                title: Text(provider.name),
                subtitle: Text(provider.type.toString().split('.').last),
                onTap: () => Navigator.of(context).pop(provider.name),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        edit.additionalProviderNames.add(result);
        edit.hasChanges = true;
      });
    }
  }

  /// Get provider icon based on type
  Widget _getProviderIcon(StorageProviderConfig provider) {
    switch (provider.type) {
      case StorageProviderType.local:
        return const Icon(Icons.folder, size: 24, color: Colors.amber);
      case StorageProviderType.firebase:
        return const Icon(Icons.cloud, size: 24, color: Colors.blue);
      case StorageProviderType.googleDrive:
        return const Icon(Icons.drive_folder_upload, size: 24, color: Colors.green);
      default:
        return const Icon(Icons.storage, size: 24);
    }
  }

  /// Save changes to a collection config
  void _saveCollectionChanges(CollectionConfigEdit edit, CollectionStorageConfig originalConfig) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final service = StorageConfigService();
      await service.addOrUpdateCollectionConfig(edit.toConfig());
      final _ = ref.refresh(storageConfigProvider);

      // Clear the edit state
      edit.hasChanges = false;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${edit.collectionType.toString().split('.').last} settings saved'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.error('Error saving collection config: $e');
      setState(() {
        _errorMessage = 'Failed to save collection config: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Sort collection configs in a consistent order
  List<CollectionStorageConfig> _getSortedCollectionConfigs(List<CollectionStorageConfig> configs) {
    // Define the order of collection types
    final order = {
      ContentCollectionType.images: 0,
      ContentCollectionType.documents: 1,
      ContentCollectionType.media: 2,
      ContentCollectionType.userFiles: 3,
      ContentCollectionType.backups: 4,
      ContentCollectionType.other: 5,
    };

    // Create a copy of the list to avoid modifying the original
    final sortedConfigs = List<CollectionStorageConfig>.from(configs);

    // Sort by the predefined order
    sortedConfigs.sort((a, b) {
      final orderA = order[a.collectionType] ?? 999;
      final orderB = order[b.collectionType] ?? 999;
      return orderA.compareTo(orderB);
    });

    return sortedConfigs;
  }
}
