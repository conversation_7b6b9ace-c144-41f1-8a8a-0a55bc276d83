import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/technician.dart';

part 'technician_event.freezed.dart';

@freezed
class TechnicianEvent with _$TechnicianEvent {
  const factory TechnicianEvent.loadTechnicians() = _LoadTechnicians;
  const factory TechnicianEvent.loadMoreTechnicians() = _LoadMoreTechnicians;
  const factory TechnicianEvent.updateTechnicianStatus({
    required Technician technician,
    required String status,
    String? notes,
    required bool isActive,
  }) = _UpdateTechnicianStatus;
  const factory TechnicianEvent.deleteTechnician({
    required String id,
  }) = _DeleteTechnician;
  const factory TechnicianEvent.applyFilters({
    required Map<String, dynamic> filters,
  }) = _ApplyFilters;
}
