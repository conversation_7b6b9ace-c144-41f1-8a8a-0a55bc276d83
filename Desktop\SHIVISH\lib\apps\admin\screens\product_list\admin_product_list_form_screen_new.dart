import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_visibility.dart';
import '../../../../shared/models/calendar/calendar_event_status.dart';
import '../../../../shared/models/calendar/calendar_event_recurrence.dart';
import '../../../../shared/models/calendar/calendar_event_reminder.dart';
import '../../../../shared/models/calendar/event_product_model.dart';
import '../../../../shared/models/product/product_model.dart';
import '../../../../shared/providers/auth_provider.dart';
import '../../../../shared/services/calendar/calendar_event_service.dart';

// Provider for calendar event service
final calendarEventServiceProvider = Provider<CalendarEventService>((ref) {
  return CalendarEventService();
});

// Provider for mock products
final mockProductsProvider = Provider<List<ProductModel>>((ref) {
  return [
    ProductModel(
      id: '1',
      name: 'Rice',
      description: 'Premium quality basmati rice',
      price: 120.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '2',
      name: 'Wheat Flour',
      description: 'Organic whole wheat flour',
      price: 45.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '3',
      name: 'Sugar',
      description: 'Refined white sugar',
      price: 40.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '4',
      name: 'Cooking Oil',
      description: 'Pure sunflower oil',
      price: 110.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
    ProductModel(
      id: '5',
      name: 'Milk',
      description: 'Fresh cow milk',
      price: 25.0,
      categoryId: 'cat1',
      sellerId: 'seller1',
      images: [],
      productType: ProductType.physical,
      productStatus: ProductStatus.approved,
      quantity: 100,
    ),
  ];
});

/// Screen for creating or editing admin product lists
class AdminProductListFormScreen extends ConsumerStatefulWidget {
  /// Creates an [AdminProductListFormScreen]
  const AdminProductListFormScreen({
    this.event,
    super.key,
  });

  /// The event to edit, or null to create a new event
  final CalendarEventModel? event;

  @override
  ConsumerState<AdminProductListFormScreen> createState() =>
      _AdminProductListFormScreenState();
}

class _AdminProductListFormScreenState
    extends ConsumerState<AdminProductListFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();

  DateTime _startDate = DateTime.now();
  DateTime _endDate = DateTime.now().add(const Duration(days: 7));
  CalendarEventVisibility _visibility = CalendarEventVisibility.public;
  CalendarEventStatus _status = CalendarEventStatus.scheduled;
  bool _isRecurring = false;
  CalendarEventRecurrence? _recurrenceRule;
  bool _hasReminder = false;
  CalendarEventReminder? _reminder;

  // Location selection
  String _selectedCity = 'Delhi';
  String _selectedArea = 'New Delhi';

  // Available cities and areas
  final List<String> _availableCities = [
    'Delhi',
    'Mumbai',
    'Bangalore',
    'Chennai',
    'Hyderabad',
    'Kolkata',
  ];

  // Map of city to areas
  final Map<String, List<String>> _availableAreas = {
    'Delhi': ['New Delhi', 'Old Delhi', 'Noida', 'Gurgaon', 'Faridabad'],
    'Mumbai': ['Andheri', 'Bandra', 'Juhu', 'Worli', 'Dadar'],
    'Bangalore': [
      'Koramangala',
      'Indiranagar',
      'Whitefield',
      'Electronic City',
      'Jayanagar'
    ],
    'Chennai': ['T Nagar', 'Adyar', 'Anna Nagar', 'Velachery', 'Mylapore'],
    'Hyderabad': [
      'Banjara Hills',
      'Jubilee Hills',
      'Hitech City',
      'Secunderabad',
      'Gachibowli'
    ],
    'Kolkata': ['Park Street', 'Salt Lake', 'New Town', 'Howrah', 'Ballygunge'],
  };

  List<ProductModel> _selectedProducts = [];

  @override
  void initState() {
    super.initState();

    if (widget.event != null) {
      // Initialize with existing event data
      _titleController.text = widget.event!.title;
      _descriptionController.text = widget.event!.description;
      _startDate = widget.event!.startDate;
      _endDate = widget.event!.endDate;
      _visibility = widget.event!.visibility;
      _status = widget.event!.status;
      _isRecurring = widget.event!.isRecurring;
      _recurrenceRule = widget.event!.recurrenceRule;
      _hasReminder = widget.event!.hasReminder;
      _reminder = widget.event!.reminder;

      // We can't directly convert EventProduct to ProductModel, so we'll leave this empty
      // In a real app, you would fetch the actual ProductModel objects
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider).value;

    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please sign in to create product lists'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
            widget.event == null ? 'Create Product List' : 'Edit Product List'),
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Title
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Title',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Description
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),

            // Date range
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: _selectStartDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Start Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(_formatDate(_startDate)),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectEndDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'End Date',
                        border: OutlineInputBorder(),
                      ),
                      child: Text(_formatDate(_endDate)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Visibility and Status
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<CalendarEventVisibility>(
                    value: _visibility,
                    decoration: const InputDecoration(
                      labelText: 'Visibility',
                      border: OutlineInputBorder(),
                    ),
                    items: CalendarEventVisibility.values.map((visibility) {
                      return DropdownMenuItem(
                        value: visibility,
                        child: Text(_getVisibilityLabel(visibility)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _visibility = value;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<CalendarEventStatus>(
                    value: _status,
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      border: OutlineInputBorder(),
                    ),
                    items: CalendarEventStatus.values.map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusLabel(status)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _status = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Location selection
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Location',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'This product list will only be visible to buyers in the selected locations',
                          style: theme.textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 16),
                        // City selection
                        DropdownButtonFormField<String>(
                          value: _selectedCity,
                          decoration: const InputDecoration(
                            labelText: 'City',
                            border: OutlineInputBorder(),
                          ),
                          items: _availableCities.map((city) {
                            return DropdownMenuItem(
                              value: city,
                              child: Text(city),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedCity = value;
                                // Reset area when city changes
                                _selectedArea =
                                    _availableAreas[_selectedCity]!.first;
                              });
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a city';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        // Area selection
                        DropdownButtonFormField<String>(
                          value: _selectedArea,
                          decoration: const InputDecoration(
                            labelText: 'Area',
                            border: OutlineInputBorder(),
                          ),
                          items: _availableAreas[_selectedCity]!.map((area) {
                            return DropdownMenuItem(
                              value: area,
                              child: Text(area),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedArea = value;
                              });
                            }
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select an area';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Products section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Products',
                      style: theme.textTheme.titleMedium,
                    ),
                    ElevatedButton.icon(
                      onPressed: _showProductSelectionDialog,
                      icon: const Icon(Icons.add),
                      label: const Text('Add Products'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (_selectedProducts.isEmpty)
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: Text('No products selected'),
                    ),
                  )
                else
                  Card(
                    child: ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _selectedProducts.length,
                      separatorBuilder: (context, index) => const Divider(),
                      itemBuilder: (context, index) {
                        final product = _selectedProducts[index];
                        return ListTile(
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: product.images.isNotEmpty
                                  ? Image.network(
                                      product.images.first,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error,
                                              stackTrace) =>
                                          const Icon(Icons.image_not_supported),
                                    )
                                  : const Icon(Icons.shopping_cart),
                            ),
                          ),
                          title: Text(product.name),
                          subtitle: Text(product.description),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () {
                              setState(() {
                                _selectedProducts.removeAt(index);
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 32),

            // Submit button
            ElevatedButton(
              onPressed: _submitForm,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text(
                widget.event == null
                    ? 'Create Product List'
                    : 'Update Product List',
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );

    if (date != null) {
      setState(() {
        _startDate = date;
        // Ensure end date is not before start date
        if (_endDate.isBefore(_startDate)) {
          _endDate = _startDate.add(const Duration(days: 1));
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime.now().add(const Duration(days: 365 * 10)),
    );

    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _getVisibilityLabel(CalendarEventVisibility visibility) {
    switch (visibility) {
      case CalendarEventVisibility.private:
        return 'Private';
      case CalendarEventVisibility.public:
        return 'Public';
      case CalendarEventVisibility.shared:
        return 'Shared';
    }
  }

  String _getStatusLabel(CalendarEventStatus status) {
    switch (status) {
      case CalendarEventStatus.scheduled:
        return 'Scheduled';
      case CalendarEventStatus.completed:
        return 'Completed';
      case CalendarEventStatus.cancelled:
        return 'Cancelled';
      case CalendarEventStatus.postponed:
        return 'Postponed';
    }
  }

  Future<void> _showProductSelectionDialog() async {
    // Get the mock products
    final products = ref.read(mockProductsProvider);

    // Show a dialog to select products
    final result = await showDialog<List<ProductModel>>(
      context: context,
      builder: (context) => ProductSelectionDialog(
        products: products,
        selectedProducts: _selectedProducts,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedProducts = result;
      });
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    final user = ref.read(currentUserProvider).value;
    if (user == null) return;

    try {
      // Add location information to the description
      final locationInfo = 'Location: $_selectedCity, $_selectedArea';
      final fullDescription = '${_descriptionController.text}\n\n$locationInfo';

      final event = CalendarEventModel(
        id: widget.event?.id ?? '',
        title: _titleController.text,
        description: fullDescription,
        startDate: _startDate,
        endDate: _endDate,
        type: CalendarEventType.productList,
        visibility: _visibility,
        status: _status,
        isRecurring: _isRecurring,
        recurrenceRule: _recurrenceRule,
        hasReminder: _hasReminder,
        reminder: _reminder,
        createdAt: widget.event?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        products: _selectedProducts
            .map((p) => EventProduct(
                  id: p.id,
                  name: p.name,
                  description: p.description,
                  price: p.price,
                  imageUrl: p.images.isNotEmpty ? p.images.first : null,
                ))
            .toList(),
      );

      if (widget.event == null) {
        // Create new event
        await ref.read(calendarEventServiceProvider).createEvent(event);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product list created successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      } else {
        // Update existing event
        await ref.read(calendarEventServiceProvider).updateEvent(event);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product list updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save product list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Dialog for selecting products
class ProductSelectionDialog extends StatefulWidget {
  /// Creates a [ProductSelectionDialog]
  const ProductSelectionDialog({
    required this.products,
    this.selectedProducts = const [],
    super.key,
  });

  /// The available products
  final List<ProductModel> products;

  /// The currently selected products
  final List<ProductModel> selectedProducts;

  @override
  State<ProductSelectionDialog> createState() => _ProductSelectionDialogState();
}

class _ProductSelectionDialogState extends State<ProductSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<ProductModel> _selectedProducts = [];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    _selectedProducts = List.from(widget.selectedProducts);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Filter products by search query
    final filteredProducts = widget.products.where((product) {
      if (_searchQuery.isEmpty) return true;
      return product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          product.description
              .toLowerCase()
              .contains(_searchQuery.toLowerCase());
    }).toList();

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.maxFinite,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select Products',
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Search and select products to add to your list',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 200),
                    ),
                  ),
                ],
              ),
            ),

            // Search bar
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search products',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            // Selected products count
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Selected: ${_selectedProducts.length} products',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // Product list
            Expanded(
              child: filteredProducts.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: theme.colorScheme.primary
                                .withValues(alpha: 128),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No products found',
                            style: theme.textTheme.titleMedium,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: filteredProducts.length,
                      itemBuilder: (context, index) {
                        final product = filteredProducts[index];
                        final isSelected = _isProductSelected(product.id);

                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (value) {
                              if (value == true) {
                                _addProduct(product);
                              } else {
                                _removeProduct(product.id);
                              }
                            },
                            title: Text(product.name),
                            subtitle: Text(
                              product.description,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            secondary: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade300),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(4),
                                child: product.images.isNotEmpty
                                    ? Image.network(
                                        product.images.first,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                const Icon(
                                                    Icons.image_not_supported),
                                      )
                                    : const Icon(Icons.shopping_cart),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),

            // Action buttons
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () =>
                        Navigator.of(context).pop(_selectedProducts),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Done'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _isProductSelected(String productId) {
    return _selectedProducts.any((p) => p.id == productId);
  }

  void _addProduct(ProductModel product) {
    if (!_isProductSelected(product.id)) {
      setState(() {
        _selectedProducts.add(product);
      });
    }
  }

  void _removeProduct(String productId) {
    setState(() {
      _selectedProducts.removeWhere((p) => p.id == productId);
    });
  }
}
