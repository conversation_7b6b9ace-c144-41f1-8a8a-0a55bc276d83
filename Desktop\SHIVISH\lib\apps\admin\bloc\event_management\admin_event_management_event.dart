import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/event/event_model.dart';

part 'admin_event_management_event.freezed.dart';

@freezed
class AdminEventManagementEvent with _$AdminEventManagementEvent {
  const factory AdminEventManagementEvent.loadPendingEvents() =
      LoadPendingEvents;

  const factory AdminEventManagementEvent.approveEvent({
    required String eventId,
    required String adminId,
  }) = ApproveEvent;

  const factory AdminEventManagementEvent.rejectEvent({
    required String eventId,
    required String adminId,
    required String reason,
  }) = RejectEvent;

  const factory AdminEventManagementEvent.createEvent({
    required EventModel event,
  }) = CreateEvent;

  const factory AdminEventManagementEvent.updateEvent({
    required EventModel event,
  }) = UpdateEvent;
}
