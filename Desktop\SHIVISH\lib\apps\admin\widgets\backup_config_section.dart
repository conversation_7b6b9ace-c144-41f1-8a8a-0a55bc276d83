import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/backup_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class BackupConfigSection extends ConsumerWidget {
  final BackupConfigModel config;

  const BackupConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Backup Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTextField(
              context,
              ref,
              'Storage Location',
              config.storageLocation,
              (value) => _updateConfig(
                ref,
                config.copyWith(storageLocation: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Backup Frequency (hours)',
              config.backupFrequencyHours,
              (value) => _updateConfig(
                ref,
                config.copyWith(backupFrequencyHours: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Retention (days)',
              config.retentionDays,
              (value) => _updateConfig(
                ref,
                config.copyWith(retentionDays: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Auto Backup',
              config.autoBackup,
              (value) => _updateConfig(
                ref,
                config.copyWith(autoBackup: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField(
    BuildContext context,
    WidgetRef ref,
    String label,
    String value,
    Function(String) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Expanded(
          child: TextFormField(
            initialValue: value,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberField(
    BuildContext context,
    WidgetRef ref,
    String label,
    int value,
    Function(int) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        SizedBox(
          width: 100,
          child: TextFormField(
            initialValue: value.toString(),
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              final number = int.tryParse(value);
              if (number != null) {
                onChanged(number);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    BuildContext context,
    WidgetRef ref,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    BackupConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateBackupConfig(newConfig);
  }
}
