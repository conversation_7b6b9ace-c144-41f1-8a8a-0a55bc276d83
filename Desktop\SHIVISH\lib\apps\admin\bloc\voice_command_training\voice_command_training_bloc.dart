import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/voice_command_training/voice_command_training_event.dart';
import 'package:shivish/apps/admin/bloc/voice_command_training/voice_command_training_state.dart';
import 'package:shivish/shared/services/voice_command_training_service.dart';

class VoiceCommandTrainingBloc
    extends Bloc<VoiceCommandTrainingEvent, VoiceCommandTrainingState> {
  final VoiceCommandTrainingService _service;

  VoiceCommandTrainingBloc(this._service)
      : super(const VoiceCommandTrainingState.initial()) {
    on<LoadVoiceCommandTrainings>(_onLoadVoiceCommandTrainings);
    on<CreateVoiceCommandTraining>(_onCreateVoiceCommandTraining);
    on<UpdateVoiceCommandTraining>(_onUpdateVoiceCommandTraining);
    on<DeleteVoiceCommandTraining>(_onDeleteVoiceCommandTraining);
    on<UpdateVoiceCommandTrainingStatus>(_onUpdateVoiceCommandTrainingStatus);
  }

  Future<void> _onLoadVoiceCommandTrainings(
    LoadVoiceCommandTrainings event,
    Emitter<VoiceCommandTrainingState> emit,
  ) async {
    try {
      emit(const VoiceCommandTrainingState.loading());
      final trainings = await _service.getVoiceCommandTrainings();
      emit(VoiceCommandTrainingState.loaded(trainings));
    } catch (e) {
      emit(VoiceCommandTrainingState.error(e.toString()));
    }
  }

  Future<void> _onCreateVoiceCommandTraining(
    CreateVoiceCommandTraining event,
    Emitter<VoiceCommandTrainingState> emit,
  ) async {
    try {
      emit(const VoiceCommandTrainingState.loading());
      await _service.createVoiceCommandTraining(event.training);
      final trainings = await _service.getVoiceCommandTrainings();
      emit(VoiceCommandTrainingState.loaded(trainings));
    } catch (e) {
      emit(VoiceCommandTrainingState.error(e.toString()));
    }
  }

  Future<void> _onUpdateVoiceCommandTraining(
    UpdateVoiceCommandTraining event,
    Emitter<VoiceCommandTrainingState> emit,
  ) async {
    try {
      emit(const VoiceCommandTrainingState.loading());
      await _service.updateVoiceCommandTraining(event.training);
      final trainings = await _service.getVoiceCommandTrainings();
      emit(VoiceCommandTrainingState.loaded(trainings));
    } catch (e) {
      emit(VoiceCommandTrainingState.error(e.toString()));
    }
  }

  Future<void> _onDeleteVoiceCommandTraining(
    DeleteVoiceCommandTraining event,
    Emitter<VoiceCommandTrainingState> emit,
  ) async {
    try {
      emit(const VoiceCommandTrainingState.loading());
      await _service.deleteVoiceCommandTraining(event.id);
      final trainings = await _service.getVoiceCommandTrainings();
      emit(VoiceCommandTrainingState.loaded(trainings));
    } catch (e) {
      emit(VoiceCommandTrainingState.error(e.toString()));
    }
  }

  Future<void> _onUpdateVoiceCommandTrainingStatus(
    UpdateVoiceCommandTrainingStatus event,
    Emitter<VoiceCommandTrainingState> emit,
  ) async {
    try {
      emit(const VoiceCommandTrainingState.loading());
      await _service.updateVoiceCommandTrainingStatus(
          event.id, event.isEnabled);
      final trainings = await _service.getVoiceCommandTrainings();
      emit(VoiceCommandTrainingState.loaded(trainings));
    } catch (e) {
      emit(VoiceCommandTrainingState.error(e.toString()));
    }
  }
}
