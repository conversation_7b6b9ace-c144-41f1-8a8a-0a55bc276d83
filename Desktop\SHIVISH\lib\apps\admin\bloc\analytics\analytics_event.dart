import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/apps/admin/widgets/analytics_filter_dialog.dart';

part 'analytics_event.freezed.dart';

@freezed
class AnalyticsEvent with _$AnalyticsEvent {
  const factory AnalyticsEvent.loadData({
    required String timeRange,
  }) = LoadAnalyticsData;

  const factory AnalyticsEvent.updateTimeRange({
    required String timeRange,
  }) = UpdateTimeRange;

  const factory AnalyticsEvent.refreshData() = RefreshAnalyticsData;

  const factory AnalyticsEvent.applyFilters(AnalyticsFilterData filters) =
      ApplyFilters;
}
