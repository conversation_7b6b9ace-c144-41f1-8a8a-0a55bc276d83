import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/media/media_model.dart';
import '../../../shared/services/auth/auth_service.dart';
import '../admin_routes.dart';
import '../bloc/media_management/admin_media_management_bloc.dart';
import '../bloc/media_management/admin_media_management_event_simple.dart';
import '../bloc/media_management/admin_media_management_state.dart';
import 'media_approval_card.dart';
import 'admin_media_form_screen.dart';

class AdminMediaManagementScreen extends StatefulWidget {
  const AdminMediaManagementScreen({super.key});

  @override
  State<AdminMediaManagementScreen> createState() =>
      _AdminMediaManagementScreenState();
}

class _AdminMediaManagementScreenState
    extends State<AdminMediaManagementScreen> {
  String? _currentUserId;

  @override
  void initState() {
    super.initState();
    _loadCurrentUserId();
  }

  Future<void> _loadCurrentUserId() async {
    final authService = context.read<AuthService>();
    final user = await authService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUserId = user.id;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Media Management'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                // If we can't pop, use GoRouter to navigate back to home
                context.go(AdminRoutes.home);
              }
            },
          ),
        ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AdminMediaFormScreen(),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
      body: BlocConsumer<AdminMediaManagementBloc, AdminMediaManagementState>(
        listener: (context, state) {
          state.maybeWhen(
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            mediaApproved: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            mediaRejected: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            mediaUploaded: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            mediaUpdated: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return state.when(
            initial: () {
              context.read<AdminMediaManagementBloc>().add(
                    const LoadPendingMediaEvent(),
                  );
              return const Center(child: CircularProgressIndicator());
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (message) => Center(
              child: Text(
                message,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ),
            loadedPendingMedia: (mediaList) =>
                _buildMediaList(context, mediaList),
            mediaApproved: (message) =>
                const Center(child: Text('Media approved')),
            mediaRejected: (message) =>
                const Center(child: Text('Media rejected')),
            mediaUploaded: (message) =>
                const Center(child: Text('Media uploaded')),
            mediaUpdated: (message) =>
                const Center(child: Text('Media updated')),
          );
        },
      ),
    );
  }

  Widget _buildMediaList(BuildContext context, List<MediaModel> mediaList) {
    if (mediaList.isEmpty) {
      return const Center(child: Text('No pending media'));
    }

    if (_currentUserId == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return ListView.builder(
      itemCount: mediaList.length,
      itemBuilder: (context, index) {
        final media = mediaList[index];
        return MediaApprovalCard(
          media: media,
          onApprove: () {
            context.read<AdminMediaManagementBloc>().add(
                  ApproveMediaEvent(
                    mediaId: media.id,
                    adminId: _currentUserId!,
                  ),
                );
          },
          onReject: (reason) {
            context.read<AdminMediaManagementBloc>().add(
                  RejectMediaEvent(
                    mediaId: media.id,
                    adminId: _currentUserId!,
                    reason: reason,
                  ),
                );
          },
          onEdit: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => AdminMediaFormScreen(media: media),
              ),
            );
          },
        );
      },
    );
  }
}
