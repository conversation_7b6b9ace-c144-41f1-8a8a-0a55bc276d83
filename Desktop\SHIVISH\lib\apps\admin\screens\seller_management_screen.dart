import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../widgets/seller_list_item.dart';
import '../../../shared/models/seller.dart';

class SellerManagementScreen extends StatefulWidget {
  const SellerManagementScreen({super.key});

  @override
  State<SellerManagementScreen> createState() => _SellerManagementScreenState();
}

class _SellerManagementScreenState extends State<SellerManagementScreen> {
  final _scrollController = ScrollController();
  late Stream<List<Seller>> _sellersStream;
  List<Seller> _sellers = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _setupSellersStream();
  }

  void _setupSellersStream() {
    try {
      _sellersStream = FirebaseFirestore.instance
          .collection('sellers')
          .where('isDeleted', isEqualTo: false)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
        debugPrint('Found ${snapshot.docs.length} sellers in collection');

        // Print all sellers for debugging
        for (var doc in snapshot.docs) {
          debugPrint('Seller ID: ${doc.id}, Data: ${doc.data()}');
        }

        return snapshot.docs.map((doc) {
          try {
            final data = doc.data();
            // Convert Firestore Timestamp to DateTime
            DateTime? createdAt;
            if (data['createdAt'] is Timestamp) {
              createdAt = (data['createdAt'] as Timestamp).toDate();
            }

            DateTime? updatedAt;
            if (data['updatedAt'] is Timestamp) {
              updatedAt = (data['updatedAt'] as Timestamp).toDate();
            }

            DateTime? lastLoginAt;
            if (data['lastLoginAt'] is Timestamp) {
              lastLoginAt = (data['lastLoginAt'] as Timestamp).toDate();
            }

            DateTime? approvedAt;
            if (data['approvedAt'] is Timestamp) {
              approvedAt = (data['approvedAt'] as Timestamp).toDate();
            }

            // Create a modified data map with converted timestamps
            final modifiedData = {
              ...data,
              'id': doc.id,
              'createdAt': createdAt,
              'updatedAt': updatedAt,
              'lastLoginAt': lastLoginAt,
              'approvedAt': approvedAt,
            };

            return Seller.fromJson(modifiedData);
          } catch (e) {
            debugPrint('Error converting seller document: $e');
            // Return a basic seller object with minimal data to avoid breaking the UI
            return Seller(
              id: doc.id,
              businessName: doc.data()['businessName'] as String? ?? 'Unknown',
              email: doc.data()['email'] as String? ?? 'Unknown',
              isApproved: doc.data()['isApproved'] as bool? ?? false,
              isActive: doc.data()['isActive'] as bool? ?? false,
              isSuspended: doc.data()['isSuspended'] as bool? ?? false,
            );
          }
        }).toList();
      });
    } catch (e) {
      debugPrint('Error setting up sellers stream: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }

    // Listen to the stream
    _sellersStream.listen(
      (sellers) {
        setState(() {
          _sellers = sellers;
          _isLoading = false;
          _error = null;
        });
      },
      onError: (error) {
        setState(() {
          _error = error.toString();
          _isLoading = false;
        });
      },
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Implement pagination if needed
  }

  void _refreshSellers() {
    setState(() {
      _isLoading = true;
    });
    _setupSellersStream();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Seller Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _refreshSellers,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading && _sellers.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _sellers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_error',
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _refreshSellers,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_sellers.isEmpty) {
      return const Center(
        child: Text('No sellers found'),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshSellers();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(8),
        itemCount: _sellers.length,
        itemBuilder: (context, index) {
          final seller = _sellers[index];
          return SellerListItem(
            seller: seller,
            onUpdateStatus: (isActive, isSuspended) {
              _updateSellerStatus(seller, isActive, isSuspended);
            },
            onUpdatePerformance: (
              rating,
              totalReviews,
              totalOrders,
              totalProducts,
              totalRevenue,
            ) {
              _updateSellerPerformance(
                seller,
                rating,
                totalReviews,
                totalOrders,
                totalProducts,
                totalRevenue,
              );
            },
            onDelete: () {
              _deleteSeller(seller);
            },
          );
        },
      ),
    );
  }

  void _updateSellerStatus(Seller seller, bool isActive, bool isSuspended) async {
    try {
      await FirebaseFirestore.instance.collection('sellers').doc(seller.id).update({
        'isActive': isActive,
        'isSuspended': isSuspended,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller status updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating seller status: $e')),
        );
      }
    }
  }

  void _updateSellerPerformance(
    Seller seller,
    double rating,
    int totalReviews,
    int totalOrders,
    int totalProducts,
    double totalRevenue,
  ) async {
    try {
      await FirebaseFirestore.instance.collection('sellers').doc(seller.id).update({
        'rating': rating,
        'totalReviews': totalReviews,
        'totalOrders': totalOrders,
        'totalProducts': totalProducts,
        'totalRevenue': totalRevenue,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Seller performance updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating seller performance: $e')),
        );
      }
    }
  }

  void _deleteSeller(Seller seller) async {
    if (!mounted) return;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Seller'),
        content: Text('Are you sure you want to delete ${seller.businessName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await FirebaseFirestore.instance.collection('sellers').doc(seller.id).update({
          'isDeleted': true,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Seller deleted successfully')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error deleting seller: $e')),
          );
        }
      }
    }
  }
}
