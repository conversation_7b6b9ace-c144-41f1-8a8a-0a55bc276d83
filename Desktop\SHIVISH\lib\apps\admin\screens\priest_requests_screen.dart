import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth/auth_bloc.dart';

class PriestRequestsScreen extends StatefulWidget {
  const PriestRequestsScreen({super.key});

  @override
  State<PriestRequestsScreen> createState() => _PriestRequestsScreenState();
}

class _PriestRequestsScreenState extends State<PriestRequestsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  List<Map<String, dynamic>> _priestRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadPriestRequests();
  }

  Future<void> _loadPriestRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Query the priests collection for pending verification
      final snapshot = await _firestore
          .collection('priests')
          .where('verificationStatus', isEqualTo: 'pending')
          .get();

      final requests = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      setState(() {
        _priestRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading priest requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approvePriest(String priestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.uid;
        }
      }

      // First check if the priest document exists
      final priestDoc = await _firestore.collection('priests').doc(priestId).get();
      if (!priestDoc.exists) {
        throw Exception('Priest document not found');
      }

      // Get the priest data to find the associated user ID
      final priestData = priestDoc.data()!;
      final userId = priestData['userId'] ?? priestId; // Use the userId field if it exists, otherwise use priestId

      // Update priest document to approve
      await _firestore.collection('priests').doc(priestId).update({
        'verificationStatus': 'approved',
        'isVerified': true,
        'isActive': true,
        'isApproved': true,  // Add this field to match what the priest app checks for
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': adminId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Check if the user document exists before updating
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        await _firestore.collection('users').doc(userId).update({
          'isApproved': true,
          'role': 'priest', // Ensure the role is set correctly
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Log that the user document wasn't found but continue without error
        debugPrint('User document not found for priest $priestId with userId $userId');
      }

      // Reload the list
      await _loadPriestRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Priest approved successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving priest: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectPriest(String priestId) async {
    final reasonController = TextEditingController();

    if (!context.mounted) return;

    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Priest'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.uid;
        }
      }

      // First check if the priest document exists
      final priestDoc = await _firestore.collection('priests').doc(priestId).get();
      if (!priestDoc.exists) {
        throw Exception('Priest document not found');
      }

      // Get the priest data to find the associated user ID
      final priestData = priestDoc.data()!;
      final userId = priestData['userId'] ?? priestId; // Use the userId field if it exists, otherwise use priestId

      // Update priest document to reject
      await _firestore.collection('priests').doc(priestId).update({
        'verificationStatus': 'rejected',
        'isVerified': false,
        'isActive': false,
        'isApproved': false,  // Add this field to match what the priest app checks for
        'rejectionReason': reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': adminId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Check if the user document exists before updating
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists) {
        await _firestore.collection('users').doc(userId).update({
          'isApproved': false,
          'rejectionReason': reason,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      } else {
        // Log that the user document wasn't found but continue without error
        debugPrint('User document not found for priest $priestId with userId $userId');
      }

      // Reload the list
      await _loadPriestRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Priest rejected successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting priest: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Priest Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPriestRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadPriestRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _priestRequests.isEmpty
                  ? const Center(
                      child: Text('No pending priest requests'),
                    )
                  : ListView.builder(
                      itemCount: _priestRequests.length,
                      itemBuilder: (context, index) {
                        final request = _priestRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['name'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phone'] != null)
                                  Text('Phone: ${request['phone']}'),
                                const SizedBox(height: 8),
                                if (request['experienceYears'] != null)
                                  Text('Experience: ${request['experienceYears']} years'),
                                if (request['specializations'] != null && (request['specializations'] as List).isNotEmpty)
                                  Text('Specializations: ${(request['specializations'] as List).join(', ')}'),
                                if (request['serviceAreas'] != null && (request['serviceAreas'] as List).isNotEmpty)
                                  Text('Service Areas: ${(request['serviceAreas'] as List).join(', ')}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['createdAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                if (request['profileImage'] != null)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8.0),
                                      child: Image.network(
                                        request['profileImage'],
                                        height: 100,
                                        width: 100,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) {
                                          return const SizedBox(
                                            height: 100,
                                            width: 100,
                                            child: Icon(Icons.person, size: 50),
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectPriest(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approvePriest(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is Timestamp) {
      final date = timestamp.toDate();
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    }

    return 'N/A';
  }
}
