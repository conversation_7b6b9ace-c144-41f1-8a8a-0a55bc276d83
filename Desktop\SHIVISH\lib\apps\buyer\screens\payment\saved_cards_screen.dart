import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../../shared/models/payment/card_model.dart';
import '../../../../shared/models/payment/payment_model.dart';
import '../../../../shared/ui_components/buttons/app_button.dart';
import '../../providers/payment_provider.dart';

class SavedCardsScreen extends ConsumerStatefulWidget {
  final double amount;
  final String orderId;
  final String merchantId;

  const SavedCardsScreen({
    super.key,
    required this.amount,
    required this.orderId,
    required this.merchantId,
  });

  @override
  ConsumerState<SavedCardsScreen> createState() => _SavedCardsScreenState();
}

class _SavedCardsScreenState extends ConsumerState<SavedCardsScreen> {
  CardModel? _selectedCard;
  bool _isLoading = false;

  Future<void> _processPayment() async {
    if (_selectedCard == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Calculate tax and fee (example: 18% tax, 2% fee)
      final taxAmount = widget.amount * 0.18;
      final feeAmount = widget.amount * 0.02;

      await ref.read(paymentProvider.notifier).createPayment(
            orderId: widget.orderId,
            merchantId: widget.merchantId,
            type: PaymentType.purchase,
            method: PaymentMethod.card,
            gateway: PaymentGateway.phonepe,
            amount: widget.amount,
            taxAmount: taxAmount,
            feeAmount: feeAmount,
            card: _selectedCard,
          );

      if (mounted) {
        context.pop(true); // Return success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final cards = ref.watch(paymentProvider).cards;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved Cards'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.push('/payment/add-card');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: cards.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.credit_card,
                          size: 64,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No saved cards',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        TextButton(
                          onPressed: () {
                            context.push('/payment/add-card');
                          },
                          child: const Text('Add a card'),
                        ),
                      ],
                    ),
                  )
                : ListView.separated(
                    padding: const EdgeInsets.all(16),
                    itemCount: cards.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                    itemBuilder: (context, index) {
                      final card = cards[index];
                      return _SavedCardItem(
                        card: card,
                        isSelected: card == _selectedCard,
                        onTap: () {
                          setState(() {
                            _selectedCard = card;
                          });
                        },
                        onDelete: () async {
                          final shouldDelete = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Delete Card'),
                              content: const Text(
                                'Are you sure you want to delete this card?',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => context.pop(false),
                                  child: const Text('Cancel'),
                                ),
                                TextButton(
                                  onPressed: () => context.pop(true),
                                  child: Text(
                                    'Delete',
                                    style: TextStyle(
                                      color:
                                          Theme.of(context).colorScheme.error,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );

                          if (shouldDelete ?? false) {
                            await ref
                                .read(paymentProvider.notifier)
                                .deleteCard(card.id);
                          }
                        },
                      );
                    },
                  ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: AppButton(
              onPressed: _selectedCard != null ? _processPayment : null,
              isLoading: _isLoading,
              child: Text('Pay ₹${widget.amount.toStringAsFixed(2)}'),
            ),
          ),
        ],
      ),
    );
  }
}

class _SavedCardItem extends StatelessWidget {
  final CardModel card;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const _SavedCardItem({
    required this.card,
    required this.isSelected,
    required this.onTap,
    required this.onDelete,
  });

  IconData _getCardIcon() {
    switch (card.brand.toLowerCase()) {
      case 'visa':
        return Icons.credit_card;
      case 'mastercard':
        return Icons.credit_card;
      case 'amex':
        return Icons.credit_card;
      default:
        return Icons.credit_card;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected
          ? Theme.of(context).colorScheme.primaryContainer
          : Theme.of(context).colorScheme.surface,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                _getCardIcon(),
                size: 32,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '•••• ${card.last4}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Expires ${card.expiryMonth}/${card.expiryYear}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                          ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: const Icon(Icons.delete_outline),
                onPressed: onDelete,
                color: Theme.of(context).colorScheme.error,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
