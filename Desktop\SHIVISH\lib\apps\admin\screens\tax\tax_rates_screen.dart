import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/utils/gst_utils.dart';
import '../../../../shared/services/tax/tax_update_service.dart';

/// Screen for managing tax rates
class TaxRatesScreen extends ConsumerStatefulWidget {
  /// Creates a [TaxRatesScreen]
  const TaxRatesScreen({super.key});

  @override
  ConsumerState<TaxRatesScreen> createState() => _TaxRatesScreenState();
}

class _TaxRatesScreenState extends ConsumerState<TaxRatesScreen> {
  bool _isLoading = false;
  bool _isRefreshing = false;
  Map<String, double> _gstRates = {};
  final TaxUpdateService _taxUpdateService = TaxUpdateService();
  DateTime? _lastUpdateTime;

  @override
  void initState() {
    super.initState();
    _loadTaxRates();
  }

  Future<void> _loadTaxRates() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize tax update service
      await _taxUpdateService.initialize();
      
      // Get current GST rates
      _gstRates = GstUtils.getCurrentRates();
      
      // Get last update time
      _lastUpdateTime = _taxUpdateService.getLastCheckTime();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading tax rates: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _refreshTaxRates() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      // Force refresh GST rates
      final success = await GstUtils.forceRefreshRates();
      
      if (success) {
        // Update rates in state
        _gstRates = GstUtils.getCurrentRates();
        
        // Update last update time
        _lastUpdateTime = DateTime.now();
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tax rates updated successfully')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to update tax rates')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error refreshing tax rates: $e')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tax Rates'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isRefreshing ? null : _refreshTaxRates,
            tooltip: 'Refresh Tax Rates',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // Last update info
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const Icon(Icons.update, size: 20),
              const SizedBox(width: 8),
              Text(
                'Last updated: ${_lastUpdateTime != null ? _formatDateTime(_lastUpdateTime!) : 'Never'}',
                style: const TextStyle(
                  fontStyle: FontStyle.italic,
                  color: Colors.grey,
                ),
              ),
              const Spacer(),
              if (_isRefreshing) 
                const SizedBox(
                  width: 20, 
                  height: 20, 
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
        ),
        
        // Tax rates list
        Expanded(
          child: _buildTaxRatesList(),
        ),
      ],
    );
  }

  Widget _buildTaxRatesList() {
    // Group rates by value
    final Map<double, List<String>> rateGroups = {};
    
    for (final entry in _gstRates.entries) {
      if (!rateGroups.containsKey(entry.value)) {
        rateGroups[entry.value] = [];
      }
      rateGroups[entry.value]!.add(entry.key);
    }
    
    // Sort rates
    final sortedRates = rateGroups.keys.toList()..sort();
    
    return ListView.builder(
      itemCount: sortedRates.length,
      itemBuilder: (context, index) {
        final rate = sortedRates[index];
        final categories = rateGroups[rate]!;
        
        return _buildRateSection(rate, categories);
      },
    );
  }

  Widget _buildRateSection(double rate, List<String> categories) {
    // Sort categories alphabetically
    categories.sort();
    
    return ExpansionTile(
      title: Text(
        '${(rate * 100).toStringAsFixed(0)}% GST Rate',
        style: const TextStyle(
          fontWeight: FontWeight.bold,
        ),
      ),
      subtitle: Text('${categories.length} categories'),
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: categories.map((category) {
              return Chip(
                label: Text(_formatCategoryName(category)),
                backgroundColor: _getCategoryColor(rate),
              );
            }).toList(),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  String _formatCategoryName(String category) {
    // Convert snake_case to Title Case
    return category
        .split('_')
        .map((word) => word.isEmpty ? '' : '${word[0].toUpperCase()}${word.substring(1)}')
        .join(' ');
  }

  Color _getCategoryColor(double rate) {
    if (rate == 0.0) return Colors.green[100]!;
    if (rate == 0.05) return Colors.blue[100]!;
    if (rate == 0.12) return Colors.purple[100]!;
    if (rate == 0.18) return Colors.orange[100]!;
    if (rate == 0.28) return Colors.red[100]!;
    return Colors.grey[200]!;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
