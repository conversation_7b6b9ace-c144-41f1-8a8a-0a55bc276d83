// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ProductEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductEventCopyWith<$Res> {
  factory $ProductEventCopyWith(
          ProductEvent value, $Res Function(ProductEvent) then) =
      _$ProductEventCopyWithImpl<$Res, ProductEvent>;
}

/// @nodoc
class _$ProductEventCopyWithImpl<$Res, $Val extends ProductEvent>
    implements $ProductEventCopyWith<$Res> {
  _$ProductEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadProductsImplCopyWith<$Res> {
  factory _$$LoadProductsImplCopyWith(
          _$LoadProductsImpl value, $Res Function(_$LoadProductsImpl) then) =
      __$$LoadProductsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadProductsImplCopyWithImpl<$Res>
    extends _$ProductEventCopyWithImpl<$Res, _$LoadProductsImpl>
    implements _$$LoadProductsImplCopyWith<$Res> {
  __$$LoadProductsImplCopyWithImpl(
      _$LoadProductsImpl _value, $Res Function(_$LoadProductsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadProductsImpl implements _LoadProducts {
  const _$LoadProductsImpl();

  @override
  String toString() {
    return 'ProductEvent.loadProducts()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadProductsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return loadProducts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return loadProducts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadProducts != null) {
      return loadProducts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return loadProducts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return loadProducts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadProducts != null) {
      return loadProducts(this);
    }
    return orElse();
  }
}

abstract class _LoadProducts implements ProductEvent {
  const factory _LoadProducts() = _$LoadProductsImpl;
}

/// @nodoc
abstract class _$$LoadMoreProductsImplCopyWith<$Res> {
  factory _$$LoadMoreProductsImplCopyWith(_$LoadMoreProductsImpl value,
          $Res Function(_$LoadMoreProductsImpl) then) =
      __$$LoadMoreProductsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreProductsImplCopyWithImpl<$Res>
    extends _$ProductEventCopyWithImpl<$Res, _$LoadMoreProductsImpl>
    implements _$$LoadMoreProductsImplCopyWith<$Res> {
  __$$LoadMoreProductsImplCopyWithImpl(_$LoadMoreProductsImpl _value,
      $Res Function(_$LoadMoreProductsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreProductsImpl implements _LoadMoreProducts {
  const _$LoadMoreProductsImpl();

  @override
  String toString() {
    return 'ProductEvent.loadMoreProducts()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreProductsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return loadMoreProducts();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return loadMoreProducts?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadMoreProducts != null) {
      return loadMoreProducts();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return loadMoreProducts(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return loadMoreProducts?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (loadMoreProducts != null) {
      return loadMoreProducts(this);
    }
    return orElse();
  }
}

abstract class _LoadMoreProducts implements ProductEvent {
  const factory _LoadMoreProducts() = _$LoadMoreProductsImpl;
}

/// @nodoc
abstract class _$$UpdateProductStatusImplCopyWith<$Res> {
  factory _$$UpdateProductStatusImplCopyWith(_$UpdateProductStatusImpl value,
          $Res Function(_$UpdateProductStatusImpl) then) =
      __$$UpdateProductStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {ProductModel product, String status, String? notes, bool isActive});

  $ProductModelCopyWith<$Res> get product;
}

/// @nodoc
class __$$UpdateProductStatusImplCopyWithImpl<$Res>
    extends _$ProductEventCopyWithImpl<$Res, _$UpdateProductStatusImpl>
    implements _$$UpdateProductStatusImplCopyWith<$Res> {
  __$$UpdateProductStatusImplCopyWithImpl(_$UpdateProductStatusImpl _value,
      $Res Function(_$UpdateProductStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? product = null,
    Object? status = null,
    Object? notes = freezed,
    Object? isActive = null,
  }) {
    return _then(_$UpdateProductStatusImpl(
      null == product
          ? _value.product
          : product // ignore: cast_nullable_to_non_nullable
              as ProductModel,
      null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      freezed == notes
          ? _value.notes
          : notes // ignore: cast_nullable_to_non_nullable
              as String?,
      null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductModelCopyWith<$Res> get product {
    return $ProductModelCopyWith<$Res>(_value.product, (value) {
      return _then(_value.copyWith(product: value));
    });
  }
}

/// @nodoc

class _$UpdateProductStatusImpl implements _UpdateProductStatus {
  const _$UpdateProductStatusImpl(
      this.product, this.status, this.notes, this.isActive);

  @override
  final ProductModel product;
  @override
  final String status;
  @override
  final String? notes;
  @override
  final bool isActive;

  @override
  String toString() {
    return 'ProductEvent.updateProductStatus(product: $product, status: $status, notes: $notes, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateProductStatusImpl &&
            (identical(other.product, product) || other.product == product) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.notes, notes) || other.notes == notes) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, product, status, notes, isActive);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateProductStatusImplCopyWith<_$UpdateProductStatusImpl> get copyWith =>
      __$$UpdateProductStatusImplCopyWithImpl<_$UpdateProductStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return updateProductStatus(product, status, notes, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return updateProductStatus?.call(product, status, notes, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateProductStatus != null) {
      return updateProductStatus(product, status, notes, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return updateProductStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return updateProductStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (updateProductStatus != null) {
      return updateProductStatus(this);
    }
    return orElse();
  }
}

abstract class _UpdateProductStatus implements ProductEvent {
  const factory _UpdateProductStatus(
      final ProductModel product,
      final String status,
      final String? notes,
      final bool isActive) = _$UpdateProductStatusImpl;

  ProductModel get product;
  String get status;
  String? get notes;
  bool get isActive;

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateProductStatusImplCopyWith<_$UpdateProductStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteProductImplCopyWith<$Res> {
  factory _$$DeleteProductImplCopyWith(
          _$DeleteProductImpl value, $Res Function(_$DeleteProductImpl) then) =
      __$$DeleteProductImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteProductImplCopyWithImpl<$Res>
    extends _$ProductEventCopyWithImpl<$Res, _$DeleteProductImpl>
    implements _$$DeleteProductImplCopyWith<$Res> {
  __$$DeleteProductImplCopyWithImpl(
      _$DeleteProductImpl _value, $Res Function(_$DeleteProductImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteProductImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteProductImpl implements _DeleteProduct {
  const _$DeleteProductImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'ProductEvent.deleteProduct(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteProductImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteProductImplCopyWith<_$DeleteProductImpl> get copyWith =>
      __$$DeleteProductImplCopyWithImpl<_$DeleteProductImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return deleteProduct(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return deleteProduct?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (deleteProduct != null) {
      return deleteProduct(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return deleteProduct(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return deleteProduct?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (deleteProduct != null) {
      return deleteProduct(this);
    }
    return orElse();
  }
}

abstract class _DeleteProduct implements ProductEvent {
  const factory _DeleteProduct(final String id) = _$DeleteProductImpl;

  String get id;

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteProductImplCopyWith<_$DeleteProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApplyFiltersImplCopyWith<$Res> {
  factory _$$ApplyFiltersImplCopyWith(
          _$ApplyFiltersImpl value, $Res Function(_$ApplyFiltersImpl) then) =
      __$$ApplyFiltersImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<String, dynamic> filters});
}

/// @nodoc
class __$$ApplyFiltersImplCopyWithImpl<$Res>
    extends _$ProductEventCopyWithImpl<$Res, _$ApplyFiltersImpl>
    implements _$$ApplyFiltersImplCopyWith<$Res> {
  __$$ApplyFiltersImplCopyWithImpl(
      _$ApplyFiltersImpl _value, $Res Function(_$ApplyFiltersImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filters = null,
  }) {
    return _then(_$ApplyFiltersImpl(
      null == filters
          ? _value._filters
          : filters // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$ApplyFiltersImpl implements _ApplyFilters {
  const _$ApplyFiltersImpl(final Map<String, dynamic> filters)
      : _filters = filters;

  final Map<String, dynamic> _filters;
  @override
  Map<String, dynamic> get filters {
    if (_filters is EqualUnmodifiableMapView) return _filters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_filters);
  }

  @override
  String toString() {
    return 'ProductEvent.applyFilters(filters: $filters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApplyFiltersImpl &&
            const DeepCollectionEquality().equals(other._filters, _filters));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_filters));

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      __$$ApplyFiltersImplCopyWithImpl<_$ApplyFiltersImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadProducts,
    required TResult Function() loadMoreProducts,
    required TResult Function(
            ProductModel product, String status, String? notes, bool isActive)
        updateProductStatus,
    required TResult Function(String id) deleteProduct,
    required TResult Function(Map<String, dynamic> filters) applyFilters,
  }) {
    return applyFilters(filters);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadProducts,
    TResult? Function()? loadMoreProducts,
    TResult? Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult? Function(String id)? deleteProduct,
    TResult? Function(Map<String, dynamic> filters)? applyFilters,
  }) {
    return applyFilters?.call(filters);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadProducts,
    TResult Function()? loadMoreProducts,
    TResult Function(
            ProductModel product, String status, String? notes, bool isActive)?
        updateProductStatus,
    TResult Function(String id)? deleteProduct,
    TResult Function(Map<String, dynamic> filters)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(filters);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadProducts value) loadProducts,
    required TResult Function(_LoadMoreProducts value) loadMoreProducts,
    required TResult Function(_UpdateProductStatus value) updateProductStatus,
    required TResult Function(_DeleteProduct value) deleteProduct,
    required TResult Function(_ApplyFilters value) applyFilters,
  }) {
    return applyFilters(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadProducts value)? loadProducts,
    TResult? Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult? Function(_UpdateProductStatus value)? updateProductStatus,
    TResult? Function(_DeleteProduct value)? deleteProduct,
    TResult? Function(_ApplyFilters value)? applyFilters,
  }) {
    return applyFilters?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadProducts value)? loadProducts,
    TResult Function(_LoadMoreProducts value)? loadMoreProducts,
    TResult Function(_UpdateProductStatus value)? updateProductStatus,
    TResult Function(_DeleteProduct value)? deleteProduct,
    TResult Function(_ApplyFilters value)? applyFilters,
    required TResult orElse(),
  }) {
    if (applyFilters != null) {
      return applyFilters(this);
    }
    return orElse();
  }
}

abstract class _ApplyFilters implements ProductEvent {
  const factory _ApplyFilters(final Map<String, dynamic> filters) =
      _$ApplyFiltersImpl;

  Map<String, dynamic> get filters;

  /// Create a copy of ProductEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApplyFiltersImplCopyWith<_$ApplyFiltersImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
