// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'refund_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$RefundEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RefundEventCopyWith<$Res> {
  factory $RefundEventCopyWith(
          RefundEvent value, $Res Function(RefundEvent) then) =
      _$RefundEventCopyWithImpl<$Res, RefundEvent>;
}

/// @nodoc
class _$RefundEventCopyWithImpl<$Res, $Val extends RefundEvent>
    implements $RefundEventCopyWith<$Res> {
  _$RefundEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadRefundsImplCopyWith<$Res> {
  factory _$$LoadRefundsImplCopyWith(
          _$LoadRefundsImpl value, $Res Function(_$LoadRefundsImpl) then) =
      __$$LoadRefundsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadRefundsImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$LoadRefundsImpl>
    implements _$$LoadRefundsImplCopyWith<$Res> {
  __$$LoadRefundsImplCopyWithImpl(
      _$LoadRefundsImpl _value, $Res Function(_$LoadRefundsImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadRefundsImpl implements LoadRefunds {
  const _$LoadRefundsImpl();

  @override
  String toString() {
    return 'RefundEvent.loadRefunds()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadRefundsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return loadRefunds();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return loadRefunds?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (loadRefunds != null) {
      return loadRefunds();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return loadRefunds(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return loadRefunds?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (loadRefunds != null) {
      return loadRefunds(this);
    }
    return orElse();
  }
}

abstract class LoadRefunds implements RefundEvent {
  const factory LoadRefunds() = _$LoadRefundsImpl;
}

/// @nodoc
abstract class _$$CreateRefundImplCopyWith<$Res> {
  factory _$$CreateRefundImplCopyWith(
          _$CreateRefundImpl value, $Res Function(_$CreateRefundImpl) then) =
      __$$CreateRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Refund refund});

  $RefundCopyWith<$Res> get refund;
}

/// @nodoc
class __$$CreateRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$CreateRefundImpl>
    implements _$$CreateRefundImplCopyWith<$Res> {
  __$$CreateRefundImplCopyWithImpl(
      _$CreateRefundImpl _value, $Res Function(_$CreateRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refund = null,
  }) {
    return _then(_$CreateRefundImpl(
      null == refund
          ? _value.refund
          : refund // ignore: cast_nullable_to_non_nullable
              as Refund,
    ));
  }

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RefundCopyWith<$Res> get refund {
    return $RefundCopyWith<$Res>(_value.refund, (value) {
      return _then(_value.copyWith(refund: value));
    });
  }
}

/// @nodoc

class _$CreateRefundImpl implements CreateRefund {
  const _$CreateRefundImpl(this.refund);

  @override
  final Refund refund;

  @override
  String toString() {
    return 'RefundEvent.createRefund(refund: $refund)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateRefundImpl &&
            (identical(other.refund, refund) || other.refund == refund));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refund);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateRefundImplCopyWith<_$CreateRefundImpl> get copyWith =>
      __$$CreateRefundImplCopyWithImpl<_$CreateRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return createRefund(refund);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return createRefund?.call(refund);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (createRefund != null) {
      return createRefund(refund);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return createRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return createRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (createRefund != null) {
      return createRefund(this);
    }
    return orElse();
  }
}

abstract class CreateRefund implements RefundEvent {
  const factory CreateRefund(final Refund refund) = _$CreateRefundImpl;

  Refund get refund;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateRefundImplCopyWith<_$CreateRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateRefundImplCopyWith<$Res> {
  factory _$$UpdateRefundImplCopyWith(
          _$UpdateRefundImpl value, $Res Function(_$UpdateRefundImpl) then) =
      __$$UpdateRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Refund refund});

  $RefundCopyWith<$Res> get refund;
}

/// @nodoc
class __$$UpdateRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$UpdateRefundImpl>
    implements _$$UpdateRefundImplCopyWith<$Res> {
  __$$UpdateRefundImplCopyWithImpl(
      _$UpdateRefundImpl _value, $Res Function(_$UpdateRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? refund = null,
  }) {
    return _then(_$UpdateRefundImpl(
      null == refund
          ? _value.refund
          : refund // ignore: cast_nullable_to_non_nullable
              as Refund,
    ));
  }

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RefundCopyWith<$Res> get refund {
    return $RefundCopyWith<$Res>(_value.refund, (value) {
      return _then(_value.copyWith(refund: value));
    });
  }
}

/// @nodoc

class _$UpdateRefundImpl implements UpdateRefund {
  const _$UpdateRefundImpl(this.refund);

  @override
  final Refund refund;

  @override
  String toString() {
    return 'RefundEvent.updateRefund(refund: $refund)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateRefundImpl &&
            (identical(other.refund, refund) || other.refund == refund));
  }

  @override
  int get hashCode => Object.hash(runtimeType, refund);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateRefundImplCopyWith<_$UpdateRefundImpl> get copyWith =>
      __$$UpdateRefundImplCopyWithImpl<_$UpdateRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return updateRefund(refund);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return updateRefund?.call(refund);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (updateRefund != null) {
      return updateRefund(refund);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return updateRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return updateRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (updateRefund != null) {
      return updateRefund(this);
    }
    return orElse();
  }
}

abstract class UpdateRefund implements RefundEvent {
  const factory UpdateRefund(final Refund refund) = _$UpdateRefundImpl;

  Refund get refund;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateRefundImplCopyWith<_$UpdateRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteRefundImplCopyWith<$Res> {
  factory _$$DeleteRefundImplCopyWith(
          _$DeleteRefundImpl value, $Res Function(_$DeleteRefundImpl) then) =
      __$$DeleteRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$DeleteRefundImpl>
    implements _$$DeleteRefundImplCopyWith<$Res> {
  __$$DeleteRefundImplCopyWithImpl(
      _$DeleteRefundImpl _value, $Res Function(_$DeleteRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteRefundImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteRefundImpl implements DeleteRefund {
  const _$DeleteRefundImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'RefundEvent.deleteRefund(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteRefundImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteRefundImplCopyWith<_$DeleteRefundImpl> get copyWith =>
      __$$DeleteRefundImplCopyWithImpl<_$DeleteRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return deleteRefund(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return deleteRefund?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (deleteRefund != null) {
      return deleteRefund(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return deleteRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return deleteRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (deleteRefund != null) {
      return deleteRefund(this);
    }
    return orElse();
  }
}

abstract class DeleteRefund implements RefundEvent {
  const factory DeleteRefund(final String id) = _$DeleteRefundImpl;

  String get id;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteRefundImplCopyWith<_$DeleteRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ApproveRefundImplCopyWith<$Res> {
  factory _$$ApproveRefundImplCopyWith(
          _$ApproveRefundImpl value, $Res Function(_$ApproveRefundImpl) then) =
      __$$ApproveRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String approvedBy});
}

/// @nodoc
class __$$ApproveRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$ApproveRefundImpl>
    implements _$$ApproveRefundImplCopyWith<$Res> {
  __$$ApproveRefundImplCopyWithImpl(
      _$ApproveRefundImpl _value, $Res Function(_$ApproveRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? approvedBy = null,
  }) {
    return _then(_$ApproveRefundImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == approvedBy
          ? _value.approvedBy
          : approvedBy // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ApproveRefundImpl implements ApproveRefund {
  const _$ApproveRefundImpl(this.id, this.approvedBy);

  @override
  final String id;
  @override
  final String approvedBy;

  @override
  String toString() {
    return 'RefundEvent.approveRefund(id: $id, approvedBy: $approvedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApproveRefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.approvedBy, approvedBy) ||
                other.approvedBy == approvedBy));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, approvedBy);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApproveRefundImplCopyWith<_$ApproveRefundImpl> get copyWith =>
      __$$ApproveRefundImplCopyWithImpl<_$ApproveRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return approveRefund(id, approvedBy);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return approveRefund?.call(id, approvedBy);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (approveRefund != null) {
      return approveRefund(id, approvedBy);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return approveRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return approveRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (approveRefund != null) {
      return approveRefund(this);
    }
    return orElse();
  }
}

abstract class ApproveRefund implements RefundEvent {
  const factory ApproveRefund(final String id, final String approvedBy) =
      _$ApproveRefundImpl;

  String get id;
  String get approvedBy;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApproveRefundImplCopyWith<_$ApproveRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RejectRefundImplCopyWith<$Res> {
  factory _$$RejectRefundImplCopyWith(
          _$RejectRefundImpl value, $Res Function(_$RejectRefundImpl) then) =
      __$$RejectRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String rejectedBy, String rejectionReason});
}

/// @nodoc
class __$$RejectRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$RejectRefundImpl>
    implements _$$RejectRefundImplCopyWith<$Res> {
  __$$RejectRefundImplCopyWithImpl(
      _$RejectRefundImpl _value, $Res Function(_$RejectRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? rejectedBy = null,
    Object? rejectionReason = null,
  }) {
    return _then(_$RejectRefundImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == rejectedBy
          ? _value.rejectedBy
          : rejectedBy // ignore: cast_nullable_to_non_nullable
              as String,
      null == rejectionReason
          ? _value.rejectionReason
          : rejectionReason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RejectRefundImpl implements RejectRefund {
  const _$RejectRefundImpl(this.id, this.rejectedBy, this.rejectionReason);

  @override
  final String id;
  @override
  final String rejectedBy;
  @override
  final String rejectionReason;

  @override
  String toString() {
    return 'RefundEvent.rejectRefund(id: $id, rejectedBy: $rejectedBy, rejectionReason: $rejectionReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RejectRefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.rejectedBy, rejectedBy) ||
                other.rejectedBy == rejectedBy) &&
            (identical(other.rejectionReason, rejectionReason) ||
                other.rejectionReason == rejectionReason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, rejectedBy, rejectionReason);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RejectRefundImplCopyWith<_$RejectRefundImpl> get copyWith =>
      __$$RejectRefundImplCopyWithImpl<_$RejectRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return rejectRefund(id, rejectedBy, rejectionReason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return rejectRefund?.call(id, rejectedBy, rejectionReason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (rejectRefund != null) {
      return rejectRefund(id, rejectedBy, rejectionReason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return rejectRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return rejectRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (rejectRefund != null) {
      return rejectRefund(this);
    }
    return orElse();
  }
}

abstract class RejectRefund implements RefundEvent {
  const factory RejectRefund(final String id, final String rejectedBy,
      final String rejectionReason) = _$RejectRefundImpl;

  String get id;
  String get rejectedBy;
  String get rejectionReason;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RejectRefundImplCopyWith<_$RejectRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProcessRefundImplCopyWith<$Res> {
  factory _$$ProcessRefundImplCopyWith(
          _$ProcessRefundImpl value, $Res Function(_$ProcessRefundImpl) then) =
      __$$ProcessRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String processedBy, String transactionId});
}

/// @nodoc
class __$$ProcessRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$ProcessRefundImpl>
    implements _$$ProcessRefundImplCopyWith<$Res> {
  __$$ProcessRefundImplCopyWithImpl(
      _$ProcessRefundImpl _value, $Res Function(_$ProcessRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? processedBy = null,
    Object? transactionId = null,
  }) {
    return _then(_$ProcessRefundImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == processedBy
          ? _value.processedBy
          : processedBy // ignore: cast_nullable_to_non_nullable
              as String,
      null == transactionId
          ? _value.transactionId
          : transactionId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ProcessRefundImpl implements ProcessRefund {
  const _$ProcessRefundImpl(this.id, this.processedBy, this.transactionId);

  @override
  final String id;
  @override
  final String processedBy;
  @override
  final String transactionId;

  @override
  String toString() {
    return 'RefundEvent.processRefund(id: $id, processedBy: $processedBy, transactionId: $transactionId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProcessRefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.processedBy, processedBy) ||
                other.processedBy == processedBy) &&
            (identical(other.transactionId, transactionId) ||
                other.transactionId == transactionId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, processedBy, transactionId);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProcessRefundImplCopyWith<_$ProcessRefundImpl> get copyWith =>
      __$$ProcessRefundImplCopyWithImpl<_$ProcessRefundImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return processRefund(id, processedBy, transactionId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return processRefund?.call(id, processedBy, transactionId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (processRefund != null) {
      return processRefund(id, processedBy, transactionId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return processRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return processRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (processRefund != null) {
      return processRefund(this);
    }
    return orElse();
  }
}

abstract class ProcessRefund implements RefundEvent {
  const factory ProcessRefund(final String id, final String processedBy,
      final String transactionId) = _$ProcessRefundImpl;

  String get id;
  String get processedBy;
  String get transactionId;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProcessRefundImplCopyWith<_$ProcessRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateDisputeImplCopyWith<$Res> {
  factory _$$CreateDisputeImplCopyWith(
          _$CreateDisputeImpl value, $Res Function(_$CreateDisputeImpl) then) =
      __$$CreateDisputeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String disputeReason});
}

/// @nodoc
class __$$CreateDisputeImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$CreateDisputeImpl>
    implements _$$CreateDisputeImplCopyWith<$Res> {
  __$$CreateDisputeImplCopyWithImpl(
      _$CreateDisputeImpl _value, $Res Function(_$CreateDisputeImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? disputeReason = null,
  }) {
    return _then(_$CreateDisputeImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == disputeReason
          ? _value.disputeReason
          : disputeReason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CreateDisputeImpl implements CreateDispute {
  const _$CreateDisputeImpl(this.id, this.disputeReason);

  @override
  final String id;
  @override
  final String disputeReason;

  @override
  String toString() {
    return 'RefundEvent.createDispute(id: $id, disputeReason: $disputeReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateDisputeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.disputeReason, disputeReason) ||
                other.disputeReason == disputeReason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, disputeReason);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateDisputeImplCopyWith<_$CreateDisputeImpl> get copyWith =>
      __$$CreateDisputeImplCopyWithImpl<_$CreateDisputeImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return createDispute(id, disputeReason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return createDispute?.call(id, disputeReason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (createDispute != null) {
      return createDispute(id, disputeReason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return createDispute(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return createDispute?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (createDispute != null) {
      return createDispute(this);
    }
    return orElse();
  }
}

abstract class CreateDispute implements RefundEvent {
  const factory CreateDispute(final String id, final String disputeReason) =
      _$CreateDisputeImpl;

  String get id;
  String get disputeReason;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateDisputeImplCopyWith<_$CreateDisputeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ResolveDisputeImplCopyWith<$Res> {
  factory _$$ResolveDisputeImplCopyWith(_$ResolveDisputeImpl value,
          $Res Function(_$ResolveDisputeImpl) then) =
      __$$ResolveDisputeImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, String resolvedBy, String resolution});
}

/// @nodoc
class __$$ResolveDisputeImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$ResolveDisputeImpl>
    implements _$$ResolveDisputeImplCopyWith<$Res> {
  __$$ResolveDisputeImplCopyWithImpl(
      _$ResolveDisputeImpl _value, $Res Function(_$ResolveDisputeImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? resolvedBy = null,
    Object? resolution = null,
  }) {
    return _then(_$ResolveDisputeImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == resolvedBy
          ? _value.resolvedBy
          : resolvedBy // ignore: cast_nullable_to_non_nullable
              as String,
      null == resolution
          ? _value.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ResolveDisputeImpl implements ResolveDispute {
  const _$ResolveDisputeImpl(this.id, this.resolvedBy, this.resolution);

  @override
  final String id;
  @override
  final String resolvedBy;
  @override
  final String resolution;

  @override
  String toString() {
    return 'RefundEvent.resolveDispute(id: $id, resolvedBy: $resolvedBy, resolution: $resolution)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ResolveDisputeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.resolvedBy, resolvedBy) ||
                other.resolvedBy == resolvedBy) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, resolvedBy, resolution);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ResolveDisputeImplCopyWith<_$ResolveDisputeImpl> get copyWith =>
      __$$ResolveDisputeImplCopyWithImpl<_$ResolveDisputeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return resolveDispute(id, resolvedBy, resolution);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return resolveDispute?.call(id, resolvedBy, resolution);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (resolveDispute != null) {
      return resolveDispute(id, resolvedBy, resolution);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return resolveDispute(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return resolveDispute?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (resolveDispute != null) {
      return resolveDispute(this);
    }
    return orElse();
  }
}

abstract class ResolveDispute implements RefundEvent {
  const factory ResolveDispute(
          final String id, final String resolvedBy, final String resolution) =
      _$ResolveDisputeImpl;

  String get id;
  String get resolvedBy;
  String get resolution;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ResolveDisputeImplCopyWith<_$ResolveDisputeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdatePartialRefundImplCopyWith<$Res> {
  factory _$$UpdatePartialRefundImplCopyWith(_$UpdatePartialRefundImpl value,
          $Res Function(_$UpdatePartialRefundImpl) then) =
      __$$UpdatePartialRefundImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, double partialAmount, String partialReason});
}

/// @nodoc
class __$$UpdatePartialRefundImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$UpdatePartialRefundImpl>
    implements _$$UpdatePartialRefundImplCopyWith<$Res> {
  __$$UpdatePartialRefundImplCopyWithImpl(_$UpdatePartialRefundImpl _value,
      $Res Function(_$UpdatePartialRefundImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? partialAmount = null,
    Object? partialReason = null,
  }) {
    return _then(_$UpdatePartialRefundImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == partialAmount
          ? _value.partialAmount
          : partialAmount // ignore: cast_nullable_to_non_nullable
              as double,
      null == partialReason
          ? _value.partialReason
          : partialReason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$UpdatePartialRefundImpl implements UpdatePartialRefund {
  const _$UpdatePartialRefundImpl(
      this.id, this.partialAmount, this.partialReason);

  @override
  final String id;
  @override
  final double partialAmount;
  @override
  final String partialReason;

  @override
  String toString() {
    return 'RefundEvent.updatePartialRefund(id: $id, partialAmount: $partialAmount, partialReason: $partialReason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdatePartialRefundImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.partialAmount, partialAmount) ||
                other.partialAmount == partialAmount) &&
            (identical(other.partialReason, partialReason) ||
                other.partialReason == partialReason));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, id, partialAmount, partialReason);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdatePartialRefundImplCopyWith<_$UpdatePartialRefundImpl> get copyWith =>
      __$$UpdatePartialRefundImplCopyWithImpl<_$UpdatePartialRefundImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return updatePartialRefund(id, partialAmount, partialReason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return updatePartialRefund?.call(id, partialAmount, partialReason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (updatePartialRefund != null) {
      return updatePartialRefund(id, partialAmount, partialReason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return updatePartialRefund(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return updatePartialRefund?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (updatePartialRefund != null) {
      return updatePartialRefund(this);
    }
    return orElse();
  }
}

abstract class UpdatePartialRefund implements RefundEvent {
  const factory UpdatePartialRefund(final String id, final double partialAmount,
      final String partialReason) = _$UpdatePartialRefundImpl;

  String get id;
  double get partialAmount;
  String get partialReason;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdatePartialRefundImplCopyWith<_$UpdatePartialRefundImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateRefundMethodImplCopyWith<$Res> {
  factory _$$UpdateRefundMethodImplCopyWith(_$UpdateRefundMethodImpl value,
          $Res Function(_$UpdateRefundMethodImpl) then) =
      __$$UpdateRefundMethodImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> method});
}

/// @nodoc
class __$$UpdateRefundMethodImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$UpdateRefundMethodImpl>
    implements _$$UpdateRefundMethodImplCopyWith<$Res> {
  __$$UpdateRefundMethodImplCopyWithImpl(_$UpdateRefundMethodImpl _value,
      $Res Function(_$UpdateRefundMethodImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? method = null,
  }) {
    return _then(_$UpdateRefundMethodImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == method
          ? _value._method
          : method // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateRefundMethodImpl implements UpdateRefundMethod {
  const _$UpdateRefundMethodImpl(this.id, final Map<String, dynamic> method)
      : _method = method;

  @override
  final String id;
  final Map<String, dynamic> _method;
  @override
  Map<String, dynamic> get method {
    if (_method is EqualUnmodifiableMapView) return _method;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_method);
  }

  @override
  String toString() {
    return 'RefundEvent.updateRefundMethod(id: $id, method: $method)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateRefundMethodImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._method, _method));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_method));

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateRefundMethodImplCopyWith<_$UpdateRefundMethodImpl> get copyWith =>
      __$$UpdateRefundMethodImplCopyWithImpl<_$UpdateRefundMethodImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return updateRefundMethod(id, method);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return updateRefundMethod?.call(id, method);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (updateRefundMethod != null) {
      return updateRefundMethod(id, method);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return updateRefundMethod(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return updateRefundMethod?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (updateRefundMethod != null) {
      return updateRefundMethod(this);
    }
    return orElse();
  }
}

abstract class UpdateRefundMethod implements RefundEvent {
  const factory UpdateRefundMethod(
          final String id, final Map<String, dynamic> method) =
      _$UpdateRefundMethodImpl;

  String get id;
  Map<String, dynamic> get method;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateRefundMethodImplCopyWith<_$UpdateRefundMethodImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateTrackingInfoImplCopyWith<$Res> {
  factory _$$UpdateTrackingInfoImplCopyWith(_$UpdateTrackingInfoImpl value,
          $Res Function(_$UpdateTrackingInfoImpl) then) =
      __$$UpdateTrackingInfoImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> tracking});
}

/// @nodoc
class __$$UpdateTrackingInfoImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$UpdateTrackingInfoImpl>
    implements _$$UpdateTrackingInfoImplCopyWith<$Res> {
  __$$UpdateTrackingInfoImplCopyWithImpl(_$UpdateTrackingInfoImpl _value,
      $Res Function(_$UpdateTrackingInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? tracking = null,
  }) {
    return _then(_$UpdateTrackingInfoImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == tracking
          ? _value._tracking
          : tracking // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateTrackingInfoImpl implements UpdateTrackingInfo {
  const _$UpdateTrackingInfoImpl(this.id, final Map<String, dynamic> tracking)
      : _tracking = tracking;

  @override
  final String id;
  final Map<String, dynamic> _tracking;
  @override
  Map<String, dynamic> get tracking {
    if (_tracking is EqualUnmodifiableMapView) return _tracking;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_tracking);
  }

  @override
  String toString() {
    return 'RefundEvent.updateTrackingInfo(id: $id, tracking: $tracking)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateTrackingInfoImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._tracking, _tracking));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_tracking));

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateTrackingInfoImplCopyWith<_$UpdateTrackingInfoImpl> get copyWith =>
      __$$UpdateTrackingInfoImplCopyWithImpl<_$UpdateTrackingInfoImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return updateTrackingInfo(id, tracking);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return updateTrackingInfo?.call(id, tracking);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (updateTrackingInfo != null) {
      return updateTrackingInfo(id, tracking);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return updateTrackingInfo(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return updateTrackingInfo?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (updateTrackingInfo != null) {
      return updateTrackingInfo(this);
    }
    return orElse();
  }
}

abstract class UpdateTrackingInfo implements RefundEvent {
  const factory UpdateTrackingInfo(
          final String id, final Map<String, dynamic> tracking) =
      _$UpdateTrackingInfoImpl;

  String get id;
  Map<String, dynamic> get tracking;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateTrackingInfoImplCopyWith<_$UpdateTrackingInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AddAuditLogImplCopyWith<$Res> {
  factory _$$AddAuditLogImplCopyWith(
          _$AddAuditLogImpl value, $Res Function(_$AddAuditLogImpl) then) =
      __$$AddAuditLogImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String id,
      String action,
      String performedBy,
      Map<String, dynamic> details});
}

/// @nodoc
class __$$AddAuditLogImplCopyWithImpl<$Res>
    extends _$RefundEventCopyWithImpl<$Res, _$AddAuditLogImpl>
    implements _$$AddAuditLogImplCopyWith<$Res> {
  __$$AddAuditLogImplCopyWithImpl(
      _$AddAuditLogImpl _value, $Res Function(_$AddAuditLogImpl) _then)
      : super(_value, _then);

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? action = null,
    Object? performedBy = null,
    Object? details = null,
  }) {
    return _then(_$AddAuditLogImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as String,
      null == performedBy
          ? _value.performedBy
          : performedBy // ignore: cast_nullable_to_non_nullable
              as String,
      null == details
          ? _value._details
          : details // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$AddAuditLogImpl implements AddAuditLog {
  const _$AddAuditLogImpl(this.id, this.action, this.performedBy,
      final Map<String, dynamic> details)
      : _details = details;

  @override
  final String id;
  @override
  final String action;
  @override
  final String performedBy;
  final Map<String, dynamic> _details;
  @override
  Map<String, dynamic> get details {
    if (_details is EqualUnmodifiableMapView) return _details;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_details);
  }

  @override
  String toString() {
    return 'RefundEvent.addAuditLog(id: $id, action: $action, performedBy: $performedBy, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddAuditLogImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.performedBy, performedBy) ||
                other.performedBy == performedBy) &&
            const DeepCollectionEquality().equals(other._details, _details));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, action, performedBy,
      const DeepCollectionEquality().hash(_details));

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddAuditLogImplCopyWith<_$AddAuditLogImpl> get copyWith =>
      __$$AddAuditLogImplCopyWithImpl<_$AddAuditLogImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRefunds,
    required TResult Function(Refund refund) createRefund,
    required TResult Function(Refund refund) updateRefund,
    required TResult Function(String id) deleteRefund,
    required TResult Function(String id, String approvedBy) approveRefund,
    required TResult Function(
            String id, String rejectedBy, String rejectionReason)
        rejectRefund,
    required TResult Function(
            String id, String processedBy, String transactionId)
        processRefund,
    required TResult Function(String id, String disputeReason) createDispute,
    required TResult Function(String id, String resolvedBy, String resolution)
        resolveDispute,
    required TResult Function(
            String id, double partialAmount, String partialReason)
        updatePartialRefund,
    required TResult Function(String id, Map<String, dynamic> method)
        updateRefundMethod,
    required TResult Function(String id, Map<String, dynamic> tracking)
        updateTrackingInfo,
    required TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)
        addAuditLog,
  }) {
    return addAuditLog(id, action, performedBy, details);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRefunds,
    TResult? Function(Refund refund)? createRefund,
    TResult? Function(Refund refund)? updateRefund,
    TResult? Function(String id)? deleteRefund,
    TResult? Function(String id, String approvedBy)? approveRefund,
    TResult? Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult? Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult? Function(String id, String disputeReason)? createDispute,
    TResult? Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult? Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult? Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult? Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult? Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
  }) {
    return addAuditLog?.call(id, action, performedBy, details);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRefunds,
    TResult Function(Refund refund)? createRefund,
    TResult Function(Refund refund)? updateRefund,
    TResult Function(String id)? deleteRefund,
    TResult Function(String id, String approvedBy)? approveRefund,
    TResult Function(String id, String rejectedBy, String rejectionReason)?
        rejectRefund,
    TResult Function(String id, String processedBy, String transactionId)?
        processRefund,
    TResult Function(String id, String disputeReason)? createDispute,
    TResult Function(String id, String resolvedBy, String resolution)?
        resolveDispute,
    TResult Function(String id, double partialAmount, String partialReason)?
        updatePartialRefund,
    TResult Function(String id, Map<String, dynamic> method)?
        updateRefundMethod,
    TResult Function(String id, Map<String, dynamic> tracking)?
        updateTrackingInfo,
    TResult Function(String id, String action, String performedBy,
            Map<String, dynamic> details)?
        addAuditLog,
    required TResult orElse(),
  }) {
    if (addAuditLog != null) {
      return addAuditLog(id, action, performedBy, details);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadRefunds value) loadRefunds,
    required TResult Function(CreateRefund value) createRefund,
    required TResult Function(UpdateRefund value) updateRefund,
    required TResult Function(DeleteRefund value) deleteRefund,
    required TResult Function(ApproveRefund value) approveRefund,
    required TResult Function(RejectRefund value) rejectRefund,
    required TResult Function(ProcessRefund value) processRefund,
    required TResult Function(CreateDispute value) createDispute,
    required TResult Function(ResolveDispute value) resolveDispute,
    required TResult Function(UpdatePartialRefund value) updatePartialRefund,
    required TResult Function(UpdateRefundMethod value) updateRefundMethod,
    required TResult Function(UpdateTrackingInfo value) updateTrackingInfo,
    required TResult Function(AddAuditLog value) addAuditLog,
  }) {
    return addAuditLog(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadRefunds value)? loadRefunds,
    TResult? Function(CreateRefund value)? createRefund,
    TResult? Function(UpdateRefund value)? updateRefund,
    TResult? Function(DeleteRefund value)? deleteRefund,
    TResult? Function(ApproveRefund value)? approveRefund,
    TResult? Function(RejectRefund value)? rejectRefund,
    TResult? Function(ProcessRefund value)? processRefund,
    TResult? Function(CreateDispute value)? createDispute,
    TResult? Function(ResolveDispute value)? resolveDispute,
    TResult? Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult? Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult? Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult? Function(AddAuditLog value)? addAuditLog,
  }) {
    return addAuditLog?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadRefunds value)? loadRefunds,
    TResult Function(CreateRefund value)? createRefund,
    TResult Function(UpdateRefund value)? updateRefund,
    TResult Function(DeleteRefund value)? deleteRefund,
    TResult Function(ApproveRefund value)? approveRefund,
    TResult Function(RejectRefund value)? rejectRefund,
    TResult Function(ProcessRefund value)? processRefund,
    TResult Function(CreateDispute value)? createDispute,
    TResult Function(ResolveDispute value)? resolveDispute,
    TResult Function(UpdatePartialRefund value)? updatePartialRefund,
    TResult Function(UpdateRefundMethod value)? updateRefundMethod,
    TResult Function(UpdateTrackingInfo value)? updateTrackingInfo,
    TResult Function(AddAuditLog value)? addAuditLog,
    required TResult orElse(),
  }) {
    if (addAuditLog != null) {
      return addAuditLog(this);
    }
    return orElse();
  }
}

abstract class AddAuditLog implements RefundEvent {
  const factory AddAuditLog(
      final String id,
      final String action,
      final String performedBy,
      final Map<String, dynamic> details) = _$AddAuditLogImpl;

  String get id;
  String get action;
  String get performedBy;
  Map<String, dynamic> get details;

  /// Create a copy of RefundEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddAuditLogImplCopyWith<_$AddAuditLogImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
