import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/auth/auth_bloc.dart';

class ExecutorRequestsScreen extends StatefulWidget {
  const ExecutorRequestsScreen({super.key});

  @override
  State<ExecutorRequestsScreen> createState() => _ExecutorRequestsScreenState();
}

class _ExecutorRequestsScreenState extends State<ExecutorRequestsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  List<Map<String, dynamic>> _executorRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadExecutorRequests();
  }

  Future<void> _loadExecutorRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final snapshot = await _firestore
          .collection('executorRequests')
          .where('status', isEqualTo: 'pending')
          .get();

      final requests = snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      setState(() {
        _executorRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading executor requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approveExecutor(String requestId) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get request document
      final requestDoc = await _firestore.collection('executorRequests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Executor request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['uid'] as String;

      // Update user document to grant executor access
      await _firestore.collection('users').doc(userId).update({
        'isApproved': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update request status
      await _firestore.collection('executorRequests').doc(requestId).update({
        'status': 'approved',
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': context.read<AuthBloc>().state is AuthAuthenticatedState 
            ? (context.read<AuthBloc>().state as AuthAuthenticatedState).user.uid 
            : null,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Reload the list
      await _loadExecutorRequests();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Executor approved successfully')),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Error approving executor: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _rejectExecutor(String requestId) async {
    final reasonController = TextEditingController();
    
    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Executor'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Update request status
      await _firestore.collection('executorRequests').doc(requestId).update({
        'status': 'rejected',
        'rejectionReason': reason,
        'rejected_at': FieldValue.serverTimestamp(),
        'rejectedBy': context.read<AuthBloc>().state is AuthAuthenticatedState 
            ? (context.read<AuthBloc>().state as AuthAuthenticatedState).user.uid 
            : null,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Reload the list
      await _loadExecutorRequests();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Executor rejected successfully')),
      );
    } catch (e) {
      setState(() {
        _errorMessage = 'Error rejecting executor: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Executor Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadExecutorRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadExecutorRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _executorRequests.isEmpty
                  ? const Center(
                      child: Text('No pending executor requests'),
                    )
                  : ListView.builder(
                      itemCount: _executorRequests.length,
                      itemBuilder: (context, index) {
                        final request = _executorRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['displayName'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phoneNumber'] != null)
                                  Text('Phone: ${request['phoneNumber']}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['requestedAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectExecutor(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveExecutor(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';
    
    if (timestamp is Timestamp) {
      final date = timestamp.toDate();
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    }
    
    return 'N/A';
  }
}
