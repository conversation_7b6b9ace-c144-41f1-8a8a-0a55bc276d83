import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/event/event_model.dart';

part 'admin_event_management_state.freezed.dart';

@freezed
class AdminEventManagementState with _$AdminEventManagementState {
  const factory AdminEventManagementState.initial() = _Initial;
  const factory AdminEventManagementState.loading() = _Loading;
  const factory AdminEventManagementState.error(String message) = _Error;
  const factory AdminEventManagementState.loadedPendingEvents(
      List<EventModel> events) = _LoadedPendingEvents;
  const factory AdminEventManagementState.eventApproved(String message) =
      _EventApproved;
  const factory AdminEventManagementState.eventRejected(String message) =
      _EventRejected;
  const factory AdminEventManagementState.eventCreated(String message) =
      _EventCreated;
  const factory AdminEventManagementState.eventUpdated(String message) =
      _EventUpdated;
}
