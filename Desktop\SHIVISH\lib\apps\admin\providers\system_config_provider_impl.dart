import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/providers/system_config_provider.dart';
import 'package:shivish/shared/services/system_config_service.dart';
import 'package:shivish/shared/core/service_locator.dart';

/// This file provides the implementation for the SystemConfigService provider
/// that is declared in the shared providers directory.

// We're using direct overrides with overrideWith instead of provider implementations

/// Function to register the provider overrides
List<Override> getSystemConfigProviderOverrides() {
  return [
    // Override the service provider
    systemConfigServiceProvider.overrideWith((ref) {
      return serviceLocator<SystemConfigService>();
    }),

    // Override the stream provider
    systemConfigProvider.overrideWith((ref) {
      final service = ref.watch(systemConfigServiceProvider);
      return service.watchConfig();
    }),

    // Override the state notifier provider
    systemConfigStateProvider.overrideWith((ref) {
      final service = ref.watch(systemConfigServiceProvider);
      return SystemConfigNotifier(service);
    }),
  ];
}
