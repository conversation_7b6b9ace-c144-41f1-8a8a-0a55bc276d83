import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';
import '../../../shared/ui_components/errors/error_message.dart';

class AdminProfileScreen extends StatelessWidget {
  const AdminProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Admin Profile'),
        ),
        body: StreamBuilder<User?>(
          stream: FirebaseAuth.instance.authStateChanges(),
          builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: LoadingIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: ErrorMessage(
                message: 'Failed to load profile: ${snapshot.error}',
                onRetry: () => FirebaseAuth.instance.authStateChanges(),
              ),
            );
          }

          final user = snapshot.data;
          if (user == null) {
            return const Center(
              child: Text('Not logged in'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                CircleAvatar(
                  radius: 60,
                  backgroundColor: Theme.of(context).colorScheme.primary.withAlpha(25),
                  child: user.photoURL != null && user.photoURL!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(60),
                          child: Image.network(
                            user.photoURL!,
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Icon(
                              Icons.person,
                              size: 60,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        )
                      : Icon(
                          Icons.person,
                          size: 60,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                ),
                const SizedBox(height: 20),
                Text(
                  user.displayName ?? 'Admin User',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  user.email ?? 'No email provided',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 30),
                _buildInfoCard(context, 'Account Information'),
                _buildProfileItem(context, 'User ID', user.uid),
                _buildProfileItem(context, 'Role', 'Administrator'),
                _buildProfileItem(context, 'Phone', user.phoneNumber ?? 'Not provided'),
                _buildProfileItem(context, 'Email Verified', user.emailVerified ? 'Yes' : 'No'),

                const SizedBox(height: 20),
                _buildInfoCard(context, 'Account Settings'),
                _buildActionItem(
                  context,
                  'Edit Profile',
                  Icons.edit,
                  () {
                    // Navigate to edit profile screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Edit profile functionality coming soon')),
                    );
                  }
                ),
                _buildActionItem(
                  context,
                  'Change Password',
                  Icons.lock_outline,
                  () {
                    // Navigate to change password screen
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Change password functionality coming soon')),
                    );
                  }
                ),
                _buildActionItem(
                  context,
                  'Notification Settings',
                  Icons.notifications_outlined,
                  () {
                    // Navigate to notification settings
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Notification settings coming soon')),
                    );
                  }
                ),
                _buildActionItem(
                  context,
                  'Privacy Settings',
                  Icons.privacy_tip_outlined,
                  () {
                    // Navigate to privacy settings
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Privacy settings coming soon')),
                    );
                  }
                ),

                const SizedBox(height: 20),
                _buildInfoCard(context, 'Support'),
                _buildActionItem(
                  context,
                  'Help & Support',
                  Icons.help_outline,
                  () {
                    // Navigate to help & support
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Help & support coming soon')),
                    );
                  }
                ),
                _buildActionItem(
                  context,
                  'Terms & Conditions',
                  Icons.description_outlined,
                  () {
                    // Navigate to terms & conditions
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Terms & conditions coming soon')),
                    );
                  }
                ),

                const SizedBox(height: 20),
                ElevatedButton.icon(
                  onPressed: () async {
                    // Logout functionality
                    await FirebaseAuth.instance.signOut();
                  },
                  icon: const Icon(Icons.logout),
                  label: const Text('Logout'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.error,
                    foregroundColor: Theme.of(context).colorScheme.onError,
                    minimumSize: const Size(double.infinity, 50),
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          );
          },
        ),
    );
  }

  Widget _buildInfoCard(BuildContext context, String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.onPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildProfileItem(BuildContext context, String label, String value) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleSmall,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildActionItem(BuildContext context, String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 22,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                label,
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(128),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
