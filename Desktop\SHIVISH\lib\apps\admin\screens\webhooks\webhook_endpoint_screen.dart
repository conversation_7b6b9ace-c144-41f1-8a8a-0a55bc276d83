import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../shared/utils/logger.dart';
import '../../../../shared/ui_components/dialogs/confirmation_dialog.dart';

class WebhookEndpointScreen extends ConsumerStatefulWidget {
  const WebhookEndpointScreen({super.key});

  @override
  ConsumerState<WebhookEndpointScreen> createState() => _WebhookEndpointScreenState();
}

class _WebhookEndpointScreenState extends ConsumerState<WebhookEndpointScreen> {
  final _logger = getLogger('WebhookEndpointScreen');
  final _firestore = FirebaseFirestore.instance;
  
  bool _isLoading = true;
  String? _errorMessage;
  Map<String, dynamic> _webhookConfigurations = {};
  
  @override
  void initState() {
    super.initState();
    _loadWebhookConfigurations();
  }
  
  /// Load webhook configurations from Firestore
  Future<void> _loadWebhookConfigurations() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
      
      final snapshot = await _firestore
          .collection('delivery_providers')
          .get();
      
      final configs = <String, dynamic>{};
      for (final doc in snapshot.docs) {
        final data = doc.data();
        configs[doc.id] = data['config'] ?? {};
      }
      
      setState(() {
        _webhookConfigurations = configs;
        _isLoading = false;
      });
    } catch (e) {
      _logger.severe('Error loading webhook configurations: $e');
      setState(() {
        _errorMessage = 'Error loading webhook configurations: $e';
        _isLoading = false;
      });
    }
  }
  
  /// Save webhook configuration
  Future<void> _saveWebhookConfiguration(String provider, Map<String, dynamic> config) async {
    try {
      await _firestore
          .collection('delivery_providers')
          .doc(provider)
          .update({
            'config': config,
            'updatedAt': FieldValue.serverTimestamp(),
          });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Webhook configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
      
      // Reload configurations
      await _loadWebhookConfigurations();
    } catch (e) {
      _logger.severe('Error saving webhook configuration: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving webhook configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  /// Build webhook configuration form for Ecom Express
  Widget _buildEcomExpressForm(BuildContext context) {
    final config = _webhookConfigurations['ecom_express'] ?? {};
    
    final apiUrlController = TextEditingController(text: config['api_url'] ?? '');
    final apiKeyController = TextEditingController(text: config['api_key'] ?? '');
    final webhookSecretController = TextEditingController(text: config['webhook_secret'] ?? '');
    final webhookUrlController = TextEditingController(
      text: config['webhook_url'] ?? 'https://your-app.com/api/webhooks/ecom-express',
    );
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ecom Express Webhook Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // API URL
            TextField(
              controller: apiUrlController,
              decoration: const InputDecoration(
                labelText: 'API URL',
                hintText: 'https://api.ecomexpress.com',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            
            // API Key
            TextField(
              controller: apiKeyController,
              decoration: const InputDecoration(
                labelText: 'API Key',
                hintText: 'Your Ecom Express API Key',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 12),
            
            // Webhook Secret
            TextField(
              controller: webhookSecretController,
              decoration: const InputDecoration(
                labelText: 'Webhook Secret',
                hintText: 'Secret key for webhook verification',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 12),
            
            // Webhook URL
            TextField(
              controller: webhookUrlController,
              decoration: const InputDecoration(
                labelText: 'Webhook URL',
                hintText: 'URL to receive webhook events',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
            ),
            const SizedBox(height: 12),
            
            // Copy Webhook URL button
            OutlinedButton.icon(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: webhookUrlController.text));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Webhook URL copied to clipboard')),
                );
              },
              icon: const Icon(Icons.copy),
              label: const Text('Copy Webhook URL'),
            ),
            const SizedBox(height: 16),
            
            // Save and Test buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () => _testWebhookConfiguration('ecom_express'),
                  child: const Text('Test Webhook'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    final newConfig = {
                      'api_url': apiUrlController.text,
                      'api_key': apiKeyController.text,
                      'webhook_secret': webhookSecretController.text,
                      'webhook_url': webhookUrlController.text,
                    };
                    _saveWebhookConfiguration('ecom_express', newConfig);
                  },
                  child: const Text('Save Configuration'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build webhook configuration form for Local Delivery
  Widget _buildLocalDeliveryForm(BuildContext context) {
    final config = _webhookConfigurations['local_delivery'] ?? {};
    
    final webhookSecretController = TextEditingController(text: config['webhook_secret'] ?? '');
    final webhookUrlController = TextEditingController(
      text: config['webhook_url'] ?? 'https://your-app.com/api/webhooks/local-delivery',
    );
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Local Delivery Webhook Configuration',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Webhook Secret
            TextField(
              controller: webhookSecretController,
              decoration: const InputDecoration(
                labelText: 'Webhook Secret',
                hintText: 'Secret key for webhook verification',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 12),
            
            // Webhook URL
            TextField(
              controller: webhookUrlController,
              decoration: const InputDecoration(
                labelText: 'Webhook URL',
                hintText: 'URL to receive webhook events',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
            ),
            const SizedBox(height: 12),
            
            // Copy Webhook URL button
            OutlinedButton.icon(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: webhookUrlController.text));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Webhook URL copied to clipboard')),
                );
              },
              icon: const Icon(Icons.copy),
              label: const Text('Copy Webhook URL'),
            ),
            const SizedBox(height: 16),
            
            // Save and Test buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () => _testWebhookConfiguration('local_delivery'),
                  child: const Text('Test Webhook'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () {
                    final newConfig = {
                      'webhook_secret': webhookSecretController.text,
                      'webhook_url': webhookUrlController.text,
                    };
                    _saveWebhookConfiguration('local_delivery', newConfig);
                  },
                  child: const Text('Save Configuration'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  /// Test webhook configuration
  Future<void> _testWebhookConfiguration(String provider) async {
    if (!mounted) return;
    
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: 'Test Webhook',
        content: 'This will send a test webhook to your server. Continue?',
        confirmText: 'Test',
        cancelText: 'Cancel',
      ),
    );
    
    if (confirmed != true || !mounted) return;
    
    // Create a dialog key to track the dialog
    final dialogKey = GlobalKey<State>();
    
    if (!mounted) return;
    
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        key: dialogKey,
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Testing webhook...'),
          ],
        ),
      ),
    );
    
    try {
      // Simulate webhook test
      await Future.delayed(const Duration(seconds: 2));
      
      if (!mounted) return;
      
      // Close loading dialog using Navigator.pop with the current context
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      
      if (!mounted) return;
      
      // Show success dialog
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Test Successful'),
          content: const Text('Webhook test completed successfully.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (!mounted) return;
      
      // Close loading dialog using Navigator.pop with the current context
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }
      
      if (!mounted) return;
      
      // Show error dialog
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Test Failed'),
          content: Text('Error testing webhook: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }
  
  /// View webhook history for a specific provider
  void _viewWebhookHistory(String provider) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebhookHistoryScreen(provider: provider),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Webhook Endpoints'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWebhookConfigurations,
            tooltip: 'Reload configurations',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null && _webhookConfigurations.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWebhookConfigurations,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Ecom Express Webhook Configuration
                      _buildEcomExpressForm(context),
                      const SizedBox(height: 16),
                      
                      // View Ecom Express Webhook History
                      OutlinedButton.icon(
                        onPressed: () => _viewWebhookHistory('ecom_express'),
                        icon: const Icon(Icons.history),
                        label: const Text('View Ecom Express Webhook History'),
                      ),
                      const SizedBox(height: 24),
                      
                      // Local Delivery Webhook Configuration
                      _buildLocalDeliveryForm(context),
                      const SizedBox(height: 16),
                      
                      // View Local Delivery Webhook History
                      OutlinedButton.icon(
                        onPressed: () => _viewWebhookHistory('local_delivery'),
                        icon: const Icon(Icons.history),
                        label: const Text('View Local Delivery Webhook History'),
                      ),
                    ],
                  ),
                ),
    );
  }
}

class WebhookHistoryScreen extends StatelessWidget {
  final String provider;
  
  const WebhookHistoryScreen({
    super.key,
    required this.provider,
  });
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${provider.replaceAll('_', ' ').toUpperCase()} Webhook History'),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance
            .collection('webhook_events')
            .where('provider', isEqualTo: provider)
            .orderBy('timestamp', descending: true)
            .limit(50)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading webhook history: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            );
          }
          
          final events = snapshot.data?.docs ?? [];
          
          if (events.isEmpty) {
            return const Center(
              child: Text('No webhook events found'),
            );
          }
          
          return ListView.builder(
            itemCount: events.length,
            itemBuilder: (context, index) {
              final event = events[index].data() as Map<String, dynamic>;
              final timestamp = event['timestamp'] as Timestamp?;
              final status = event['status'] as String? ?? 'unknown';
              final eventType = event['event_type'] as String? ?? 'unknown';
              
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  title: Text('Event: $eventType'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Status: $status'),
                      Text('Time: ${timestamp?.toDate().toString() ?? 'Unknown'}'),
                    ],
                  ),
                  trailing: Icon(
                    status == 'success' ? Icons.check_circle : Icons.error,
                    color: status == 'success' ? Colors.green : Colors.red,
                  ),
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('Webhook Event Details'),
                        content: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text('Event Type: $eventType'),
                              Text('Status: $status'),
                              Text('Time: ${timestamp?.toDate().toString() ?? 'Unknown'}'),
                              const Divider(),
                              const Text(
                                'Payload:',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  const JsonEncoder.withIndent('  ')
                                      .convert(event['payload'] ?? {}),
                                  style: const TextStyle(fontFamily: 'monospace'),
                                ),
                              ),
                              if (event['error'] != null) ...[
                                const SizedBox(height: 16),
                                const Text(
                                  'Error:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.red[50],
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(color: Colors.red),
                                  ),
                                  child: Text(
                                    event['error'].toString(),
                                    style: const TextStyle(color: Colors.red),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Close'),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
