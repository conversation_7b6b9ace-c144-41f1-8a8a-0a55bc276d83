﻿package com.shivish.utils

import android.util.Log

/**
 * Utility class for Java version operations
 */
object JavaVersionUtils {
    private const val TAG = "JavaVersionUtils"

    /**
     * Get Java version
     */
    fun getJavaVersion(): String {
        return try {
            System.getProperty("java.version") ?: "Unknown"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Java version: ${e.message}")
            "Unknown"
        }
    }

    /**
     * Get Java vendor
     */
    fun getJavaVendor(): String {
        return try {
            System.getProperty("java.vendor") ?: "Unknown"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Java vendor: ${e.message}")
            "Unknown"
        }
    }

    /**
     * Get Java home
     */
    fun getJavaHome(): String {
        return try {
            System.getProperty("java.home") ?: "Unknown"
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Java home: ${e.message}")
            "Unknown"
        }
    }

    /**
     * Check if Java version is compatible
     */
    fun isJavaVersionCompatible(minVersion: String): Boolean {
        return try {
            val currentVersion = getJavaVersion()
            // Simple version comparison - can be enhanced for more complex version strings
            currentVersion >= minVersion
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Java version compatibility: ${e.message}")
            false
        }
    }

    /**
     * Get all Java system properties
     */
    fun getJavaSystemProperties(): Map<String, String> {
        return try {
            val properties = mutableMapOf<String, String>()
            val systemProperties = System.getProperties()
            for (key in systemProperties.stringPropertyNames()) {
                properties[key] = systemProperties.getProperty(key) ?: ""
            }
            properties
        } catch (e: Exception) {
            Log.e(TAG, "Error getting Java system properties: ${e.message}")
            emptyMap()
        }
    }
}
