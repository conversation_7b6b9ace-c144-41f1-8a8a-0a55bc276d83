// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_gateway_analytics_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$PaymentGatewayAnalyticsEvent {
  DateTimeRange<DateTime> get dateRange => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)
        loadAnalytics,
    required TResult Function(DateTimeRange<DateTime> dateRange)
        updateDateRange,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult? Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalytics value) loadAnalytics,
    required TResult Function(UpdateDateRange value) updateDateRange,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalytics value)? loadAnalytics,
    TResult? Function(UpdateDateRange value)? updateDateRange,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalytics value)? loadAnalytics,
    TResult Function(UpdateDateRange value)? updateDateRange,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentGatewayAnalyticsEventCopyWith<PaymentGatewayAnalyticsEvent>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  factory $PaymentGatewayAnalyticsEventCopyWith(
          PaymentGatewayAnalyticsEvent value,
          $Res Function(PaymentGatewayAnalyticsEvent) then) =
      _$PaymentGatewayAnalyticsEventCopyWithImpl<$Res,
          PaymentGatewayAnalyticsEvent>;
  @useResult
  $Res call({DateTimeRange<DateTime> dateRange});
}

/// @nodoc
class _$PaymentGatewayAnalyticsEventCopyWithImpl<$Res,
        $Val extends PaymentGatewayAnalyticsEvent>
    implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  _$PaymentGatewayAnalyticsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateRange = null,
  }) {
    return _then(_value.copyWith(
      dateRange: null == dateRange
          ? _value.dateRange
          : dateRange // ignore: cast_nullable_to_non_nullable
              as DateTimeRange<DateTime>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoadAnalyticsImplCopyWith<$Res>
    implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  factory _$$LoadAnalyticsImplCopyWith(
          _$LoadAnalyticsImpl value, $Res Function(_$LoadAnalyticsImpl) then) =
      __$$LoadAnalyticsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({PaymentGateway gateway, DateTimeRange<DateTime> dateRange});

  $PaymentGatewayCopyWith<$Res> get gateway;
}

/// @nodoc
class __$$LoadAnalyticsImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsEventCopyWithImpl<$Res,
        _$LoadAnalyticsImpl> implements _$$LoadAnalyticsImplCopyWith<$Res> {
  __$$LoadAnalyticsImplCopyWithImpl(
      _$LoadAnalyticsImpl _value, $Res Function(_$LoadAnalyticsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gateway = null,
    Object? dateRange = null,
  }) {
    return _then(_$LoadAnalyticsImpl(
      gateway: null == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as PaymentGateway,
      dateRange: null == dateRange
          ? _value.dateRange
          : dateRange // ignore: cast_nullable_to_non_nullable
              as DateTimeRange<DateTime>,
    ));
  }

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentGatewayCopyWith<$Res> get gateway {
    return $PaymentGatewayCopyWith<$Res>(_value.gateway, (value) {
      return _then(_value.copyWith(gateway: value));
    });
  }
}

/// @nodoc

class _$LoadAnalyticsImpl implements LoadAnalytics {
  const _$LoadAnalyticsImpl({required this.gateway, required this.dateRange});

  @override
  final PaymentGateway gateway;
  @override
  final DateTimeRange<DateTime> dateRange;

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsEvent.loadAnalytics(gateway: $gateway, dateRange: $dateRange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadAnalyticsImpl &&
            (identical(other.gateway, gateway) || other.gateway == gateway) &&
            (identical(other.dateRange, dateRange) ||
                other.dateRange == dateRange));
  }

  @override
  int get hashCode => Object.hash(runtimeType, gateway, dateRange);

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadAnalyticsImplCopyWith<_$LoadAnalyticsImpl> get copyWith =>
      __$$LoadAnalyticsImplCopyWithImpl<_$LoadAnalyticsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)
        loadAnalytics,
    required TResult Function(DateTimeRange<DateTime> dateRange)
        updateDateRange,
  }) {
    return loadAnalytics(gateway, dateRange);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult? Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
  }) {
    return loadAnalytics?.call(gateway, dateRange);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
    required TResult orElse(),
  }) {
    if (loadAnalytics != null) {
      return loadAnalytics(gateway, dateRange);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalytics value) loadAnalytics,
    required TResult Function(UpdateDateRange value) updateDateRange,
  }) {
    return loadAnalytics(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalytics value)? loadAnalytics,
    TResult? Function(UpdateDateRange value)? updateDateRange,
  }) {
    return loadAnalytics?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalytics value)? loadAnalytics,
    TResult Function(UpdateDateRange value)? updateDateRange,
    required TResult orElse(),
  }) {
    if (loadAnalytics != null) {
      return loadAnalytics(this);
    }
    return orElse();
  }
}

abstract class LoadAnalytics implements PaymentGatewayAnalyticsEvent {
  const factory LoadAnalytics(
      {required final PaymentGateway gateway,
      required final DateTimeRange<DateTime> dateRange}) = _$LoadAnalyticsImpl;

  PaymentGateway get gateway;
  @override
  DateTimeRange<DateTime> get dateRange;

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadAnalyticsImplCopyWith<_$LoadAnalyticsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateDateRangeImplCopyWith<$Res>
    implements $PaymentGatewayAnalyticsEventCopyWith<$Res> {
  factory _$$UpdateDateRangeImplCopyWith(_$UpdateDateRangeImpl value,
          $Res Function(_$UpdateDateRangeImpl) then) =
      __$$UpdateDateRangeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTimeRange<DateTime> dateRange});
}

/// @nodoc
class __$$UpdateDateRangeImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsEventCopyWithImpl<$Res,
        _$UpdateDateRangeImpl> implements _$$UpdateDateRangeImplCopyWith<$Res> {
  __$$UpdateDateRangeImplCopyWithImpl(
      _$UpdateDateRangeImpl _value, $Res Function(_$UpdateDateRangeImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateRange = null,
  }) {
    return _then(_$UpdateDateRangeImpl(
      dateRange: null == dateRange
          ? _value.dateRange
          : dateRange // ignore: cast_nullable_to_non_nullable
              as DateTimeRange<DateTime>,
    ));
  }
}

/// @nodoc

class _$UpdateDateRangeImpl implements UpdateDateRange {
  const _$UpdateDateRangeImpl({required this.dateRange});

  @override
  final DateTimeRange<DateTime> dateRange;

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsEvent.updateDateRange(dateRange: $dateRange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateDateRangeImpl &&
            (identical(other.dateRange, dateRange) ||
                other.dateRange == dateRange));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dateRange);

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateDateRangeImplCopyWith<_$UpdateDateRangeImpl> get copyWith =>
      __$$UpdateDateRangeImplCopyWithImpl<_$UpdateDateRangeImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)
        loadAnalytics,
    required TResult Function(DateTimeRange<DateTime> dateRange)
        updateDateRange,
  }) {
    return updateDateRange(dateRange);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(
            PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult? Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
  }) {
    return updateDateRange?.call(dateRange);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(PaymentGateway gateway, DateTimeRange<DateTime> dateRange)?
        loadAnalytics,
    TResult Function(DateTimeRange<DateTime> dateRange)? updateDateRange,
    required TResult orElse(),
  }) {
    if (updateDateRange != null) {
      return updateDateRange(dateRange);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadAnalytics value) loadAnalytics,
    required TResult Function(UpdateDateRange value) updateDateRange,
  }) {
    return updateDateRange(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadAnalytics value)? loadAnalytics,
    TResult? Function(UpdateDateRange value)? updateDateRange,
  }) {
    return updateDateRange?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadAnalytics value)? loadAnalytics,
    TResult Function(UpdateDateRange value)? updateDateRange,
    required TResult orElse(),
  }) {
    if (updateDateRange != null) {
      return updateDateRange(this);
    }
    return orElse();
  }
}

abstract class UpdateDateRange implements PaymentGatewayAnalyticsEvent {
  const factory UpdateDateRange(
          {required final DateTimeRange<DateTime> dateRange}) =
      _$UpdateDateRangeImpl;

  @override
  DateTimeRange<DateTime> get dateRange;

  /// Create a copy of PaymentGatewayAnalyticsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateDateRangeImplCopyWith<_$UpdateDateRangeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$PaymentGatewayAnalyticsState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)
        loaded,
    required TResult Function(String message) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult? Function(String message)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentGatewayAnalyticsStateCopyWith<$Res> {
  factory $PaymentGatewayAnalyticsStateCopyWith(
          PaymentGatewayAnalyticsState value,
          $Res Function(PaymentGatewayAnalyticsState) then) =
      _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res,
          PaymentGatewayAnalyticsState>;
}

/// @nodoc
class _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res,
        $Val extends PaymentGatewayAnalyticsState>
    implements $PaymentGatewayAnalyticsStateCopyWith<$Res> {
  _$PaymentGatewayAnalyticsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)
        loaded,
    required TResult Function(String message) error,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult? Function(String message)? error,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class Initial implements PaymentGatewayAnalyticsState {
  const factory Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)
        loaded,
    required TResult Function(String message) error,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult? Function(String message)? error,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class Loading implements PaymentGatewayAnalyticsState {
  const factory Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {PaymentGateway gateway,
      Map<String, dynamic> summary,
      List<Map<String, dynamic>> dailyCounts,
      List<Map<String, dynamic>> statusDistribution});

  $PaymentGatewayCopyWith<$Res> get gateway;
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gateway = null,
    Object? summary = null,
    Object? dailyCounts = null,
    Object? statusDistribution = null,
  }) {
    return _then(_$LoadedImpl(
      gateway: null == gateway
          ? _value.gateway
          : gateway // ignore: cast_nullable_to_non_nullable
              as PaymentGateway,
      summary: null == summary
          ? _value._summary
          : summary // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      dailyCounts: null == dailyCounts
          ? _value._dailyCounts
          : dailyCounts // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
      statusDistribution: null == statusDistribution
          ? _value._statusDistribution
          : statusDistribution // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>,
    ));
  }

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaymentGatewayCopyWith<$Res> get gateway {
    return $PaymentGatewayCopyWith<$Res>(_value.gateway, (value) {
      return _then(_value.copyWith(gateway: value));
    });
  }
}

/// @nodoc

class _$LoadedImpl implements Loaded {
  const _$LoadedImpl(
      {required this.gateway,
      required final Map<String, dynamic> summary,
      required final List<Map<String, dynamic>> dailyCounts,
      required final List<Map<String, dynamic>> statusDistribution})
      : _summary = summary,
        _dailyCounts = dailyCounts,
        _statusDistribution = statusDistribution;

  @override
  final PaymentGateway gateway;
  final Map<String, dynamic> _summary;
  @override
  Map<String, dynamic> get summary {
    if (_summary is EqualUnmodifiableMapView) return _summary;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_summary);
  }

  final List<Map<String, dynamic>> _dailyCounts;
  @override
  List<Map<String, dynamic>> get dailyCounts {
    if (_dailyCounts is EqualUnmodifiableListView) return _dailyCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dailyCounts);
  }

  final List<Map<String, dynamic>> _statusDistribution;
  @override
  List<Map<String, dynamic>> get statusDistribution {
    if (_statusDistribution is EqualUnmodifiableListView)
      return _statusDistribution;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_statusDistribution);
  }

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsState.loaded(gateway: $gateway, summary: $summary, dailyCounts: $dailyCounts, statusDistribution: $statusDistribution)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            (identical(other.gateway, gateway) || other.gateway == gateway) &&
            const DeepCollectionEquality().equals(other._summary, _summary) &&
            const DeepCollectionEquality()
                .equals(other._dailyCounts, _dailyCounts) &&
            const DeepCollectionEquality()
                .equals(other._statusDistribution, _statusDistribution));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      gateway,
      const DeepCollectionEquality().hash(_summary),
      const DeepCollectionEquality().hash(_dailyCounts),
      const DeepCollectionEquality().hash(_statusDistribution));

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)
        loaded,
    required TResult Function(String message) error,
  }) {
    return loaded(gateway, summary, dailyCounts, statusDistribution);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult? Function(String message)? error,
  }) {
    return loaded?.call(gateway, summary, dailyCounts, statusDistribution);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(gateway, summary, dailyCounts, statusDistribution);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return loaded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return loaded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (loaded != null) {
      return loaded(this);
    }
    return orElse();
  }
}

abstract class Loaded implements PaymentGatewayAnalyticsState {
  const factory Loaded(
          {required final PaymentGateway gateway,
          required final Map<String, dynamic> summary,
          required final List<Map<String, dynamic>> dailyCounts,
          required final List<Map<String, dynamic>> statusDistribution}) =
      _$LoadedImpl;

  PaymentGateway get gateway;
  Map<String, dynamic> get summary;
  List<Map<String, dynamic>> get dailyCounts;
  List<Map<String, dynamic>> get statusDistribution;

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$PaymentGatewayAnalyticsStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'PaymentGatewayAnalyticsState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)
        loaded,
    required TResult Function(String message) error,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult? Function(String message)? error,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(
            PaymentGateway gateway,
            Map<String, dynamic> summary,
            List<Map<String, dynamic>> dailyCounts,
            List<Map<String, dynamic>> statusDistribution)?
        loaded,
    TResult Function(String message)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Loaded value) loaded,
    required TResult Function(Error value) error,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Loaded value)? loaded,
    TResult? Function(Error value)? error,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Loaded value)? loaded,
    TResult Function(Error value)? error,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class Error implements PaymentGatewayAnalyticsState {
  const factory Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of PaymentGatewayAnalyticsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
