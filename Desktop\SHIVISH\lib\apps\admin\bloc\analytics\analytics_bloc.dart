import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/models/analytics/analytics_model.dart';
import 'package:shivish/shared/services/analytics/analytics_service.dart';
import 'package:shivish/apps/admin/widgets/analytics_filter_dialog.dart';
import 'package:shivish/apps/admin/bloc/analytics/analytics_event.dart';
import 'package:shivish/apps/admin/bloc/analytics/analytics_state.dart';

class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final AnalyticsService _analyticsService;
  StreamSubscription<AnalyticsData>? _analyticsSubscription;
  String _currentTimeRange = 'week';
  AnalyticsFilterData? _currentFilters;

  AnalyticsBloc(this._analyticsService)
      : super(const AnalyticsState.initial()) {
    on<LoadAnalyticsData>(_onLoadAnalyticsData);
    on<UpdateTimeRange>(_onUpdateTimeRange);
    on<RefreshAnalyticsData>(_onRefreshAnalyticsData);
    on<ApplyFilters>(_onApplyFilters);
  }

  Future<void> _onLoadAnalyticsData(
    LoadAnalyticsData event,
    Emitter<AnalyticsState> emit,
  ) async {
    _currentTimeRange = event.timeRange;
    await _subscribeToAnalyticsData(emit);
  }

  Future<void> _onUpdateTimeRange(
    UpdateTimeRange event,
    Emitter<AnalyticsState> emit,
  ) async {
    _currentTimeRange = event.timeRange;
    await _subscribeToAnalyticsData(emit);
  }

  Future<void> _onRefreshAnalyticsData(
    RefreshAnalyticsData event,
    Emitter<AnalyticsState> emit,
  ) async {
    await _subscribeToAnalyticsData(emit);
  }

  Future<void> _onApplyFilters(
    ApplyFilters event,
    Emitter<AnalyticsState> emit,
  ) async {
    _currentFilters = event.filters;
    await _subscribeToAnalyticsData(emit);
  }

  Future<void> _subscribeToAnalyticsData(Emitter<AnalyticsState> emit) async {
    try {
      emit(const AnalyticsState.loading());
      await _analyticsSubscription?.cancel();
      _analyticsSubscription =
          _analyticsService.getAnalyticsDataStream(_currentTimeRange).listen(
        (data) {
          final filteredData = _applyFiltersToData(data);
          emit(AnalyticsState.loaded(
            filteredData,
            timeRange: _currentTimeRange,
            filters: _currentFilters,
          ));
        },
        onError: (error) => emit(AnalyticsState.error(error.toString())),
      );
    } catch (e) {
      emit(AnalyticsState.error(e.toString()));
    }
  }

  AnalyticsData _applyFiltersToData(AnalyticsData data) {
    if (_currentFilters == null) return data;

    // Apply filters to sales data
    final filteredSalesData = data.salesData.where((sale) {
      final amount = sale.amount;
      return (_currentFilters!.minSales == null ||
              amount >= _currentFilters!.minSales!) &&
          (_currentFilters!.maxSales == null ||
              amount <= _currentFilters!.maxSales!);
    }).toList();

    // Apply filters to top products
    final filteredTopProducts = data.topProducts.where((product) {
      final revenue = product.revenue;
      return (_currentFilters!.minOrderValue == null ||
              revenue >= _currentFilters!.minOrderValue!) &&
          (_currentFilters!.maxOrderValue == null ||
              revenue <= _currentFilters!.maxOrderValue!);
    }).toList();

    // Sort top products based on filter settings
    if (_currentFilters!.sortBy.isNotEmpty) {
      filteredTopProducts.sort((a, b) {
        int comparison;
        switch (_currentFilters!.sortBy) {
          case 'sales':
            comparison = b.revenue.compareTo(a.revenue);
            break;
          case 'orders':
            comparison = b.quantity.compareTo(a.quantity);
            break;
          case 'average_order':
            comparison =
                (b.revenue / b.quantity).compareTo(a.revenue / a.quantity);
            break;
          case 'satisfaction':
            comparison = b.revenue.compareTo(a.revenue);
            break;
          default:
            comparison = b.revenue.compareTo(a.revenue);
        }
        return _currentFilters!.sortAscending ? -comparison : comparison;
      });
    }

    // Apply filters to customer metrics
    final customerMetrics = data.customerMetrics;
    if (_currentFilters!.minSatisfaction != null &&
        customerMetrics.customerSatisfaction <
            _currentFilters!.minSatisfaction!) {
      return data.copyWith(
        salesData: filteredSalesData,
        topProducts: filteredTopProducts,
        customerMetrics: customerMetrics.copyWith(
          customerSatisfaction: _currentFilters!.minSatisfaction!,
        ),
      );
    }

    if (_currentFilters!.maxSatisfaction != null &&
        customerMetrics.customerSatisfaction >
            _currentFilters!.maxSatisfaction!) {
      return data.copyWith(
        salesData: filteredSalesData,
        topProducts: filteredTopProducts,
        customerMetrics: customerMetrics.copyWith(
          customerSatisfaction: _currentFilters!.maxSatisfaction!,
        ),
      );
    }

    return data.copyWith(
      salesData: filteredSalesData,
      topProducts: filteredTopProducts,
    );
  }

  @override
  Future<void> close() async {
    await _analyticsSubscription?.cancel();
    return super.close();
  }
}
