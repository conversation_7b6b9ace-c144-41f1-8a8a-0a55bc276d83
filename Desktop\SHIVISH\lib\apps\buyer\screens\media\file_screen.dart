import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:share_plus/share_plus.dart' as share_plus;
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import '../../../../../shared/models/file/file_model.dart';
import '../../../../shared/providers/file_provider.dart';
import 'widgets/file_list_widget.dart';
import 'widgets/file_preview_widget.dart';
import 'widgets/file_upload_dialog.dart';

class FileScreen extends HookConsumerWidget {
  final String userId;

  const FileScreen({
    super.key,
    required this.userId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortBy = useState(FileSort.dateDesc);
    final selectedFile = useState<FileModel?>(null);
    final showUploadDialog = useState(false);

    final filesAsync = ref.watch(filesProvider(userId));

    Future<void> shareFile(FileModel file) async {
      try {
        if (file.thumbnailUrl == null) {
          throw Exception('File URL is not available');
        }
        final response = await http.get(Uri.parse(file.thumbnailUrl!));
        final tempDir = await getTemporaryDirectory();
        final tempFile = File('${tempDir.path}/${file.name}');
        await tempFile.writeAsBytes(response.bodyBytes);
        await share_plus.Share.shareXFiles([share_plus.XFile(tempFile.path)]);
        await tempFile.delete();
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to share file: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    Future<void> downloadFile(FileModel file) async {
      try {
        if (file.thumbnailUrl == null) {
          throw Exception('File URL is not available');
        }
        final response = await http.get(Uri.parse(file.thumbnailUrl!));
        final appDir = await getApplicationDocumentsDirectory();
        final filePath = '${appDir.path}/${file.name}';
        final downloadedFile = File(filePath);
        await downloadedFile.writeAsBytes(response.bodyBytes);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File downloaded to: $filePath'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to download file: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: const Text('Files'),
            actions: [
              PopupMenuButton<FileSort>(
                initialValue: sortBy.value,
                onSelected: (value) => sortBy.value = value,
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: FileSort.nameAsc,
                    child: Text('Name (A-Z)'),
                  ),
                  const PopupMenuItem(
                    value: FileSort.nameDesc,
                    child: Text('Name (Z-A)'),
                  ),
                  const PopupMenuItem(
                    value: FileSort.dateAsc,
                    child: Text('Date (Oldest)'),
                  ),
                  const PopupMenuItem(
                    value: FileSort.dateDesc,
                    child: Text('Date (Newest)'),
                  ),
                  const PopupMenuItem(
                    value: FileSort.sizeAsc,
                    child: Text('Size (Smallest)'),
                  ),
                  const PopupMenuItem(
                    value: FileSort.sizeDesc,
                    child: Text('Size (Largest)'),
                  ),
                ],
              ),
            ],
          ),
          body: filesAsync.when(
            data: (files) => FileListWidget(
              files: files,
              sortBy: sortBy.value,
              onTap: (file) => selectedFile.value = file,
              onDelete: (file) async {
                try {
                  await ref.read(deleteFileProvider(file.id).future);
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to delete file: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              onShare: (file) => shareFile(file),
              onDownload: (file) => downloadFile(file),
            ),
            loading: () => const Center(
              child: CircularProgressIndicator(),
            ),
            error: (error, stack) => Center(
              child: Text('Error loading files: $error'),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => showUploadDialog.value = true,
            child: const Icon(Icons.add),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        ),
        if (selectedFile.value != null)
          Scaffold(
            appBar: AppBar(
              title: Text(selectedFile.value!.name),
              actions: [
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => selectedFile.value = null,
                ),
              ],
            ),
            body: FilePreviewWidget(
              filePath: selectedFile.value!.path,
              fileType: selectedFile.value!.mimeType,
            ),
          ),
        if (showUploadDialog.value)
          FileUploadDialog(
            userId: userId,
          ),
      ],
    );
  }
}
