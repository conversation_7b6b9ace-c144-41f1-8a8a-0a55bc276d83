import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/admin/admin_routes.dart';
import 'package:shivish/shared/models/config/app_config_model.dart';
import 'package:shivish/shared/services/config/app_config_service.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class AppConfigScreen extends ConsumerStatefulWidget {
  const AppConfigScreen({super.key});

  @override
  ConsumerState<AppConfigScreen> createState() => _AppConfigScreenState();
}

class _AppConfigScreenState extends ConsumerState<AppConfigScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _supportEmailController;
  late TextEditingController _placeholderImageUrlController;
  late TextEditingController _apiBaseUrlController;
  late TextEditingController _chatUrlController;
  late TextEditingController _paymentApiUrlController;

  @override
  void initState() {
    super.initState();
    _supportEmailController = TextEditingController();
    _placeholderImageUrlController = TextEditingController();
    _apiBaseUrlController = TextEditingController();
    _chatUrlController = TextEditingController();
    _paymentApiUrlController = TextEditingController();
  }

  @override
  void dispose() {
    _supportEmailController.dispose();
    _placeholderImageUrlController.dispose();
    _apiBaseUrlController.dispose();
    _chatUrlController.dispose();
    _paymentApiUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Configuration'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Check if we can pop the current route
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // Navigate back to settings screen using GoRouter
              context.go(AdminRoutes.settings);
            }
          },
        ),
      ),
      body: StreamBuilder<AppConfigModel>(
        stream: ref.read(appConfigServiceProvider).listenToConfig(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const LoadingIndicator();
          }

          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error: ${snapshot.error}',
                style: const TextStyle(color: Colors.red),
              ),
            );
          }

          final config = snapshot.data!;
          _supportEmailController.text = config.supportEmail;
          _placeholderImageUrlController.text = config.placeholderImageUrl;
          _apiBaseUrlController.text = config.apiBaseUrl;
          _chatUrlController.text = config.chatUrl;
          _paymentApiUrlController.text = config.paymentApiUrl;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Support Settings',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _supportEmailController,
                            decoration: const InputDecoration(
                              labelText: 'Support Email',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter support email';
                              }
                              if (!value.contains('@')) {
                                return 'Please enter a valid email';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _chatUrlController,
                            decoration: const InputDecoration(
                              labelText: 'Chat URL',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter chat URL';
                              }
                              if (!value.startsWith('http')) {
                                return 'Please enter a valid URL';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'API Settings',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _apiBaseUrlController,
                            decoration: const InputDecoration(
                              labelText: 'API Base URL',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter API base URL';
                              }
                              if (!value.startsWith('http')) {
                                return 'Please enter a valid URL';
                              }
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _paymentApiUrlController,
                            decoration: const InputDecoration(
                              labelText: 'Payment API URL',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter payment API URL';
                              }
                              if (!value.startsWith('http')) {
                                return 'Please enter a valid URL';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Media Settings',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _placeholderImageUrlController,
                            decoration: const InputDecoration(
                              labelText: 'Placeholder Image URL',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter placeholder image URL';
                              }
                              if (!value.startsWith('http')) {
                                return 'Please enter a valid URL';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  AppButton(
                    onPressed: _saveConfig,
                    child: const Text('Save Configuration'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final config = AppConfigModel(
        supportEmail: _supportEmailController.text,
        placeholderImageUrl: _placeholderImageUrlController.text,
        apiBaseUrl: _apiBaseUrlController.text,
        chatUrl: _chatUrlController.text,
        paymentApiUrl: _paymentApiUrlController.text,
      );

      await ref.read(appConfigServiceProvider).updateConfig(config);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration updated successfully'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
