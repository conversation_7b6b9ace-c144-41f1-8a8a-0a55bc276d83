import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_event.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_state.dart';
import 'package:shivish/shared/services/backup_service.dart';

@injectable
class BackupBloc extends Bloc<BackupEvent, BackupState> {
  final BackupService _backupService;

  BackupBloc(this._backupService) : super(const BackupState.initial()) {
    on<LoadBackups>(_onLoadBackups);
    on<CreateBackup>(_onCreateBackup);
    on<UpdateBackup>(_onUpdateBackup);
    on<DeleteBackup>(_onDeleteBackup);
    on<RestoreBackup>(_onRestoreBackup);
    on<ValidateBackup>(_onValidateBackup);
    on<ScheduleBackup>(_onScheduleBackup);
    on<CancelScheduledBackup>(_onCancelScheduledBackup);
    on<UpdateRetentionSettings>(_onUpdateRetentionSettings);
    on<UpdateCompressionSettings>(_onUpdateCompressionSettings);
    on<UpdateValidationSettings>(_onUpdateValidationSettings);
    on<UpdateNotificationSettings>(_onUpdateNotificationSettings);
    on<UpdateMonitoringSettings>(_onUpdateMonitoringSettings);
  }

  Future<void> _onLoadBackups(
      LoadBackups event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onCreateBackup(
      CreateBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.createBackup(event.backup);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateBackup(
      UpdateBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateBackup(event.backup);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onDeleteBackup(
      DeleteBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.deleteBackup(event.id);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onRestoreBackup(
      RestoreBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.restoreBackup(event.id);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onValidateBackup(
      ValidateBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.validateBackup(event.id);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onScheduleBackup(
      ScheduleBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.scheduleBackup(event.backup);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onCancelScheduledBackup(
      CancelScheduledBackup event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.cancelScheduledBackup(event.id);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateRetentionSettings(
      UpdateRetentionSettings event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateRetentionSettings(event.id, event.days);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateCompressionSettings(
      UpdateCompressionSettings event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateCompressionSettings(event.id, event.settings);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateValidationSettings(
      UpdateValidationSettings event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateValidationSettings(event.id, event.settings);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateNotificationSettings(
      UpdateNotificationSettings event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateNotificationSettings(event.id, event.settings);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }

  Future<void> _onUpdateMonitoringSettings(
      UpdateMonitoringSettings event, Emitter<BackupState> emit) async {
    try {
      emit(const BackupState.loading());
      await _backupService.updateMonitoringSettings(event.id, event.settings);
      final backups = await _backupService.getBackups();
      emit(BackupState.loaded(backups));
    } catch (e) {
      emit(BackupState.error(e.toString()));
    }
  }
}
