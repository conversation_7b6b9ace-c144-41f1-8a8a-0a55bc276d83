import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../../shared/models/order/order_model.dart';
import '../../../../../shared/providers/delivery_tracking_provider.dart';
import '../../../../../shared/widgets/maps/flutter_map_view.dart';
import '../../../../../shared/utils/logger.dart';

/// A bottom sheet that displays live tracking information for an order
class LiveTrackingBottomSheet extends ConsumerStatefulWidget {
  /// The order ID to track
  final String orderId;

  /// The order model
  final OrderModel order;

  const LiveTrackingBottomSheet({
    super.key,
    required this.orderId,
    required this.order,
  });

  @override
  ConsumerState<LiveTrackingBottomSheet> createState() => _LiveTrackingBottomSheetState();
}

class _LiveTrackingBottomSheetState extends ConsumerState<LiveTrackingBottomSheet> with SingleTickerProviderStateMixin {
  final logger = getLogger('LiveTrackingBottomSheet');
  final _mapController = Completer<MapController>();
  Timer? _refreshTimer;

  // Animation controller for the bottom sheet
  late AnimationController _controller;

  // Sheet states
  static const double _minHeight = 140.0;
  static const double _midHeight = 300.0;
  static const double _maxHeight = 500.0;
  double _currentHeight = _minHeight;

  @override
  void initState() {
    super.initState();

    // Initialize animation controller
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // Animation setup for future use if needed
    // Currently using _currentHeight for direct height control

    // Set up refresh timer to update tracking info every 10 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 10), (_) {
      if (mounted) {
        // Refresh the providers
        ref.invalidate(orderTrackingProvider(widget.orderId));
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  void _toggleSheetHeight() {
    setState(() {
      if (_currentHeight == _minHeight) {
        _currentHeight = _midHeight;
        _controller.animateTo(0.5);
      } else if (_currentHeight == _midHeight) {
        _currentHeight = _maxHeight;
        _controller.animateTo(1.0);
      } else {
        _currentHeight = _minHeight;
        _controller.animateTo(0.0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final trackingInfoAsync = ref.watch(orderTrackingProvider(widget.orderId));
    final liveTrackingInfo = ref.watch(liveDeliveryTrackingProvider(widget.orderId));

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          height: _currentHeight,
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 40),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle and header
              GestureDetector(
                onTap: _toggleSheetHeight,
                behavior: HitTestBehavior.opaque,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Column(
                    children: [
                      // Drag handle
                      Container(
                        width: 40,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Header with live indicator
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Live indicator dot
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: liveTrackingInfo?.isRecent == true ? Colors.green : Colors.grey,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Live Delivery Tracking',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              // Content based on sheet state
              Expanded(
                child: SingleChildScrollView(
                  physics: const ClampingScrollPhysics(),
                  child: Column(
                    children: [
                      // Delivery info summary (always visible)
                      _buildDeliverySummary(context, liveTrackingInfo),

                      // Map and detailed info (visible when expanded)
                      if (_currentHeight > _minHeight) ...[
                        const Divider(),
                        _buildMapView(context, trackingInfoAsync, liveTrackingInfo),
                      ],

                      // Additional details (visible when fully expanded)
                      if (_currentHeight == _maxHeight) ...[
                        const Divider(),
                        _buildDetailedInfo(context, widget.order, liveTrackingInfo),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDeliverySummary(BuildContext context, LiveTrackingInfo? liveTrackingInfo) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // ETA info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Estimated Arrival',
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                Text(
                  liveTrackingInfo?.formattedETA ?? 'Calculating...',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (liveTrackingInfo?.estimatedTimeOfArrival != null)
                  Text(
                    DateFormat('h:mm a').format(liveTrackingInfo!.estimatedTimeOfArrival!),
                    style: theme.textTheme.bodySmall,
                  ),
              ],
            ),
          ),

          // Distance info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Distance',
                  style: theme.textTheme.bodySmall,
                ),
                const SizedBox(height: 4),
                Text(
                  liveTrackingInfo?.formattedDistance ?? 'Calculating...',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (liveTrackingInfo?.timeInMinutes != null)
                  Text(
                    '${liveTrackingInfo!.timeInMinutes} min drive',
                    style: theme.textTheme.bodySmall,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapView(
    BuildContext context,
    AsyncValue<Map<String, dynamic>?> trackingInfoAsync,
    LiveTrackingInfo? liveTrackingInfo
  ) {
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 20),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Stack(
        children: [
          // Map
          trackingInfoAsync.when(
            data: (trackingInfo) {
              // Get delivery location from order
              final deliveryLocation = widget.order.deliveryAddress.latitude != null &&
                                      widget.order.deliveryAddress.longitude != null
                  ? latlong2.LatLng(
                      widget.order.deliveryAddress.latitude!,
                      widget.order.deliveryAddress.longitude!,
                    )
                  : null;

              // Get delivery person location from tracking info
              final deliveryPersonLocation = liveTrackingInfo?.deliveryPersonLocation;
              final isLiveTracking = liveTrackingInfo?.isLive ?? false;
              final isRecent = liveTrackingInfo?.isRecent ?? false;

              return FlutterMapView(
                pickupLocation: null, // We don't show pickup in this view
                dropLocation: deliveryLocation,
                currentLocation: deliveryPersonLocation,
                showRoute: true,
                fitToMarkers: true,
                initialZoom: 15,
                showCurrentLocation: true,
                followCurrentLocation: isLiveTracking && isRecent,
                onMapCreated: (controller) {
                  _mapController.complete(controller);
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stackTrace) {
              logger.severe('Error loading tracking info: $error\n$stackTrace');
              return Center(
                child: Text('Error loading map: $error'),
              );
            },
          ),

          // Live indicator overlay
          if (liveTrackingInfo?.isRecent == true && liveTrackingInfo?.deliveryPersonLocation != null)
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      'LIVE',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Map controls
          Positioned(
            right: 8,
            bottom: 8,
            child: Column(
              children: [
                FloatingActionButton.small(
                  heroTag: "recenterMap",
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black87,
                  elevation: 4,
                  onPressed: () {
                    if (liveTrackingInfo?.deliveryPersonLocation != null) {
                      _mapController.future.then((controller) {
                        controller.move(liveTrackingInfo!.deliveryPersonLocation!, 15);
                      });
                    }
                  },
                  child: const Icon(Icons.my_location, size: 18),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedInfo(
    BuildContext context,
    OrderModel order,
    LiveTrackingInfo? liveTrackingInfo
  ) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Delivery person info
          Text(
            'Delivery Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Delivery person card
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor: theme.colorScheme.primary.withValues(alpha: 30),
                  child: Icon(
                    Icons.delivery_dining,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),

                // Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Delivery Partner',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'On the way to your location',
                        style: theme.textTheme.bodyMedium,
                      ),
                      if (liveTrackingInfo?.lastUpdated != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Last updated: ${_formatLastUpdated(liveTrackingInfo!.lastUpdated!)}',
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ],
                  ),
                ),

                // Call button
                IconButton(
                  onPressed: () {
                    // Implement call functionality
                  },
                  icon: Icon(
                    Icons.phone,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Delivery address
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Delivery Address',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '${order.deliveryAddress.street}\n'
                  '${order.deliveryAddress.city}, ${order.deliveryAddress.state} ${order.deliveryAddress.postalCode}',
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Share location button
          if (liveTrackingInfo?.deliveryPersonLocation != null)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _shareDeliveryLocation(order, liveTrackingInfo!.deliveryPersonLocation!),
                icon: const Icon(Icons.share_location),
                label: const Text('Share Delivery Location'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inHours}h ${difference.inMinutes % 60}m ago';
    }
  }

  Future<void> _shareDeliveryLocation(OrderModel order, latlong2.LatLng location) async {
    try {
      // Create a shareable link with the current location
      final lat = location.latitude;
      final lng = location.longitude;
      final uri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$lat,$lng'
      );

      // Create a message to share
      final message = 'Track my order #${order.id}: $uri';

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: message));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tracking link copied to clipboard'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      logger.severe('Error sharing location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
