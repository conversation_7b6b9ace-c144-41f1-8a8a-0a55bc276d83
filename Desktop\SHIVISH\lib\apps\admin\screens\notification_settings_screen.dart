import 'package:flutter/material.dart';
import 'package:shivish/shared/services/messaging_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  late final MessagingService _messagingService;
  bool _orderNotifications = true;
  bool _promotionNotifications = true;
  bool _systemNotifications = true;
  bool _emailNotifications = true;
  bool _pushNotifications = true;
  bool _smsNotifications = true;
  String _notificationSound = 'Default';
  bool _vibrationEnabled = true;
  String _quietHoursStart = '22:00';
  String _quietHoursEnd = '07:00';

  @override
  void initState() {
    super.initState();
    _messagingService = MessagingService();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _orderNotifications = prefs.getBool('order_notifications') ?? true;
        _promotionNotifications =
            prefs.getBool('promotion_notifications') ?? true;
        _systemNotifications = prefs.getBool('system_notifications') ?? true;
        _emailNotifications = prefs.getBool('email_notifications') ?? true;
        _pushNotifications = prefs.getBool('push_notifications') ?? true;
        _smsNotifications = prefs.getBool('sms_notifications') ?? true;
        _notificationSound = prefs.getString('notification_sound') ?? 'Default';
        _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
        _quietHoursStart = prefs.getString('quiet_hours_start') ?? '22:00';
        _quietHoursEnd = prefs.getString('quiet_hours_end') ?? '07:00';
      });
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('order_notifications', _orderNotifications);
      await prefs.setBool('promotion_notifications', _promotionNotifications);
      await prefs.setBool('system_notifications', _systemNotifications);
      await prefs.setBool('email_notifications', _emailNotifications);
      await prefs.setBool('push_notifications', _pushNotifications);
      await prefs.setBool('sms_notifications', _smsNotifications);
      await prefs.setString('notification_sound', _notificationSound);
      await prefs.setBool('vibration_enabled', _vibrationEnabled);
      await prefs.setString('quiet_hours_start', _quietHoursStart);
      await prefs.setString('quiet_hours_end', _quietHoursEnd);

      // Update notification preferences in Firestore
      await FirebaseFirestore.instance.collection('users').doc('admin').update({
        'notification_preferences': {
          'order_notifications': _orderNotifications,
          'promotion_notifications': _promotionNotifications,
          'system_notifications': _systemNotifications,
          'email_notifications': _emailNotifications,
          'push_notifications': _pushNotifications,
          'sms_notifications': _smsNotifications,
          'notification_sound': _notificationSound,
          'vibration_enabled': _vibrationEnabled,
          'quiet_hours_start': _quietHoursStart,
          'quiet_hours_end': _quietHoursEnd,
        },
      });

      // Update foreground notification presentation options
      await _messagingService.setForegroundNotificationPresentationOptions(
        alert: _pushNotifications,
        badge: _pushNotifications,
        sound: _pushNotifications,
      );
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
    }
  }

  Future<void> _showNotificationSoundDialog() async {
    final sounds = ['Default', 'Soft', 'Loud', 'Custom'];
    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Notification Sound'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: sounds.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(sounds[index]),
                onTap: () => Navigator.pop(context, sounds[index]),
              );
            },
          ),
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _notificationSound = result;
      });
      await _saveSettings();
    }
  }

  Future<void> _showQuietHoursDialog() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Set Quiet Hours'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Start Time'),
              subtitle: Text(_quietHoursStart),
              onTap: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay(
                    hour: int.parse(_quietHoursStart.split(':')[0]),
                    minute: int.parse(_quietHoursStart.split(':')[1]),
                  ),
                );
                if (time != null) {
                  setState(() {
                    _quietHoursStart =
                        '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
                  });
                }
              },
            ),
            ListTile(
              title: const Text('End Time'),
              subtitle: Text(_quietHoursEnd),
              onTap: () async {
                final time = await showTimePicker(
                  context: context,
                  initialTime: TimeOfDay(
                    hour: int.parse(_quietHoursEnd.split(':')[0]),
                    minute: int.parse(_quietHoursEnd.split(':')[1]),
                  ),
                );
                if (time != null) {
                  setState(() {
                    _quietHoursEnd =
                        '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
                  });
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(
              context,
              {
                'start': _quietHoursStart,
                'end': _quietHoursEnd,
              },
            ),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      setState(() {
        _quietHoursStart = result['start']!;
        _quietHoursEnd = result['end']!;
      });
      await _saveSettings();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
      ),
      body: ListView(
        children: [
          _buildSection(
            context,
            'Notification Types',
            [
              SwitchListTile(
                title: const Text('Order Notifications'),
                subtitle: const Text('Get notified about order updates'),
                value: _orderNotifications,
                onChanged: (value) {
                  setState(() {
                    _orderNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Promotion Notifications'),
                subtitle: const Text('Receive promotional offers'),
                value: _promotionNotifications,
                onChanged: (value) {
                  setState(() {
                    _promotionNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('System Notifications'),
                subtitle: const Text('Important system updates'),
                value: _systemNotifications,
                onChanged: (value) {
                  setState(() {
                    _systemNotifications = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          _buildSection(
            context,
            'Delivery Methods',
            [
              SwitchListTile(
                title: const Text('Push Notifications'),
                subtitle: const Text('Receive notifications on this device'),
                value: _pushNotifications,
                onChanged: (value) {
                  setState(() {
                    _pushNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('Email Notifications'),
                subtitle: const Text('Receive notifications via email'),
                value: _emailNotifications,
                onChanged: (value) {
                  setState(() {
                    _emailNotifications = value;
                  });
                  _saveSettings();
                },
              ),
              SwitchListTile(
                title: const Text('SMS Notifications'),
                subtitle: const Text('Receive notifications via SMS'),
                value: _smsNotifications,
                onChanged: (value) {
                  setState(() {
                    _smsNotifications = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          _buildSection(
            context,
            'Sound & Vibration',
            [
              ListTile(
                title: const Text('Notification Sound'),
                subtitle: Text(_notificationSound),
                trailing: const Icon(Icons.chevron_right),
                onTap: _showNotificationSoundDialog,
              ),
              SwitchListTile(
                title: const Text('Vibration'),
                subtitle: const Text('Vibrate when receiving notifications'),
                value: _vibrationEnabled,
                onChanged: (value) {
                  setState(() {
                    _vibrationEnabled = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          _buildSection(
            context,
            'Quiet Hours',
            [
              ListTile(
                title: const Text('Quiet Hours'),
                subtitle: Text('$_quietHoursStart - $_quietHoursEnd'),
                trailing: const Icon(Icons.chevron_right),
                onTap: _showQuietHoursDialog,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        ...children,
      ],
    );
  }

}
