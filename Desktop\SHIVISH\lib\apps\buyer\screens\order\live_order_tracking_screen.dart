import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:latlong2/latlong.dart' as latlong2;
import 'package:intl/intl.dart';
import '../../../../../shared/models/order/order_model.dart';
import '../../../../../shared/providers/order_provider.dart';
import '../../../../../shared/providers/delivery_tracking_provider.dart';
import '../../../../../shared/ui_components/dialogs/contact_support_dialog.dart';
import '../../../../../shared/utils/logger.dart';
import '../../../../../shared/widgets/maps/flutter_map_view.dart';
import '../../../../../shared/ui_components/navigation/back_button_handler.dart';

class LiveOrderTrackingScreen extends ConsumerStatefulWidget {
  final String orderId;

  const LiveOrderTrackingScreen({
    super.key,
    required this.orderId,
  });

  @override
  ConsumerState<LiveOrderTrackingScreen> createState() => _LiveOrderTrackingScreenState();
}

class _LiveOrderTrackingScreenState extends ConsumerState<LiveOrderTrackingScreen> {
  final logger = getLogger('LiveOrderTrackingScreen');
  Timer? _refreshTimer;
  final _mapController = Completer<MapController>();

  @override
  void initState() {
    super.initState();

    // Set up refresh timer to update tracking info every 15 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 15), (_) {
      if (mounted) {
        // Refresh the providers
        ref.invalidate(orderStreamProvider(widget.orderId));
        ref.invalidate(orderTrackingProvider(widget.orderId));
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _showContactSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ContactSupportDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final orderAsync = ref.watch(orderStreamProvider(widget.orderId));
    final trackingInfoAsync = ref.watch(orderTrackingProvider(widget.orderId));
    final liveTrackingInfo = ref.watch(liveDeliveryTrackingProvider(widget.orderId));
    final theme = Theme.of(context);

    return BackButtonHandler(
      fallbackRoute: '/buyer/orders',
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Live Order Tracking'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                // Refresh the providers
                ref.invalidate(orderStreamProvider(widget.orderId));
                ref.invalidate(orderTrackingProvider(widget.orderId));
              },
              tooltip: 'Refresh tracking',
            ),
            IconButton(
              icon: const Icon(Icons.support_agent),
              onPressed: () => _showContactSupport(context),
            ),
          ],
        ),
        body: orderAsync.when(
          data: (order) {
            return trackingInfoAsync.when(
              data: (trackingInfo) {
                return _buildOrderTracking(context, order, trackingInfo, liveTrackingInfo);
              },
              loading: () => _buildOrderTracking(context, order, null, liveTrackingInfo),
              error: (error, stackTrace) {
                logger.severe('Error loading tracking info: $error\n$stackTrace');
                return _buildOrderTracking(context, order, null, liveTrackingInfo);
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stackTrace) {
            logger.severe(
                'Error loading order tracking\nError: $error\nStack trace: $stackTrace');
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading order tracking\n$error',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showContactSupport(context),
                    child: const Text('Contact Support'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOrderTracking(
    BuildContext context,
    OrderModel order,
    Map<String, dynamic>? trackingInfo,
    LiveTrackingInfo? liveTrackingInfo
  ) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOrderDetails(order),
          _buildLiveMapWithTracking(order, trackingInfo, liveTrackingInfo),
          _buildLiveTrackingInfo(context, order, liveTrackingInfo),
          _buildTimeline(order),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(OrderModel order) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order #${order.id}',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${order.items.length} items • Total: ₹${order.total.toStringAsFixed(2)}',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          const Text(
            'Delivery Address:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${order.deliveryAddress.street}\n'
            '${order.deliveryAddress.city}, ${order.deliveryAddress.state} ${order.deliveryAddress.postalCode}\n'
            '${order.deliveryAddress.country}',
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildLiveMapWithTracking(
    OrderModel order,
    Map<String, dynamic>? trackingInfo,
    LiveTrackingInfo? liveTrackingInfo
  ) {
    if (order.deliveryAddress.latitude == null ||
        order.deliveryAddress.longitude == null) {
      return const SizedBox.shrink();
    }

    // Get delivery location from order
    final deliveryLocation = latlong2.LatLng(
      order.deliveryAddress.latitude!,
      order.deliveryAddress.longitude!,
    );

    // Get delivery person location from live tracking info
    final deliveryPersonLocation = liveTrackingInfo?.deliveryPersonLocation;
    final isLiveTracking = liveTrackingInfo?.isLive ?? false;
    final isRecent = liveTrackingInfo?.isRecent ?? false;
    final lastUpdated = liveTrackingInfo?.lastUpdated;

    return SizedBox(
      height: 300, // Increased height for better visibility
      child: Stack(
        children: [
          FlutterMapView(
            pickupLocation: liveTrackingInfo?.deliveryLocation,
            dropLocation: deliveryLocation,
            currentLocation: deliveryPersonLocation,
            showRoute: true,
            fitToMarkers: true,
            initialZoom: 15,
            showCurrentLocation: false,
            followCurrentLocation: isLiveTracking && isRecent,
            onMapCreated: (controller) {
              _mapController.complete(controller);
            },
          ),

          // Live tracking indicator
          if (isLiveTracking && isRecent && deliveryPersonLocation != null)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Live Tracking',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Last updated indicator
          if (lastUpdated != null && deliveryPersonLocation != null)
            Positioned(
              top: isLiveTracking && isRecent ? 60 : 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'Updated: ${_formatLastUpdated(lastUpdated)}',
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _formatLastUpdated(DateTime lastUpdated) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else {
      return '${difference.inHours}h ${difference.inMinutes % 60}m ago';
    }
  }

  Future<void> _shareDeliveryLocation(OrderModel order, latlong2.LatLng location) async {
    try {
      // Create a shareable link with the current location
      final lat = location.latitude;
      final lng = location.longitude;
      final uri = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$lat,$lng'
      );

      // Create a message to share
      final message = 'Track my order #${order.id}: $uri';

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: message));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tracking link copied to clipboard'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      logger.severe('Error sharing location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildLiveTrackingInfo(
    BuildContext context,
    OrderModel order,
    LiveTrackingInfo? liveTrackingInfo
  ) {
    if (liveTrackingInfo == null ||
        liveTrackingInfo.deliveryPersonLocation == null) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.delivery_dining,
                    color: theme.colorScheme.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Live Delivery Tracking',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // ETA information
              if (liveTrackingInfo.estimatedTimeOfArrival != null) ...[
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 30),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.access_time,
                        color: theme.colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Estimated Time of Arrival',
                            style: theme.textTheme.bodyLarge,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            liveTrackingInfo.formattedETA,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            DateFormat('EEEE, MMMM d • h:mm a')
                                .format(liveTrackingInfo.estimatedTimeOfArrival!),
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],

              // Distance information
              if (liveTrackingInfo.distanceInKm != null) ...[
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.secondary.withValues(alpha: 30),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.directions,
                        color: theme.colorScheme.secondary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Distance',
                            style: theme.textTheme.bodyLarge,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            liveTrackingInfo.formattedDistance,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (liveTrackingInfo.timeInMinutes != null)
                            Text(
                              'Approximately ${liveTrackingInfo.timeInMinutes} min drive',
                              style: theme.textTheme.bodySmall,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],

              // Share location button
              Center(
                child: ElevatedButton.icon(
                  onPressed: () => _shareDeliveryLocation(
                    order,
                    liveTrackingInfo.deliveryPersonLocation!
                  ),
                  icon: const Icon(Icons.share_location),
                  label: const Text('Share Delivery Location'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTimeline(OrderModel order) {
    final theme = Theme.of(context);

    // Define the timeline steps based on order status
    final steps = [
      {
        'title': 'Order Placed',
        'subtitle': _formatDate(order.createdAt),
        'icon': Icons.shopping_bag,
        'isCompleted': true,
      },
      {
        'title': 'Order Confirmed',
        'subtitle': order.status.index >= OrderStatus.confirmed.index
            ? _formatDate(order.updatedAt)
            : 'Pending',
        'icon': Icons.check_circle,
        'isCompleted': order.status.index >= OrderStatus.confirmed.index,
      },
      {
        'title': 'Processing',
        'subtitle': order.status.index >= OrderStatus.processing.index
            ? _formatDate(order.updatedAt)
            : 'Pending',
        'icon': Icons.inventory,
        'isCompleted': order.status.index >= OrderStatus.processing.index,
      },
      {
        'title': 'Out for Delivery',
        'subtitle': order.status.index >= OrderStatus.outForDelivery.index
            ? _formatDate(order.updatedAt)
            : 'Pending',
        'icon': Icons.local_shipping,
        'isCompleted': order.status.index >= OrderStatus.outForDelivery.index,
      },
      {
        'title': 'Delivered',
        'subtitle': order.status.index >= OrderStatus.delivered.index
            ? _formatDate(order.updatedAt)
            : 'Pending',
        'icon': Icons.home,
        'isCompleted': order.status.index >= OrderStatus.delivered.index,
      },
    ];

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Timeline',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: steps.length,
            itemBuilder: (context, index) {
              final step = steps[index];
              final isCompleted = step['isCompleted'] as bool;
              final isLast = index == steps.length - 1;

              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: isCompleted
                              ? theme.colorScheme.primary
                              : theme.colorScheme.surface,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          step['icon'] as IconData,
                          color: isCompleted
                              ? theme.colorScheme.onPrimary
                              : theme.colorScheme.onSurface,
                          size: 16,
                        ),
                      ),
                      if (!isLast)
                        Container(
                          width: 2,
                          height: 40,
                          color: isCompleted
                              ? theme.colorScheme.primary
                              : theme.colorScheme.surface,
                        ),
                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          step['title'] as String,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isCompleted
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          step['subtitle'] as String,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        if (!isLast) const SizedBox(height: 24),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return DateFormat('MMM d, yyyy • h:mm a').format(date);
  }
}
