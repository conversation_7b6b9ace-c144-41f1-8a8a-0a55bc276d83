// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'voice_command_training_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$VoiceCommandTrainingEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceCommandTrainingEventCopyWith<$Res> {
  factory $VoiceCommandTrainingEventCopyWith(VoiceCommandTrainingEvent value,
          $Res Function(VoiceCommandTrainingEvent) then) =
      _$VoiceCommandTrainingEventCopyWithImpl<$Res, VoiceCommandTrainingEvent>;
}

/// @nodoc
class _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        $Val extends VoiceCommandTrainingEvent>
    implements $VoiceCommandTrainingEventCopyWith<$Res> {
  _$VoiceCommandTrainingEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadVoiceCommandTrainingsImplCopyWith<$Res> {
  factory _$$LoadVoiceCommandTrainingsImplCopyWith(
          _$LoadVoiceCommandTrainingsImpl value,
          $Res Function(_$LoadVoiceCommandTrainingsImpl) then) =
      __$$LoadVoiceCommandTrainingsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadVoiceCommandTrainingsImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        _$LoadVoiceCommandTrainingsImpl>
    implements _$$LoadVoiceCommandTrainingsImplCopyWith<$Res> {
  __$$LoadVoiceCommandTrainingsImplCopyWithImpl(
      _$LoadVoiceCommandTrainingsImpl _value,
      $Res Function(_$LoadVoiceCommandTrainingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadVoiceCommandTrainingsImpl implements LoadVoiceCommandTrainings {
  const _$LoadVoiceCommandTrainingsImpl();

  @override
  String toString() {
    return 'VoiceCommandTrainingEvent.loadVoiceCommandTrainings()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadVoiceCommandTrainingsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) {
    return loadVoiceCommandTrainings();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) {
    return loadVoiceCommandTrainings?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (loadVoiceCommandTrainings != null) {
      return loadVoiceCommandTrainings();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) {
    return loadVoiceCommandTrainings(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) {
    return loadVoiceCommandTrainings?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (loadVoiceCommandTrainings != null) {
      return loadVoiceCommandTrainings(this);
    }
    return orElse();
  }
}

abstract class LoadVoiceCommandTrainings implements VoiceCommandTrainingEvent {
  const factory LoadVoiceCommandTrainings() = _$LoadVoiceCommandTrainingsImpl;
}

/// @nodoc
abstract class _$$CreateVoiceCommandTrainingImplCopyWith<$Res> {
  factory _$$CreateVoiceCommandTrainingImplCopyWith(
          _$CreateVoiceCommandTrainingImpl value,
          $Res Function(_$CreateVoiceCommandTrainingImpl) then) =
      __$$CreateVoiceCommandTrainingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VoiceCommandTraining training});

  $VoiceCommandTrainingCopyWith<$Res> get training;
}

/// @nodoc
class __$$CreateVoiceCommandTrainingImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        _$CreateVoiceCommandTrainingImpl>
    implements _$$CreateVoiceCommandTrainingImplCopyWith<$Res> {
  __$$CreateVoiceCommandTrainingImplCopyWithImpl(
      _$CreateVoiceCommandTrainingImpl _value,
      $Res Function(_$CreateVoiceCommandTrainingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? training = null,
  }) {
    return _then(_$CreateVoiceCommandTrainingImpl(
      null == training
          ? _value.training
          : training // ignore: cast_nullable_to_non_nullable
              as VoiceCommandTraining,
    ));
  }

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoiceCommandTrainingCopyWith<$Res> get training {
    return $VoiceCommandTrainingCopyWith<$Res>(_value.training, (value) {
      return _then(_value.copyWith(training: value));
    });
  }
}

/// @nodoc

class _$CreateVoiceCommandTrainingImpl implements CreateVoiceCommandTraining {
  const _$CreateVoiceCommandTrainingImpl(this.training);

  @override
  final VoiceCommandTraining training;

  @override
  String toString() {
    return 'VoiceCommandTrainingEvent.createVoiceCommandTraining(training: $training)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateVoiceCommandTrainingImpl &&
            (identical(other.training, training) ||
                other.training == training));
  }

  @override
  int get hashCode => Object.hash(runtimeType, training);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateVoiceCommandTrainingImplCopyWith<_$CreateVoiceCommandTrainingImpl>
      get copyWith => __$$CreateVoiceCommandTrainingImplCopyWithImpl<
          _$CreateVoiceCommandTrainingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) {
    return createVoiceCommandTraining(training);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) {
    return createVoiceCommandTraining?.call(training);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (createVoiceCommandTraining != null) {
      return createVoiceCommandTraining(training);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) {
    return createVoiceCommandTraining(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) {
    return createVoiceCommandTraining?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (createVoiceCommandTraining != null) {
      return createVoiceCommandTraining(this);
    }
    return orElse();
  }
}

abstract class CreateVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const factory CreateVoiceCommandTraining(
      final VoiceCommandTraining training) = _$CreateVoiceCommandTrainingImpl;

  VoiceCommandTraining get training;

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateVoiceCommandTrainingImplCopyWith<_$CreateVoiceCommandTrainingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVoiceCommandTrainingImplCopyWith<$Res> {
  factory _$$UpdateVoiceCommandTrainingImplCopyWith(
          _$UpdateVoiceCommandTrainingImpl value,
          $Res Function(_$UpdateVoiceCommandTrainingImpl) then) =
      __$$UpdateVoiceCommandTrainingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({VoiceCommandTraining training});

  $VoiceCommandTrainingCopyWith<$Res> get training;
}

/// @nodoc
class __$$UpdateVoiceCommandTrainingImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        _$UpdateVoiceCommandTrainingImpl>
    implements _$$UpdateVoiceCommandTrainingImplCopyWith<$Res> {
  __$$UpdateVoiceCommandTrainingImplCopyWithImpl(
      _$UpdateVoiceCommandTrainingImpl _value,
      $Res Function(_$UpdateVoiceCommandTrainingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? training = null,
  }) {
    return _then(_$UpdateVoiceCommandTrainingImpl(
      null == training
          ? _value.training
          : training // ignore: cast_nullable_to_non_nullable
              as VoiceCommandTraining,
    ));
  }

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VoiceCommandTrainingCopyWith<$Res> get training {
    return $VoiceCommandTrainingCopyWith<$Res>(_value.training, (value) {
      return _then(_value.copyWith(training: value));
    });
  }
}

/// @nodoc

class _$UpdateVoiceCommandTrainingImpl implements UpdateVoiceCommandTraining {
  const _$UpdateVoiceCommandTrainingImpl(this.training);

  @override
  final VoiceCommandTraining training;

  @override
  String toString() {
    return 'VoiceCommandTrainingEvent.updateVoiceCommandTraining(training: $training)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVoiceCommandTrainingImpl &&
            (identical(other.training, training) ||
                other.training == training));
  }

  @override
  int get hashCode => Object.hash(runtimeType, training);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVoiceCommandTrainingImplCopyWith<_$UpdateVoiceCommandTrainingImpl>
      get copyWith => __$$UpdateVoiceCommandTrainingImplCopyWithImpl<
          _$UpdateVoiceCommandTrainingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTraining(training);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTraining?.call(training);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandTraining != null) {
      return updateVoiceCommandTraining(training);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTraining(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTraining?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandTraining != null) {
      return updateVoiceCommandTraining(this);
    }
    return orElse();
  }
}

abstract class UpdateVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const factory UpdateVoiceCommandTraining(
      final VoiceCommandTraining training) = _$UpdateVoiceCommandTrainingImpl;

  VoiceCommandTraining get training;

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateVoiceCommandTrainingImplCopyWith<_$UpdateVoiceCommandTrainingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteVoiceCommandTrainingImplCopyWith<$Res> {
  factory _$$DeleteVoiceCommandTrainingImplCopyWith(
          _$DeleteVoiceCommandTrainingImpl value,
          $Res Function(_$DeleteVoiceCommandTrainingImpl) then) =
      __$$DeleteVoiceCommandTrainingImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteVoiceCommandTrainingImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        _$DeleteVoiceCommandTrainingImpl>
    implements _$$DeleteVoiceCommandTrainingImplCopyWith<$Res> {
  __$$DeleteVoiceCommandTrainingImplCopyWithImpl(
      _$DeleteVoiceCommandTrainingImpl _value,
      $Res Function(_$DeleteVoiceCommandTrainingImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteVoiceCommandTrainingImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteVoiceCommandTrainingImpl implements DeleteVoiceCommandTraining {
  const _$DeleteVoiceCommandTrainingImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'VoiceCommandTrainingEvent.deleteVoiceCommandTraining(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteVoiceCommandTrainingImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteVoiceCommandTrainingImplCopyWith<_$DeleteVoiceCommandTrainingImpl>
      get copyWith => __$$DeleteVoiceCommandTrainingImplCopyWithImpl<
          _$DeleteVoiceCommandTrainingImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) {
    return deleteVoiceCommandTraining(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) {
    return deleteVoiceCommandTraining?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (deleteVoiceCommandTraining != null) {
      return deleteVoiceCommandTraining(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) {
    return deleteVoiceCommandTraining(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) {
    return deleteVoiceCommandTraining?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (deleteVoiceCommandTraining != null) {
      return deleteVoiceCommandTraining(this);
    }
    return orElse();
  }
}

abstract class DeleteVoiceCommandTraining implements VoiceCommandTrainingEvent {
  const factory DeleteVoiceCommandTraining(final String id) =
      _$DeleteVoiceCommandTrainingImpl;

  String get id;

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteVoiceCommandTrainingImplCopyWith<_$DeleteVoiceCommandTrainingImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateVoiceCommandTrainingStatusImplCopyWith<$Res> {
  factory _$$UpdateVoiceCommandTrainingStatusImplCopyWith(
          _$UpdateVoiceCommandTrainingStatusImpl value,
          $Res Function(_$UpdateVoiceCommandTrainingStatusImpl) then) =
      __$$UpdateVoiceCommandTrainingStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, bool isEnabled});
}

/// @nodoc
class __$$UpdateVoiceCommandTrainingStatusImplCopyWithImpl<$Res>
    extends _$VoiceCommandTrainingEventCopyWithImpl<$Res,
        _$UpdateVoiceCommandTrainingStatusImpl>
    implements _$$UpdateVoiceCommandTrainingStatusImplCopyWith<$Res> {
  __$$UpdateVoiceCommandTrainingStatusImplCopyWithImpl(
      _$UpdateVoiceCommandTrainingStatusImpl _value,
      $Res Function(_$UpdateVoiceCommandTrainingStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isEnabled = null,
  }) {
    return _then(_$UpdateVoiceCommandTrainingStatusImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == isEnabled
          ? _value.isEnabled
          : isEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$UpdateVoiceCommandTrainingStatusImpl
    implements UpdateVoiceCommandTrainingStatus {
  const _$UpdateVoiceCommandTrainingStatusImpl(this.id, this.isEnabled);

  @override
  final String id;
  @override
  final bool isEnabled;

  @override
  String toString() {
    return 'VoiceCommandTrainingEvent.updateVoiceCommandTrainingStatus(id: $id, isEnabled: $isEnabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateVoiceCommandTrainingStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isEnabled, isEnabled) ||
                other.isEnabled == isEnabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, isEnabled);

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateVoiceCommandTrainingStatusImplCopyWith<
          _$UpdateVoiceCommandTrainingStatusImpl>
      get copyWith => __$$UpdateVoiceCommandTrainingStatusImplCopyWithImpl<
          _$UpdateVoiceCommandTrainingStatusImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadVoiceCommandTrainings,
    required TResult Function(VoiceCommandTraining training)
        createVoiceCommandTraining,
    required TResult Function(VoiceCommandTraining training)
        updateVoiceCommandTraining,
    required TResult Function(String id) deleteVoiceCommandTraining,
    required TResult Function(String id, bool isEnabled)
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTrainingStatus(id, isEnabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadVoiceCommandTrainings,
    TResult? Function(VoiceCommandTraining training)?
        createVoiceCommandTraining,
    TResult? Function(VoiceCommandTraining training)?
        updateVoiceCommandTraining,
    TResult? Function(String id)? deleteVoiceCommandTraining,
    TResult? Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTrainingStatus?.call(id, isEnabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadVoiceCommandTrainings,
    TResult Function(VoiceCommandTraining training)? createVoiceCommandTraining,
    TResult Function(VoiceCommandTraining training)? updateVoiceCommandTraining,
    TResult Function(String id)? deleteVoiceCommandTraining,
    TResult Function(String id, bool isEnabled)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandTrainingStatus != null) {
      return updateVoiceCommandTrainingStatus(id, isEnabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadVoiceCommandTrainings value)
        loadVoiceCommandTrainings,
    required TResult Function(CreateVoiceCommandTraining value)
        createVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTraining value)
        updateVoiceCommandTraining,
    required TResult Function(DeleteVoiceCommandTraining value)
        deleteVoiceCommandTraining,
    required TResult Function(UpdateVoiceCommandTrainingStatus value)
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTrainingStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult? Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult? Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult? Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
  }) {
    return updateVoiceCommandTrainingStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadVoiceCommandTrainings value)?
        loadVoiceCommandTrainings,
    TResult Function(CreateVoiceCommandTraining value)?
        createVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTraining value)?
        updateVoiceCommandTraining,
    TResult Function(DeleteVoiceCommandTraining value)?
        deleteVoiceCommandTraining,
    TResult Function(UpdateVoiceCommandTrainingStatus value)?
        updateVoiceCommandTrainingStatus,
    required TResult orElse(),
  }) {
    if (updateVoiceCommandTrainingStatus != null) {
      return updateVoiceCommandTrainingStatus(this);
    }
    return orElse();
  }
}

abstract class UpdateVoiceCommandTrainingStatus
    implements VoiceCommandTrainingEvent {
  const factory UpdateVoiceCommandTrainingStatus(
          final String id, final bool isEnabled) =
      _$UpdateVoiceCommandTrainingStatusImpl;

  String get id;
  bool get isEnabled;

  /// Create a copy of VoiceCommandTrainingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateVoiceCommandTrainingStatusImplCopyWith<
          _$UpdateVoiceCommandTrainingStatusImpl>
      get copyWith => throw _privateConstructorUsedError;
}
