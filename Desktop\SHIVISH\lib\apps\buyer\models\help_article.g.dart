// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'help_article.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HelpArticleImpl _$$HelpArticleImplFromJson(Map<String, dynamic> json) =>
    _$HelpArticleImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      question: json['question'] as String,
      answer: json['answer'] as String,
      status: json['status'] as String,
      date: json['date'] as String,
      description: json['description'] as String?,
      updates: (json['updates'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$$HelpArticleImplToJson(_$HelpArticleImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'question': instance.question,
      'answer': instance.answer,
      'status': instance.status,
      'date': instance.date,
      'description': instance.description,
      'updates': instance.updates,
    };
