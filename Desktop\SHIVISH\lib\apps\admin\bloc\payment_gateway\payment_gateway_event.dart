import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/payment_gateway.dart';

part 'payment_gateway_event.freezed.dart';

@freezed
class PaymentGatewayEvent with _$PaymentGatewayEvent {
  const factory PaymentGatewayEvent.loadGateways() = LoadGateways;
  const factory PaymentGatewayEvent.createPaymentGateway(
      PaymentGateway gateway) = CreatePaymentGateway;
  const factory PaymentGatewayEvent.updatePaymentGateway(
      PaymentGateway gateway) = UpdatePaymentGateway;
  const factory PaymentGatewayEvent.deletePaymentGateway(String id) =
      DeletePaymentGateway;
  const factory PaymentGatewayEvent.updateGatewayStatus(
      String id, bool isActive) = UpdateGatewayStatus;
  const factory PaymentGatewayEvent.updateGatewayCredentials(
      String id, Map<String, dynamic> credentials) = UpdateGatewayCredentials;
  const factory PaymentGatewayEvent.updateGatewayFees(
      String id, Map<String, dynamic> fees) = UpdateGatewayFees;
  const factory PaymentGatewayEvent.calculateTransactionFee(
      String id, double amount) = CalculateTransactionFee;
}
