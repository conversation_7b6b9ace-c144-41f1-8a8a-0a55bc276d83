// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_event_management_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AdminEventManagementEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminEventManagementEventCopyWith<$Res> {
  factory $AdminEventManagementEventCopyWith(AdminEventManagementEvent value,
          $Res Function(AdminEventManagementEvent) then) =
      _$AdminEventManagementEventCopyWithImpl<$Res, AdminEventManagementEvent>;
}

/// @nodoc
class _$AdminEventManagementEventCopyWithImpl<$Res,
        $Val extends AdminEventManagementEvent>
    implements $AdminEventManagementEventCopyWith<$Res> {
  _$AdminEventManagementEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadPendingEventsImplCopyWith<$Res> {
  factory _$$LoadPendingEventsImplCopyWith(_$LoadPendingEventsImpl value,
          $Res Function(_$LoadPendingEventsImpl) then) =
      __$$LoadPendingEventsImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadPendingEventsImplCopyWithImpl<$Res>
    extends _$AdminEventManagementEventCopyWithImpl<$Res,
        _$LoadPendingEventsImpl>
    implements _$$LoadPendingEventsImplCopyWith<$Res> {
  __$$LoadPendingEventsImplCopyWithImpl(_$LoadPendingEventsImpl _value,
      $Res Function(_$LoadPendingEventsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadPendingEventsImpl implements LoadPendingEvents {
  const _$LoadPendingEventsImpl();

  @override
  String toString() {
    return 'AdminEventManagementEvent.loadPendingEvents()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadPendingEventsImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) {
    return loadPendingEvents();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) {
    return loadPendingEvents?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) {
    if (loadPendingEvents != null) {
      return loadPendingEvents();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) {
    return loadPendingEvents(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) {
    return loadPendingEvents?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) {
    if (loadPendingEvents != null) {
      return loadPendingEvents(this);
    }
    return orElse();
  }
}

abstract class LoadPendingEvents implements AdminEventManagementEvent {
  const factory LoadPendingEvents() = _$LoadPendingEventsImpl;
}

/// @nodoc
abstract class _$$ApproveEventImplCopyWith<$Res> {
  factory _$$ApproveEventImplCopyWith(
          _$ApproveEventImpl value, $Res Function(_$ApproveEventImpl) then) =
      __$$ApproveEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId, String adminId});
}

/// @nodoc
class __$$ApproveEventImplCopyWithImpl<$Res>
    extends _$AdminEventManagementEventCopyWithImpl<$Res, _$ApproveEventImpl>
    implements _$$ApproveEventImplCopyWith<$Res> {
  __$$ApproveEventImplCopyWithImpl(
      _$ApproveEventImpl _value, $Res Function(_$ApproveEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventId = null,
    Object? adminId = null,
  }) {
    return _then(_$ApproveEventImpl(
      eventId: null == eventId
          ? _value.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ApproveEventImpl implements ApproveEvent {
  const _$ApproveEventImpl({required this.eventId, required this.adminId});

  @override
  final String eventId;
  @override
  final String adminId;

  @override
  String toString() {
    return 'AdminEventManagementEvent.approveEvent(eventId: $eventId, adminId: $adminId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ApproveEventImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId, adminId);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ApproveEventImplCopyWith<_$ApproveEventImpl> get copyWith =>
      __$$ApproveEventImplCopyWithImpl<_$ApproveEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) {
    return approveEvent(eventId, adminId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) {
    return approveEvent?.call(eventId, adminId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) {
    if (approveEvent != null) {
      return approveEvent(eventId, adminId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) {
    return approveEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) {
    return approveEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) {
    if (approveEvent != null) {
      return approveEvent(this);
    }
    return orElse();
  }
}

abstract class ApproveEvent implements AdminEventManagementEvent {
  const factory ApproveEvent(
      {required final String eventId,
      required final String adminId}) = _$ApproveEventImpl;

  String get eventId;
  String get adminId;

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ApproveEventImplCopyWith<_$ApproveEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RejectEventImplCopyWith<$Res> {
  factory _$$RejectEventImplCopyWith(
          _$RejectEventImpl value, $Res Function(_$RejectEventImpl) then) =
      __$$RejectEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String eventId, String adminId, String reason});
}

/// @nodoc
class __$$RejectEventImplCopyWithImpl<$Res>
    extends _$AdminEventManagementEventCopyWithImpl<$Res, _$RejectEventImpl>
    implements _$$RejectEventImplCopyWith<$Res> {
  __$$RejectEventImplCopyWithImpl(
      _$RejectEventImpl _value, $Res Function(_$RejectEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? eventId = null,
    Object? adminId = null,
    Object? reason = null,
  }) {
    return _then(_$RejectEventImpl(
      eventId: null == eventId
          ? _value.eventId
          : eventId // ignore: cast_nullable_to_non_nullable
              as String,
      adminId: null == adminId
          ? _value.adminId
          : adminId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: null == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$RejectEventImpl implements RejectEvent {
  const _$RejectEventImpl(
      {required this.eventId, required this.adminId, required this.reason});

  @override
  final String eventId;
  @override
  final String adminId;
  @override
  final String reason;

  @override
  String toString() {
    return 'AdminEventManagementEvent.rejectEvent(eventId: $eventId, adminId: $adminId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RejectEventImpl &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.adminId, adminId) || other.adminId == adminId) &&
            (identical(other.reason, reason) || other.reason == reason));
  }

  @override
  int get hashCode => Object.hash(runtimeType, eventId, adminId, reason);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RejectEventImplCopyWith<_$RejectEventImpl> get copyWith =>
      __$$RejectEventImplCopyWithImpl<_$RejectEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) {
    return rejectEvent(eventId, adminId, reason);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) {
    return rejectEvent?.call(eventId, adminId, reason);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) {
    if (rejectEvent != null) {
      return rejectEvent(eventId, adminId, reason);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) {
    return rejectEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) {
    return rejectEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) {
    if (rejectEvent != null) {
      return rejectEvent(this);
    }
    return orElse();
  }
}

abstract class RejectEvent implements AdminEventManagementEvent {
  const factory RejectEvent(
      {required final String eventId,
      required final String adminId,
      required final String reason}) = _$RejectEventImpl;

  String get eventId;
  String get adminId;
  String get reason;

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RejectEventImplCopyWith<_$RejectEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateEventImplCopyWith<$Res> {
  factory _$$CreateEventImplCopyWith(
          _$CreateEventImpl value, $Res Function(_$CreateEventImpl) then) =
      __$$CreateEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EventModel event});

  $EventModelCopyWith<$Res> get event;
}

/// @nodoc
class __$$CreateEventImplCopyWithImpl<$Res>
    extends _$AdminEventManagementEventCopyWithImpl<$Res, _$CreateEventImpl>
    implements _$$CreateEventImplCopyWith<$Res> {
  __$$CreateEventImplCopyWithImpl(
      _$CreateEventImpl _value, $Res Function(_$CreateEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? event = null,
  }) {
    return _then(_$CreateEventImpl(
      event: null == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as EventModel,
    ));
  }

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventModelCopyWith<$Res> get event {
    return $EventModelCopyWith<$Res>(_value.event, (value) {
      return _then(_value.copyWith(event: value));
    });
  }
}

/// @nodoc

class _$CreateEventImpl implements CreateEvent {
  const _$CreateEventImpl({required this.event});

  @override
  final EventModel event;

  @override
  String toString() {
    return 'AdminEventManagementEvent.createEvent(event: $event)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateEventImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateEventImplCopyWith<_$CreateEventImpl> get copyWith =>
      __$$CreateEventImplCopyWithImpl<_$CreateEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) {
    return createEvent(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) {
    return createEvent?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) {
    if (createEvent != null) {
      return createEvent(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) {
    return createEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) {
    return createEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) {
    if (createEvent != null) {
      return createEvent(this);
    }
    return orElse();
  }
}

abstract class CreateEvent implements AdminEventManagementEvent {
  const factory CreateEvent({required final EventModel event}) =
      _$CreateEventImpl;

  EventModel get event;

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateEventImplCopyWith<_$CreateEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateEventImplCopyWith<$Res> {
  factory _$$UpdateEventImplCopyWith(
          _$UpdateEventImpl value, $Res Function(_$UpdateEventImpl) then) =
      __$$UpdateEventImplCopyWithImpl<$Res>;
  @useResult
  $Res call({EventModel event});

  $EventModelCopyWith<$Res> get event;
}

/// @nodoc
class __$$UpdateEventImplCopyWithImpl<$Res>
    extends _$AdminEventManagementEventCopyWithImpl<$Res, _$UpdateEventImpl>
    implements _$$UpdateEventImplCopyWith<$Res> {
  __$$UpdateEventImplCopyWithImpl(
      _$UpdateEventImpl _value, $Res Function(_$UpdateEventImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? event = null,
  }) {
    return _then(_$UpdateEventImpl(
      event: null == event
          ? _value.event
          : event // ignore: cast_nullable_to_non_nullable
              as EventModel,
    ));
  }

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EventModelCopyWith<$Res> get event {
    return $EventModelCopyWith<$Res>(_value.event, (value) {
      return _then(_value.copyWith(event: value));
    });
  }
}

/// @nodoc

class _$UpdateEventImpl implements UpdateEvent {
  const _$UpdateEventImpl({required this.event});

  @override
  final EventModel event;

  @override
  String toString() {
    return 'AdminEventManagementEvent.updateEvent(event: $event)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateEventImpl &&
            (identical(other.event, event) || other.event == event));
  }

  @override
  int get hashCode => Object.hash(runtimeType, event);

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateEventImplCopyWith<_$UpdateEventImpl> get copyWith =>
      __$$UpdateEventImplCopyWithImpl<_$UpdateEventImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadPendingEvents,
    required TResult Function(String eventId, String adminId) approveEvent,
    required TResult Function(String eventId, String adminId, String reason)
        rejectEvent,
    required TResult Function(EventModel event) createEvent,
    required TResult Function(EventModel event) updateEvent,
  }) {
    return updateEvent(event);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadPendingEvents,
    TResult? Function(String eventId, String adminId)? approveEvent,
    TResult? Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult? Function(EventModel event)? createEvent,
    TResult? Function(EventModel event)? updateEvent,
  }) {
    return updateEvent?.call(event);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadPendingEvents,
    TResult Function(String eventId, String adminId)? approveEvent,
    TResult Function(String eventId, String adminId, String reason)?
        rejectEvent,
    TResult Function(EventModel event)? createEvent,
    TResult Function(EventModel event)? updateEvent,
    required TResult orElse(),
  }) {
    if (updateEvent != null) {
      return updateEvent(event);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadPendingEvents value) loadPendingEvents,
    required TResult Function(ApproveEvent value) approveEvent,
    required TResult Function(RejectEvent value) rejectEvent,
    required TResult Function(CreateEvent value) createEvent,
    required TResult Function(UpdateEvent value) updateEvent,
  }) {
    return updateEvent(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadPendingEvents value)? loadPendingEvents,
    TResult? Function(ApproveEvent value)? approveEvent,
    TResult? Function(RejectEvent value)? rejectEvent,
    TResult? Function(CreateEvent value)? createEvent,
    TResult? Function(UpdateEvent value)? updateEvent,
  }) {
    return updateEvent?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadPendingEvents value)? loadPendingEvents,
    TResult Function(ApproveEvent value)? approveEvent,
    TResult Function(RejectEvent value)? rejectEvent,
    TResult Function(CreateEvent value)? createEvent,
    TResult Function(UpdateEvent value)? updateEvent,
    required TResult orElse(),
  }) {
    if (updateEvent != null) {
      return updateEvent(this);
    }
    return orElse();
  }
}

abstract class UpdateEvent implements AdminEventManagementEvent {
  const factory UpdateEvent({required final EventModel event}) =
      _$UpdateEventImpl;

  EventModel get event;

  /// Create a copy of AdminEventManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateEventImplCopyWith<_$UpdateEventImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
