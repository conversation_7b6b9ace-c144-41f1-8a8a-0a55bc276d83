import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/backup.dart';

part 'backup_event.freezed.dart';

@freezed
class BackupEvent with _$BackupEvent {
  const factory BackupEvent.loadBackups() = LoadBackups;
  const factory BackupEvent.createBackup(Backup backup) = CreateBackup;
  const factory BackupEvent.updateBackup(Backup backup) = UpdateBackup;
  const factory BackupEvent.deleteBackup(String id) = DeleteBackup;
  const factory BackupEvent.restoreBackup(String id) = RestoreBackup;
  const factory BackupEvent.validateBackup(String id) = ValidateBackup;
  const factory BackupEvent.scheduleBackup(Backup backup) = ScheduleBackup;
  const factory BackupEvent.cancelScheduledBackup(String id) =
      CancelScheduledBackup;
  const factory BackupEvent.updateRetentionSettings(String id, int days) =
      UpdateRetentionSettings;
  const factory BackupEvent.updateCompressionSettings(
      String id, Map<String, dynamic> settings) = UpdateCompressionSettings;
  const factory BackupEvent.updateValidationSettings(
      String id, Map<String, dynamic> settings) = UpdateValidationSettings;
  const factory BackupEvent.updateNotificationSettings(
      String id, Map<String, dynamic> settings) = UpdateNotificationSettings;
  const factory BackupEvent.updateMonitoringSettings(
      String id, Map<String, dynamic> settings) = UpdateMonitoringSettings;
}
