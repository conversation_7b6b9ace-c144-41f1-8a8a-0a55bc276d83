import 'package:flutter/material.dart';

class PriestFilterDialog extends StatefulWidget {
  final Map<String, dynamic> currentFilters;

  const PriestFilterDialog({
    super.key,
    required this.currentFilters,
  });

  @override
  State<PriestFilterDialog> createState() => _PriestFilterDialogState();
}

class _PriestFilterDialogState extends State<PriestFilterDialog> {
  late String? _status;
  late bool? _isActive;

  @override
  void initState() {
    super.initState();
    _status = widget.currentFilters['status'];
    _isActive = widget.currentFilters['isActive'];
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Priests'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DropdownButtonFormField<String>(
            value: _status,
            decoration: const InputDecoration(
              labelText: 'Verification Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: null,
                child: Text('All'),
              ),
              DropdownMenuItem(
                value: 'pending',
                child: Text('Pending'),
              ),
              DropdownMenuItem(
                value: 'verified',
                child: Text('Verified'),
              ),
              DropdownMenuItem(
                value: 'rejected',
                child: Text('Rejected'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _status = value;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<bool>(
            value: _isActive,
            decoration: const InputDecoration(
              labelText: 'Active Status',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(
                value: null,
                child: Text('All'),
              ),
              DropdownMenuItem(
                value: true,
                child: Text('Active'),
              ),
              DropdownMenuItem(
                value: false,
                child: Text('Inactive'),
              ),
            ],
            onChanged: (value) {
              setState(() {
                _isActive = value;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text('Clear'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, null),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => Navigator.pop(
            context,
            {
              'status': _status,
              'isActive': _isActive,
            },
          ),
          child: const Text('Apply'),
        ),
      ],
    );
  }
}
