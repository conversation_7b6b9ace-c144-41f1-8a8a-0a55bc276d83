import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/services/media/media_service.dart';
import '../../../../shared/models/media/media_model.dart';
import 'admin_media_management_event_simple.dart';
import 'admin_media_management_state.dart';

class AdminMediaManagementBloc
    extends Bloc<AdminMediaManagementEvent, AdminMediaManagementState> {
  final MediaService _mediaService;

  AdminMediaManagementBloc({
    required MediaService mediaService,
  })  : _mediaService = mediaService,
        super(const AdminMediaManagementState.initial()) {
    on<LoadPendingMediaEvent>((event, emit) async {
      await _onLoadPendingMedia(emit);
    });

    on<ApproveMediaEvent>((event, emit) async {
      await _onApproveMedia(emit, event.mediaId, event.adminId);
    });

    on<RejectMediaEvent>((event, emit) async {
      await _onRejectMedia(emit, event.mediaId, event.adminId, event.reason);
    });

    on<UploadMediaEvent>((event, emit) async {
      await _onUploadMedia(emit, event.file, event.title, event.description, event.adminId);
    });

    on<UpdateMediaEvent>((event, emit) async {
      await _onUpdateMedia(emit, event.mediaId, event.title, event.description, event.file);
    });
  }

  Future<void> _onLoadPendingMedia(
      Emitter<AdminMediaManagementState> emit) async {
    try {
      emit(const AdminMediaManagementState.loading());
      final mediaList = await _mediaService.getPendingMedia();
      emit(AdminMediaManagementState.loadedPendingMedia(mediaList));
    } catch (e) {
      emit(AdminMediaManagementState.error(e.toString()));
    }
  }

  Future<void> _onApproveMedia(
    Emitter<AdminMediaManagementState> emit,
    String mediaId,
    String adminId,
  ) async {
    try {
      emit(const AdminMediaManagementState.loading());
      await _mediaService.approveMedia(mediaId: mediaId, adminId: adminId);
      emit(const AdminMediaManagementState.mediaApproved(
          'Media approved successfully'));
      add(const LoadPendingMediaEvent());
    } catch (e) {
      emit(AdminMediaManagementState.error(e.toString()));
    }
  }

  Future<void> _onRejectMedia(
    Emitter<AdminMediaManagementState> emit,
    String mediaId,
    String adminId,
    String reason,
  ) async {
    try {
      emit(const AdminMediaManagementState.loading());
      await _mediaService.rejectMedia(
        mediaId: mediaId,
        adminId: adminId,
        reason: reason,
      );
      emit(const AdminMediaManagementState.mediaRejected(
          'Media rejected successfully'));
      add(const LoadPendingMediaEvent());
    } catch (e) {
      emit(AdminMediaManagementState.error(e.toString()));
    }
  }

  Future<void> _onUploadMedia(
    Emitter<AdminMediaManagementState> emit,
    File file,
    String title,
    String description,
    String adminId,
  ) async {
    try {
      emit(const AdminMediaManagementState.loading());
      // Detect media type from file extension
      final fileName = file.path.split('/').last;
      final fileExt = fileName.split('.').last.toLowerCase();

      // Determine media type based on file extension
      MediaType? detectedType;
      if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].contains(fileExt)) {
        detectedType = MediaType.image;
      } else if (['mp4', 'mov', 'avi', 'mkv', 'webm', 'flv'].contains(fileExt)) {
        detectedType = MediaType.video;
      } else if (['mp3', 'wav', 'aac', 'm4a', 'ogg', 'flac'].contains(fileExt)) {
        detectedType = MediaType.audio;
      }

      await _mediaService.uploadMediaAndCreateRecord(
        file: file,
        userId: adminId,
        title: title,
        fileName: fileName,
        visibility: MediaVisibility.public,
        status: MediaStatus.approved,
        description: description,
        type: detectedType, // Use detected media type
      );
      emit(const AdminMediaManagementState.mediaUploaded(
          'Media uploaded successfully'));
    } catch (e) {
      emit(AdminMediaManagementState.error(e.toString()));
    }
  }

  Future<void> _onUpdateMedia(
    Emitter<AdminMediaManagementState> emit,
    String mediaId,
    String title,
    String description,
    File? file,
  ) async {
    try {
      emit(const AdminMediaManagementState.loading());
      var media = await _mediaService.getMediaById(mediaId);
      if (media == null) {
        throw Exception('Media not found');
      }

      if (file != null) {
        // Upload new file and update URL
        final url = await _mediaService.uploadMedia(
          file,
          'media/$mediaId/${file.path.split('/').last}',
        );
        media = media.copyWith(
          url: url,
          title: title,
          description: description,
        );
      } else {
        media = media.copyWith(
          title: title,
          description: description,
        );
      }

      await _mediaService.updateMedia(media);
      emit(const AdminMediaManagementState.mediaUpdated(
          'Media updated successfully'));
    } catch (e) {
      emit(AdminMediaManagementState.error(e.toString()));
    }
  }
}
