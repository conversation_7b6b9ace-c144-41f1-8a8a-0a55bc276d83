import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../../shared/providers/tone_provider.dart';
import '../../../shared/providers/auth_provider.dart';
import '../../../shared/ui_components/loading/loading_indicator.dart';

class ToneCreationDrawer extends ConsumerStatefulWidget {
  final VoidCallback? onToneCreated;

  const ToneCreationDrawer({
    super.key,
    this.onToneCreated,
  });

  @override
  ConsumerState<ToneCreationDrawer> createState() => _ToneCreationDrawerState();
}

class _ToneCreationDrawerState extends ConsumerState<ToneCreationDrawer> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _categoryController = TextEditingController();
  String? _selectedFilePath;
  String _selectedFileName = '';
  bool _isLoading = false;
  bool _isMultiple = false;
  bool _isDefault = false;
  String _selectedLanguage = 'en';
  final List<String> _selectedDays = [];

  final List<Map<String, String>> _languages = [
    {'code': 'en', 'name': 'English', 'native': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'native': 'हिंदी'},
    {'code': 'te', 'name': 'Telugu', 'native': 'తెలుగు'},
    {'code': 'ta', 'name': 'Tamil', 'native': 'தமிழ்'},
    {'code': 'kn', 'name': 'Kannada', 'native': 'ಕನ್ನಡ'},
  ];

  final List<Map<String, dynamic>> _daysOfWeek = [
    {'code': 'mon', 'name': 'Monday'},
    {'code': 'tue', 'name': 'Tuesday'},
    {'code': 'wed', 'name': 'Wednesday'},
    {'code': 'thu', 'name': 'Thursday'},
    {'code': 'fri', 'name': 'Friday'},
    {'code': 'sat', 'name': 'Saturday'},
    {'code': 'sun', 'name': 'Sunday'},
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  Future<void> _pickAudioFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFilePath = result.files.first.path;
          _selectedFileName = result.files.first.name;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate() || _selectedFilePath == null) {
      if (_selectedFilePath == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select an audio file')),
        );
      }
      return;
    }

    final user = ref.read(authProvider);
    if (user == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please sign in to upload tones')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Get file duration (in a real app, you would calculate this)
      final duration = 30; // Placeholder duration in seconds

      await ref.read(uploadToneProvider(
        toneName: _nameController.text,
        category: _categoryController.text,
        filePath: _selectedFilePath!,
        duration: duration,
        isDefault: _isDefault,
        uploadedBy: 'admin',
        language: _selectedLanguage,
        isMultiple: _isMultiple,
        daysOfWeek: _selectedDays,
      ).future);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tone uploaded successfully'),
          ),
        );
        Navigator.pop(context);
        
        // Call the callback if provided
        if (widget.onToneCreated != null) {
          widget.onToneCreated!();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to upload tone: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: _isLoading
          ? const Center(child: LoadingIndicator())
          : SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Create New Tone',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: _nameController,
                          decoration: const InputDecoration(
                            labelText: 'Tone Name',
                            hintText: 'Enter a name for the tone',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a name';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _categoryController,
                          decoration: const InputDecoration(
                            labelText: 'Category',
                            hintText: 'Enter a category (e.g., Morning, Evening)',
                            border: OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a category';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Language',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<String>(
                          value: _selectedLanguage,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                          ),
                          items: _languages.map((language) {
                            return DropdownMenuItem<String>(
                              value: language['code'],
                              child: Text(
                                  '${language['name']} (${language['native']})'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedLanguage = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('Multiple Tones (Day-wise)'),
                          subtitle: const Text(
                            'Enable to set different tones for different days of the week',
                          ),
                          value: _isMultiple,
                          onChanged: (value) {
                            setState(() {
                              _isMultiple = value;
                              if (!value) {
                                _selectedDays.clear();
                              }
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                        if (_isMultiple) ...[
                          const SizedBox(height: 8),
                          const Text(
                            'Select Days',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: _daysOfWeek.map((day) {
                              final isSelected =
                                  _selectedDays.contains(day['code']);
                              return FilterChip(
                                label: Text(day['name']),
                                selected: isSelected,
                                onSelected: (selected) {
                                  setState(() {
                                    if (selected) {
                                      _selectedDays.add(day['code']);
                                    } else {
                                      _selectedDays.remove(day['code']);
                                    }
                                  });
                                },
                              );
                            }).toList(),
                          ),
                        ],
                        const SizedBox(height: 16),
                        SwitchListTile(
                          title: const Text('Set as Default'),
                          subtitle: const Text(
                            'Make this tone the default for the selected language',
                          ),
                          value: _isDefault,
                          onChanged: (value) {
                            setState(() {
                              _isDefault = value;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Audio File',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: _pickAudioFile,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.audio_file),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    _selectedFileName.isEmpty
                                        ? 'Select audio file'
                                        : _selectedFileName,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                if (_selectedFileName.isNotEmpty)
                                  IconButton(
                                    icon: const Icon(Icons.clear),
                                    onPressed: () {
                                      setState(() {
                                        _selectedFilePath = null;
                                        _selectedFileName = '';
                                      });
                                    },
                                  ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 24),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _submitForm,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Upload Tone'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
