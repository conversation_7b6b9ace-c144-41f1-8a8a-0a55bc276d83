import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/api_key.dart';

part 'api_key_state.freezed.dart';

@freezed
class ApiKeyState with _$ApiKeyState {
  const factory ApiKeyState.initial() = ApiKeyInitial;
  const factory ApiKeyState.loading() = ApiKeyLoading;
  const factory ApiKeyState.loaded(List<ApiKey> apiKeys) = ApiKeyLoaded;
  const factory ApiKeyState.error(String message) = ApiKeyError;
}
