package com.shivish.utils

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.app.AlertDialog
import androidx.core.content.ContextCompat

/**
 * Utility class for handling clipboard operations safely
 */
object ClipboardUtils {
    private const val TAG = "ClipboardUtils"

    /**
     * Check if the app has clipboard permission
     */
    fun hasClipboardPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // For Android 10 and above, we need to check if the app is in focus
            // This is a simplified check and may not be 100% accurate
            try {
                val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
                clipboardManager != null
            } catch (e: Exception) {
                Log.e(TAG, "Error checking clipboard permission: ${e.message}")
                false
            }
        } else {
            // For older Android versions, clipboard access is granted by default
            true
        }
    }

    /**
     * Request clipboard permission
     */
    fun requestClipboardPermission(context: Context) {
        try {
            // Only show clipboard permission dialog when actually needed
            // Don't show it at app startup
            Log.d(TAG, "Clipboard permission will be requested when needed")

            // We'll show the dialog when the user tries to use clipboard features
            // This avoids overwhelming the user with permission dialogs at startup
        } catch (e: Exception) {
            // If there's an error, just log it and continue
            Log.e(TAG, "Error with clipboard permission: ${e.message}")
        }
    }

    /**
     * Show clipboard permission dialog when needed
     */
    fun showClipboardPermissionDialog(context: Context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                // For Android 10 and above, we need to show a dialog explaining why we need clipboard access
                AlertDialog.Builder(context)
                    .setTitle("Clipboard Access Required")
                    .setMessage("This app needs access to the clipboard to copy and paste text. Please ensure the app is in focus when using clipboard features.")
                    .setPositiveButton("OK", null)
                    .setCancelable(true) // Allow dialog to be dismissed
                    .show()

                // Log that we've shown the clipboard permission dialog
                Log.d(TAG, "Clipboard permission dialog shown")
            }
        } catch (e: Exception) {
            // If there's an error showing the dialog, just log it and continue
            Log.e(TAG, "Error showing clipboard permission dialog: ${e.message}")
        }
    }

    /**
     * Copy text to clipboard
     */
    fun copyToClipboard(context: Context, text: String, label: String): Boolean {
        return try {
            val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
            if (clipboardManager != null) {
                val clipData = ClipData.newPlainText(label, text)
                clipboardManager.setPrimaryClip(clipData)
                Log.d(TAG, "Text copied to clipboard: $text")
                true
            } else {
                Log.e(TAG, "Clipboard manager is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error copying to clipboard: ${e.message}")
            false
        }
    }

    /**
     * Get text from clipboard
     */
    fun getFromClipboard(context: Context): String? {
        return try {
            val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
            if (clipboardManager != null && clipboardManager.hasPrimaryClip()) {
                val clipData = clipboardManager.primaryClip
                if (clipData != null && clipData.itemCount > 0) {
                    val text = clipData.getItemAt(0).text
                    Log.d(TAG, "Text retrieved from clipboard: $text")
                    text.toString()
                } else {
                    null
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting from clipboard: ${e.message}")
            null
        }
    }

    /**
     * Check if clipboard has text
     */
    fun hasText(context: Context): Boolean {
        return try {
            val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
            clipboardManager?.hasPrimaryClip() ?: false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking clipboard text: ${e.message}")
            false
        }
    }

    /**
     * Clear clipboard
     */
    fun clearClipboard(context: Context): Boolean {
        return try {
            val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
            if (clipboardManager != null) {
                clipboardManager.setPrimaryClip(ClipData.newPlainText("", ""))
                Log.d(TAG, "Clipboard cleared")
                true
            } else {
                Log.e(TAG, "Clipboard manager is null")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing clipboard: ${e.message}")
            false
        }
    }
}