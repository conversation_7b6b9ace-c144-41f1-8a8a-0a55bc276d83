import 'package:freezed_annotation/freezed_annotation.dart';

part 'category_model.freezed.dart';
part 'category_model.g.dart';

/// Model for categories
@freezed
class CategoryModel with _$CategoryModel {
  /// Creates a [CategoryModel]
  const factory CategoryModel({
    /// Unique identifier for the category
    required String id,

    /// Name of the category
    required String name,

    /// Description of the category
    String? description,

    /// Icon URL for the category
    String? iconUrl,

    /// Image URL for the category
    String? imageUrl,

    /// Parent category ID (null for root categories)
    String? parentId,

    /// Whether the category is active
    @Default(true) bool isActive,

    /// Priority order for the category
    @Default(0) int priority,

    /// Created at timestamp
    required DateTime createdAt,

    /// Updated at timestamp
    required DateTime updatedAt,
  }) = _CategoryModel;

  /// Creates a [CategoryModel] from JSON
  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);
}
