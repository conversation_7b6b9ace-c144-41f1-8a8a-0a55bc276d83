import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/priest.dart';
import '../../../../shared/services/priest_service.dart';
import 'package:shivish/shared/core/types/firebase_compatibility.dart';


part 'priest_event.dart';
part 'priest_state.dart';
part 'priest_bloc.freezed.dart';

class PriestBloc extends Bloc<PriestEvent, PriestState> {
  final PriestService _priestService;
  Map<String, dynamic>? _lastDocument;

  PriestBloc(this._priestService) : super(const PriestState.initial()) {
    on<PriestEvent>((event, emit) async {
      await event.map(
        loadPriests: (e) => _onLoadPriests(e, emit),
        loadMorePriests: (e) => _onLoadMorePriests(e, emit),
        updatePriestStatus: (e) => _onUpdatePriestStatus(e, emit),
        updatePriestVerification: (e) => _onUpdatePriestVerification(e, emit),
        deletePriest: (e) => _onDeletePriest(e, emit),
        startRealtimeUpdates: (e) => _onStartRealtimeUpdates(e, emit),
        stopRealtimeUpdates: (e) => _onStopRealtimeUpdates(e, emit),
      );
    });
  }

  Future<void> _onLoadPriests(
      _LoadPriests event, Emitter<PriestState> emit) async {
    try {
      emit(const PriestState.loading());
      final priests = await _priestService.getPriests();
      if (priests.isNotEmpty) {
        _lastDocument = await _priestService.getPriestDocument(priests.last.id);
      }
      emit(PriestState.loaded(priests));
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onLoadMorePriests(
      _LoadMorePriests event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded && _lastDocument != null) {
        final morePriests = await _priestService.getPriests(
          startAfter: _lastDocument,
        );
        if (morePriests.isNotEmpty) {
          _lastDocument =
              await _priestService.getPriestDocument(morePriests.last.id);
          emit(PriestState.loaded([...currentState.priests, ...morePriests]));
        }
      }
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePriestStatus(
      _UpdatePriestStatus event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        await _priestService.updatePriestStatus(
          id: event.id,
          isActive: event.isActive,
        );
        final updatedPriests = currentState.priests.map((priest) {
          if (priest.id == event.id) {
            return priest.copyWith(isActive: event.isActive);
          }
          return priest;
        }).toList();
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePriestVerification(
      _UpdatePriestVerification event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        await _priestService.updatePriestVerification(
          id: event.id,
          isVerified: event.isVerified,
          verificationStatus: event.verificationStatus,
          verificationNotes: event.verificationNotes ?? '',
        );
        final updatedPriests = currentState.priests.map((priest) {
          if (priest.id == event.id) {
            return priest.copyWith(
              isVerified: event.isVerified,
              verificationStatus: event.verificationStatus,
              verificationNotes: event.verificationNotes ?? '',
            );
          }
          return priest;
        }).toList();
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onDeletePriest(
      _DeletePriest event, Emitter<PriestState> emit) async {
    try {
      final currentState = state;
      if (currentState is _Loaded) {
        await _priestService.deletePriest(id: event.id);
        final updatedPriests = currentState.priests
            .eq((priest) => priest.id != event.id)
            .toList();
        emit(PriestState.loaded(updatedPriests));
      }
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onStartRealtimeUpdates(
      _StartRealtimeUpdates event, Emitter<PriestState> emit) async {
    try {
      await _priestService.startRealtimeUpdates((priests) {
        emit(PriestState.loaded(priests));
      });
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }

  Future<void> _onStopRealtimeUpdates(
      _StopRealtimeUpdates event, Emitter<PriestState> emit) async {
    try {
      await _priestService.stopRealtimeUpdates();
    } catch (e) {
      emit(PriestState.error(e.toString()));
    }
  }
}
