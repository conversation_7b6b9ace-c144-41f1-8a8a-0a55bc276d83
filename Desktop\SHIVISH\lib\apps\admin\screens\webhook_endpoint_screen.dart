import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../shared/utils/logger.dart';
import '../../../shared/ui_components/dialogs/confirmation_dialog.dart';

/// Screen for managing webhook endpoints
class WebhookEndpointScreen extends ConsumerStatefulWidget {
  const WebhookEndpointScreen({super.key});

  @override
  ConsumerState<WebhookEndpointScreen> createState() => _WebhookEndpointScreenState();
}

class _WebhookEndpointScreenState extends ConsumerState<WebhookEndpointScreen> {
  final _logger = getLogger('WebhookEndpointScreen');
  final _firestore = FirebaseFirestore.instance;

  // Webhook configuration
  Map<String, dynamic>? _ecomExpressConfig;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadWebhookConfigurations();
  }

  /// Load webhook configurations from Firestore
  Future<void> _loadWebhookConfigurations() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load Ecom Express configuration
      final ecomExpressDoc = await _firestore
          .collection('delivery_providers')
          .doc('ecom_express')
          .get();

      if (ecomExpressDoc.exists) {
        _ecomExpressConfig = ecomExpressDoc.data();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      _logger.severe('Error loading webhook configurations: $e');
      setState(() {
        _isLoading = false;
        _error = 'Error loading webhook configurations: $e';
      });
    }
  }

  /// Save webhook configuration
  Future<void> _saveWebhookConfiguration(String provider, Map<String, dynamic> config) async {
    try {
      await _firestore
          .collection('delivery_providers')
          .doc(provider)
          .update({
            'config': config,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Webhook configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Reload configurations
      await _loadWebhookConfigurations();
    } catch (e) {
      _logger.severe('Error saving webhook configuration: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving webhook configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Generate a new webhook secret
  String _generateWebhookSecret() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    final result = StringBuffer();

    for (var i = 0; i < 32; i++) {
      result.write(chars[random % chars.length]);
    }

    return result.toString();
  }

  /// Show dialog to edit webhook configuration
  Future<void> _showEditWebhookDialog(String provider, Map<String, dynamic> config) async {
    final apiKeyController = TextEditingController(text: config['api_key'] ?? '');
    final apiSecretController = TextEditingController(text: config['api_secret'] ?? '');
    final webhookSecretController = TextEditingController(text: config['webhook_secret'] ?? '');
    final apiUrlController = TextEditingController(text: config['api_url'] ?? '');

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit $provider Configuration'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextField(
                controller: apiKeyController,
                decoration: const InputDecoration(
                  labelText: 'API Key',
                  hintText: 'Enter API key',
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: apiSecretController,
                decoration: const InputDecoration(
                  labelText: 'API Secret',
                  hintText: 'Enter API secret',
                ),
                obscureText: true,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: webhookSecretController,
                      decoration: const InputDecoration(
                        labelText: 'Webhook Secret',
                        hintText: 'Enter webhook secret',
                      ),
                      obscureText: true,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () {
                      webhookSecretController.text = _generateWebhookSecret();
                    },
                    tooltip: 'Generate new secret',
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: apiUrlController,
                decoration: const InputDecoration(
                  labelText: 'API URL',
                  hintText: 'Enter API URL',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final updatedConfig = Map<String, dynamic>.from(config);
              updatedConfig['api_key'] = apiKeyController.text;
              updatedConfig['api_secret'] = apiSecretController.text;
              updatedConfig['webhook_secret'] = webhookSecretController.text;
              updatedConfig['api_url'] = apiUrlController.text;

              Navigator.of(context).pop(updatedConfig);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _saveWebhookConfiguration(provider, result);
    }
  }

  /// Show webhook URL information
  void _showWebhookUrlInfo(String provider) {
    final webhookUrl = 'https://your-app-domain.com/api/webhooks/$provider';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Webhook URL'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Use this URL in your delivery provider dashboard:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      webhookUrl,
                      style: const TextStyle(fontFamily: 'monospace'),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.copy),
                    onPressed: () {
                      Clipboard.setData(ClipboardData(text: webhookUrl));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Webhook URL copied to clipboard'),
                        ),
                      );
                    },
                    tooltip: 'Copy to clipboard',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Headers to include:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Text(
                'Content-Type: application/json',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// View recent webhooks
  Future<void> _viewRecentWebhooks(String provider) async {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WebhookHistoryScreen(provider: provider),
      ),
    );
  }

  /// Test webhook configuration
  Future<void> _testWebhookConfiguration(String provider) async {
    if (!mounted) return;

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => ConfirmationDialog(
        title: 'Test Webhook',
        content: 'This will send a test webhook to your server. Continue?',
        confirmText: 'Test',
        cancelText: 'Cancel',
      ),
    );

    if (confirmed != true || !mounted) return;

    // Create a dialog key to track the dialog
    final dialogKey = GlobalKey<State>();

    if (!mounted) return;

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => AlertDialog(
        key: dialogKey,
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Testing webhook...'),
          ],
        ),
      ),
    );

    try {
      // Simulate webhook test
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      // Close loading dialog using Navigator.pop with the current context
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (!mounted) return;

      // Show success dialog
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Test Successful'),
          content: const Text('Webhook test completed successfully.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // Close loading dialog using Navigator.pop with the current context
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (!mounted) return;

      // Show error dialog
      showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Test Failed'),
          content: Text('Error testing webhook: $e'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Webhook Endpoints'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadWebhookConfigurations,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(_error!, style: const TextStyle(color: Colors.red)),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWebhookConfigurations,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _buildWebhookCard(
                      title: 'Ecom Express',
                      description: 'Webhook endpoint for Ecom Express delivery updates',
                      config: _ecomExpressConfig?['config'] ?? {},
                      provider: 'ecom_express',
                      icon: Icons.local_shipping,
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Webhook Setup Instructions',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              '1. Configure your webhook endpoint in the delivery provider dashboard',
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              '2. Set the webhook URL to your app\'s webhook endpoint',
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              '3. Configure the webhook secret for secure communication',
                            ),
                            const SizedBox(height: 4),
                            const Text(
                              '4. Test the webhook to ensure it\'s working correctly',
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Note: Webhooks allow real-time delivery updates to be sent to your app.',
                              style: TextStyle(fontStyle: FontStyle.italic),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildWebhookCard({
    required String title,
    required String description,
    required Map<String, dynamic> config,
    required String provider,
    required IconData icon,
  }) {
    final isConfigured = config['api_key'] != null && config['api_key'].isNotEmpty;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isConfigured ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isConfigured ? 'Configured' : 'Not Configured',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(description),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  icon: const Icon(Icons.info_outline),
                  label: const Text('Webhook URL'),
                  onPressed: () => _showWebhookUrlInfo(provider),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  icon: const Icon(Icons.history),
                  label: const Text('View History'),
                  onPressed: () => _viewRecentWebhooks(provider),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  icon: const Icon(Icons.send),
                  label: const Text('Test'),
                  onPressed: () => _testWebhookConfiguration(provider),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit'),
                  onPressed: () => _showEditWebhookDialog(provider, config),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Screen for viewing webhook history
class WebhookHistoryScreen extends StatelessWidget {
  final String provider;

  const WebhookHistoryScreen({
    super.key,
    required this.provider,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('$provider Webhook History'),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance
            .collection('webhooks')
            .where('provider', isEqualTo: provider)
            .orderBy('receivedAt', descending: true)
            .limit(50)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text('Error: ${snapshot.error}'),
            );
          }

          final webhooks = snapshot.data?.docs ?? [];

          if (webhooks.isEmpty) {
            return const Center(
              child: Text('No webhook history found'),
            );
          }

          return ListView.builder(
            itemCount: webhooks.length,
            itemBuilder: (context, index) {
              final webhook = webhooks[index].data() as Map<String, dynamic>;
              final receivedAt = webhook['receivedAt'] as Timestamp?;
              final processed = webhook['processed'] as bool? ?? false;

              return ListTile(
                title: Text(
                  'Webhook ${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: Text(
                  'Received: ${receivedAt?.toDate().toString() ?? 'Unknown'}',
                ),
                trailing: Icon(
                  processed ? Icons.check_circle : Icons.error,
                  color: processed ? Colors.green : Colors.red,
                ),
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Webhook Details'),
                      content: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Received: ${receivedAt?.toDate().toString() ?? 'Unknown'}'),
                            Text('Processed: ${processed ? 'Yes' : 'No'}'),
                            const SizedBox(height: 16),
                            const Text(
                              'Payload:',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                const JsonEncoder.withIndent('  ').convert(webhook['data'] ?? {}),
                                style: const TextStyle(fontFamily: 'monospace'),
                              ),
                            ),
                          ],
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Close'),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
