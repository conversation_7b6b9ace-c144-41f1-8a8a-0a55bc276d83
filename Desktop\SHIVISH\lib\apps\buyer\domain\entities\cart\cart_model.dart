import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/product/product_model.dart';

part 'cart_model.freezed.dart';
part 'cart_model.g.dart';

/// Cart item model
@freezed
class CartItemModel with _$CartItemModel {
  /// Creates a [CartItemModel]
  const factory CartItemModel({
    required ProductModel product,
    required int quantity,
    required String sellerId,
    String? notes,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _CartItemModel;

  /// Creates a [CartItemModel] from JSON
  factory CartItemModel.fromJson(Map<String, dynamic> json) =>
      _$CartItemModelFromJson(json);
}

/// Cart model
@freezed
class CartModel with _$CartModel {
  /// Creates a [CartModel]
  const factory CartModel({
    required String id,
    required List<CartItemModel> items,
    required DateTime createdAt,
    required DateTime updatedAt,
  }) = _CartModel;

  /// Creates a [CartModel] from JSON
  factory CartModel.fromJson(Map<String, dynamic> json) =>
      _$CartModelFromJson(json);

  /// Creates an empty [CartModel]
  factory CartModel.empty() => CartModel(
        id: '',
        items: const [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
}

/// Extension on [CartModel] to add computed properties
extension CartModelX on CartModel {
  /// Calculates the total price of all items in the cart
  double get totalPrice => items.fold(
        0,
        (sum, item) => sum + (item.product.price * item.quantity),
      );

  /// Calculates the total quantity of all items in the cart
  int get totalQuantity => items.fold(0, (sum, item) => sum + item.quantity);
}
