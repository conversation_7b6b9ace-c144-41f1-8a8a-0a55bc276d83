import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/models/calendar/calendar_event_model.dart';
import '../../../../shared/models/calendar/calendar_event_type.dart';
import '../../../../shared/models/calendar/calendar_event_visibility.dart';
import '../../../../shared/models/calendar/calendar_event_status.dart';
import '../../../../shared/providers/auth_provider.dart';
import 'admin_product_list_form_screen_new.dart';

/// Screen for managing admin product lists
class AdminProductListScreen extends ConsumerStatefulWidget {
  /// Creates an [AdminProductListScreen]
  const AdminProductListScreen({super.key});

  @override
  ConsumerState<AdminProductListScreen> createState() =>
      _AdminProductListScreenState();
}

class _AdminProductListScreenState
    extends ConsumerState<AdminProductListScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider).value;

    if (user == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please sign in to manage product lists'),
        ),
      );
    }

    final productListsAsync = ref.watch(adminProductListsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Lists'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute<void>(
                  builder: (context) => const AdminProductListFormScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search product lists',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),

          // Product lists
          Expanded(
            child: productListsAsync.when(
              data: (productLists) {
                // Filter by search query
                final filteredLists = productLists.where((list) {
                  if (_searchQuery.isEmpty) return true;
                  return list.title
                          .toLowerCase()
                          .contains(_searchQuery.toLowerCase()) ||
                      list.description
                          .toLowerCase()
                          .contains(_searchQuery.toLowerCase());
                }).toList();

                if (filteredLists.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.shopping_cart,
                          size: 64,
                          color:
                              theme.colorScheme.primary.withValues(alpha: 128),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No product lists found',
                          style: theme.textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute<void>(
                                builder: (context) =>
                                    const AdminProductListFormScreen(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Create Product List'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredLists.length,
                  itemBuilder: (context, index) {
                    final list = filteredLists[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                topRight: Radius.circular(4),
                              ),
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        list.title,
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '${_formatDate(list.startDate)} - ${_formatDate(list.endDate)}',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: Colors.white
                                              .withValues(alpha: 200),
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      // Extract location from description
                                      if (list.description
                                          .contains('Location:'))
                                        Row(
                                          children: [
                                            const Icon(
                                              Icons.location_on,
                                              color: Colors.white,
                                              size: 14,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              _extractLocation(
                                                  list.description),
                                              style: theme.textTheme.bodySmall
                                                  ?.copyWith(
                                                color: Colors.white
                                                    .withValues(alpha: 200),
                                              ),
                                            ),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                                _buildStatusChip(list.status),
                              ],
                            ),
                          ),

                          // Description
                          if (list.description.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Text(list.description),
                            ),

                          // Products
                          if (list.products != null &&
                              list.products!.isNotEmpty)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Products (${list.products!.length})',
                                    style: theme.textTheme.titleSmall,
                                  ),
                                  const SizedBox(height: 8),
                                  Wrap(
                                    spacing: 8,
                                    runSpacing: 8,
                                    children: list.products!.map((product) {
                                      return Chip(
                                        label: Text(product.name),
                                        avatar: const Icon(Icons.shopping_cart,
                                            size: 16),
                                      );
                                    }).toList(),
                                  ),
                                ],
                              ),
                            ),

                          // Actions
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                TextButton.icon(
                                  onPressed: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute<void>(
                                        builder: (context) =>
                                            AdminProductListFormScreen(
                                          event: list,
                                        ),
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.edit),
                                  label: const Text('Edit'),
                                ),
                                const SizedBox(width: 8),
                                TextButton.icon(
                                  onPressed: () => _deleteProductList(list),
                                  icon: const Icon(Icons.delete,
                                      color: Colors.red),
                                  label: const Text(
                                    'Delete',
                                    style: TextStyle(color: Colors.red),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Text('Error loading product lists: $error'),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute<void>(
              builder: (context) => const AdminProductListFormScreen(),
            ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Extract location information from description
  String _extractLocation(String description) {
    // Find the location information in the description
    final locationIndex = description.indexOf('Location:');
    if (locationIndex == -1) return 'No location';

    // Extract the location part
    final locationPart = description.substring(locationIndex);

    // If there's more text after the location, only take the location part
    final endIndex = locationPart.indexOf('\n');
    if (endIndex != -1) {
      return locationPart.substring(0, endIndex).trim();
    }

    return locationPart.trim();
  }

  Widget _buildStatusChip(CalendarEventStatus status) {
    Color color;
    String label;

    switch (status) {
      case CalendarEventStatus.scheduled:
        color = Colors.blue;
        label = 'Scheduled';
      case CalendarEventStatus.completed:
        color = Colors.green;
        label = 'Completed';
      case CalendarEventStatus.cancelled:
        color = Colors.red;
        label = 'Cancelled';
      case CalendarEventStatus.postponed:
        color = Colors.orange;
        label = 'Postponed';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 50),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Future<void> _deleteProductList(CalendarEventModel event) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Product List'),
        content: const Text(
          'Are you sure you want to delete this product list? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(calendarEventServiceProvider).deleteEvent(event.id);
        ref.invalidate(adminProductListsProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product list deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete product list: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

/// Provider for admin product lists
final adminProductListsProvider =
    StreamProvider<List<CalendarEventModel>>((ref) {
  final service = ref.watch(calendarEventServiceProvider);
  return service.getEventsByType(CalendarEventType.productList).map((events) {
    // Filter for admin-created product lists (non-private)
    return events
        .where((event) => event.visibility != CalendarEventVisibility.private)
        .toList();
  });
});
