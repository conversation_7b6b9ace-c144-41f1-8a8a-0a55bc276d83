import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:math' as math;
import '../bloc/auth/auth_bloc.dart';

class TechnicianRequestsScreen extends StatefulWidget {
  const TechnicianRequestsScreen({super.key});

  @override
  State<TechnicianRequestsScreen> createState() => _TechnicianRequestsScreenState();
}

class _TechnicianRequestsScreenState extends State<TechnicianRequestsScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;
  List<Map<String, dynamic>> _technicianRequests = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadTechnicianRequests();
  }

  Future<void> _loadTechnicianRequests() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // First, get all technicians to see what's in the collection
      final allTechnicians = await _firestore
          .collection('technicians')
          .get();

      debugPrint('Found ${allTechnicians.docs.length} total technicians');

      // Print the first few technicians to see their data structure
      for (int i = 0; i < math.min(5, allTechnicians.docs.length); i++) {
        final doc = allTechnicians.docs[i];
        debugPrint('Technician ${i+1}: ${doc.id}');
        debugPrint('Data: ${doc.data()}');
      }

      // Now get the pending technicians
      final snapshot = await _firestore
          .collection('technicians')
          .where('verificationStatus', isEqualTo: 'pending')
          .get();

      debugPrint('Found ${snapshot.docs.length} technicians with verificationStatus=pending');

      // Try alternative query with snake_case field name
      final snapshotAlt = await _firestore
          .collection('technicians')
          .where('verificationStatus', isEqualTo: 'pending')
          .get();

      debugPrint('Found ${snapshotAlt.docs.length} technicians with verificationStatus=pending');

      // Combine results from both queries
      final allDocs = [...snapshot.docs, ...snapshotAlt.docs];

      // Remove duplicates by ID
      final uniqueIds = <String>{};
      final uniqueDocs = allDocs.where((doc) {
        final isUnique = !uniqueIds.contains(doc.id);
        if (isUnique) {
          uniqueIds.add(doc.id);
        }
        return isUnique;
      }).toList();

      debugPrint('Found ${uniqueDocs.length} unique pending technicians');

      final requests = uniqueDocs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      setState(() {
        _technicianRequests = requests;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading technician requests: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _approveTechnician(String requestId) async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get request document
      final requestDoc = await _firestore.collection('technicians').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Technician request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['id'] as String; // Use id instead of uid

      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.uid;
        }
      }

      // Update user document to grant technician access
      await _firestore.collection('users').doc(userId).update({
        'isVerified': true,
        'isActive': true,
        'verificationStatus': 'approved',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update request status
      await _firestore.collection('technicians').doc(requestId).update({
        'verificationStatus': 'approved',
        'isActive': true,
        'approvedAt': FieldValue.serverTimestamp(),
        'approvedBy': adminId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Reload the list
      await _loadTechnicianRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Technician approved successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error approving technician: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _rejectTechnician(String requestId) async {
    if (!mounted) return;

    final reasonController = TextEditingController();

    final reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Technician'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Please provide a reason for rejection:'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(reasonController.text),
            child: const Text('Reject'),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty || !mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the current admin user ID
      String? adminId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        if (authState is AuthAuthenticatedState) {
          adminId = authState.user.uid;
        }
      }

      // Update request status
      await _firestore.collection('technicians').doc(requestId).update({
        'verificationStatus': 'rejected',
        'rejectionReason': reason,
        'rejectedAt': FieldValue.serverTimestamp(),
        'rejectedBy': adminId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Reload the list
      await _loadTechnicianRequests();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Technician rejected successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Error rejecting technician: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Technician Requests'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTechnicianRequests,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadTechnicianRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _technicianRequests.isEmpty
                  ? const Center(
                      child: Text('No pending technician requests'),
                    )
                  : ListView.builder(
                      itemCount: _technicianRequests.length,
                      itemBuilder: (context, index) {
                        final request = _technicianRequests[index];
                        return Card(
                          margin: const EdgeInsets.all(8),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  request['name'] ?? 'Unknown',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text('Email: ${request['email'] ?? 'N/A'}'),
                                if (request['phone'] != null)
                                  Text('Phone: ${request['phone']}'),
                                const SizedBox(height: 8),
                                Text(
                                  'Requested: ${_formatTimestamp(request['createdAt'])}',
                                  style: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                                if (request['documents'] != null && (request['documents'] as List).isNotEmpty)
                                  _buildDocumentsList(request['documents'] as List),
                                const SizedBox(height: 16),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => _rejectTechnician(request['id']),
                                      child: const Text('Reject'),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveTechnician(request['id']),
                                      child: const Text('Approve'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }

  Widget _buildDocumentsList(List documents) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        const Text(
          'Documents:',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...documents.map((doc) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              const Icon(Icons.description, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${doc['type']}: ${doc['name'] ?? 'Document'}',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is Timestamp) {
      final date = timestamp.toDate();
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute}';
    }

    return 'N/A';
  }
}
