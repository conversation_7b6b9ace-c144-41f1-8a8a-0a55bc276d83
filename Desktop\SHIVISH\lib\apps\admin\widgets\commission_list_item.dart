import 'package:flutter/material.dart';
import 'package:shivish/shared/models/commission.dart';

class CommissionListItem extends StatelessWidget {
  final Commission commission;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const CommissionListItem({
    super.key,
    required this.commission,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        title: Text(commission.categoryId),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Percentage: ${commission.percentage}%'),
            Text('Minimum Amount: ${commission.minimumAmount}'),
            Text('Maximum Amount: ${commission.maximumAmount}'),
            Text('Is Active: ${commission.isActive}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: onEdit,
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: onDelete,
            ),
          ],
        ),
      ),
    );
  }
}
