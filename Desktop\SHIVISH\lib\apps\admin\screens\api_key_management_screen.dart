import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_bloc.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_event.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_state.dart';
import 'package:shivish/apps/admin/widgets/api_key_list_item.dart';
import 'package:shivish/shared/models/api_key.dart';
import 'package:shivish/shared/ui_components/dialogs/api_key_form_dialog.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/ui_components/states/empty_state.dart';

class ApiKeyManagementScreen extends StatelessWidget {
  const ApiKeyManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Key Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateDialog(context),
          ),
        ],
      ),
      body: BlocConsumer<ApiKeyBloc, ApiKeyState>(
        listener: (context, state) {
          state.maybeWhen(
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: ErrorMessage(message: message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return state.when(
            initial: () {
              context.read<ApiKeyBloc>().add(const ApiKeyEvent.load());
              return const Center(child: LoadingIndicator());
            },
            loading: () => const Center(child: LoadingIndicator()),
            loaded: (apiKeys) {
              if (apiKeys.isEmpty) {
                return EmptyState(
                  icon: Icons.key,
                  message: 'Create your first API key to get started.',
                  actionLabel: 'Create API Key',
                  onAction: () => _showCreateDialog(context),
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  context.read<ApiKeyBloc>().add(const ApiKeyEvent.load());
                },
                child: ListView.builder(
                  itemCount: apiKeys.length,
                  itemBuilder: (context, index) {
                    return ApiKeyListItem(apiKey: apiKeys[index]);
                  },
                ),
              );
            },
            error: (message) => Center(
              child: ErrorMessage(
                message: message,
                onRetry: () {
                  context.read<ApiKeyBloc>().add(const ApiKeyEvent.load());
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _showCreateDialog(BuildContext context) async {
    final result = await showDialog<ApiKey>(
      context: context,
      builder: (context) => const ApiKeyFormDialog(),
    );

    if (result != null && context.mounted) {
      context.read<ApiKeyBloc>().add(
            ApiKeyEvent.create(result),
          );
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(
          message: 'API key created successfully',
        ),
      );
    }
  }
}
