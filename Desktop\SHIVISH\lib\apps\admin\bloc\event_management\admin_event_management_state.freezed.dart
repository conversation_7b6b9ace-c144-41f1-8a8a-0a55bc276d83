// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'admin_event_management_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AdminEventManagementState {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AdminEventManagementStateCopyWith<$Res> {
  factory $AdminEventManagementStateCopyWith(AdminEventManagementState value,
          $Res Function(AdminEventManagementState) then) =
      _$AdminEventManagementStateCopyWithImpl<$Res, AdminEventManagementState>;
}

/// @nodoc
class _$AdminEventManagementStateCopyWithImpl<$Res,
        $Val extends AdminEventManagementState>
    implements $AdminEventManagementStateCopyWith<$Res> {
  _$AdminEventManagementStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$InitialImplCopyWith<$Res> {
  factory _$$InitialImplCopyWith(
          _$InitialImpl value, $Res Function(_$InitialImpl) then) =
      __$$InitialImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$InitialImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$InitialImpl>
    implements _$$InitialImplCopyWith<$Res> {
  __$$InitialImplCopyWithImpl(
      _$InitialImpl _value, $Res Function(_$InitialImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$InitialImpl implements _Initial {
  const _$InitialImpl();

  @override
  String toString() {
    return 'AdminEventManagementState.initial()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$InitialImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return initial();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return initial?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return initial(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return initial?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (initial != null) {
      return initial(this);
    }
    return orElse();
  }
}

abstract class _Initial implements AdminEventManagementState {
  const factory _Initial() = _$InitialImpl;
}

/// @nodoc
abstract class _$$LoadingImplCopyWith<$Res> {
  factory _$$LoadingImplCopyWith(
          _$LoadingImpl value, $Res Function(_$LoadingImpl) then) =
      __$$LoadingImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadingImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$LoadingImpl>
    implements _$$LoadingImplCopyWith<$Res> {
  __$$LoadingImplCopyWithImpl(
      _$LoadingImpl _value, $Res Function(_$LoadingImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadingImpl implements _Loading {
  const _$LoadingImpl();

  @override
  String toString() {
    return 'AdminEventManagementState.loading()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadingImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return loading();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return loading?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return loading(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return loading?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (loading != null) {
      return loading(this);
    }
    return orElse();
  }
}

abstract class _Loading implements AdminEventManagementState {
  const factory _Loading() = _$LoadingImpl;
}

/// @nodoc
abstract class _$$ErrorImplCopyWith<$Res> {
  factory _$$ErrorImplCopyWith(
          _$ErrorImpl value, $Res Function(_$ErrorImpl) then) =
      __$$ErrorImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$ErrorImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$ErrorImpl>
    implements _$$ErrorImplCopyWith<$Res> {
  __$$ErrorImplCopyWithImpl(
      _$ErrorImpl _value, $Res Function(_$ErrorImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$ErrorImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ErrorImpl implements _Error {
  const _$ErrorImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminEventManagementState.error(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      __$$ErrorImplCopyWithImpl<_$ErrorImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return error(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return error?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return error(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return error?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (error != null) {
      return error(this);
    }
    return orElse();
  }
}

abstract class _Error implements AdminEventManagementState {
  const factory _Error(final String message) = _$ErrorImpl;

  String get message;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorImplCopyWith<_$ErrorImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$LoadedPendingEventsImplCopyWith<$Res> {
  factory _$$LoadedPendingEventsImplCopyWith(_$LoadedPendingEventsImpl value,
          $Res Function(_$LoadedPendingEventsImpl) then) =
      __$$LoadedPendingEventsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<EventModel> events});
}

/// @nodoc
class __$$LoadedPendingEventsImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res,
        _$LoadedPendingEventsImpl>
    implements _$$LoadedPendingEventsImplCopyWith<$Res> {
  __$$LoadedPendingEventsImplCopyWithImpl(_$LoadedPendingEventsImpl _value,
      $Res Function(_$LoadedPendingEventsImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? events = null,
  }) {
    return _then(_$LoadedPendingEventsImpl(
      null == events
          ? _value._events
          : events // ignore: cast_nullable_to_non_nullable
              as List<EventModel>,
    ));
  }
}

/// @nodoc

class _$LoadedPendingEventsImpl implements _LoadedPendingEvents {
  const _$LoadedPendingEventsImpl(final List<EventModel> events)
      : _events = events;

  final List<EventModel> _events;
  @override
  List<EventModel> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  @override
  String toString() {
    return 'AdminEventManagementState.loadedPendingEvents(events: $events)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedPendingEventsImpl &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_events));

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedPendingEventsImplCopyWith<_$LoadedPendingEventsImpl> get copyWith =>
      __$$LoadedPendingEventsImplCopyWithImpl<_$LoadedPendingEventsImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return loadedPendingEvents(events);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return loadedPendingEvents?.call(events);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (loadedPendingEvents != null) {
      return loadedPendingEvents(events);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return loadedPendingEvents(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return loadedPendingEvents?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (loadedPendingEvents != null) {
      return loadedPendingEvents(this);
    }
    return orElse();
  }
}

abstract class _LoadedPendingEvents implements AdminEventManagementState {
  const factory _LoadedPendingEvents(final List<EventModel> events) =
      _$LoadedPendingEventsImpl;

  List<EventModel> get events;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedPendingEventsImplCopyWith<_$LoadedPendingEventsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EventApprovedImplCopyWith<$Res> {
  factory _$$EventApprovedImplCopyWith(
          _$EventApprovedImpl value, $Res Function(_$EventApprovedImpl) then) =
      __$$EventApprovedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$EventApprovedImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$EventApprovedImpl>
    implements _$$EventApprovedImplCopyWith<$Res> {
  __$$EventApprovedImplCopyWithImpl(
      _$EventApprovedImpl _value, $Res Function(_$EventApprovedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$EventApprovedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EventApprovedImpl implements _EventApproved {
  const _$EventApprovedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminEventManagementState.eventApproved(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventApprovedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventApprovedImplCopyWith<_$EventApprovedImpl> get copyWith =>
      __$$EventApprovedImplCopyWithImpl<_$EventApprovedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return eventApproved(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return eventApproved?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventApproved != null) {
      return eventApproved(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return eventApproved(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return eventApproved?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventApproved != null) {
      return eventApproved(this);
    }
    return orElse();
  }
}

abstract class _EventApproved implements AdminEventManagementState {
  const factory _EventApproved(final String message) = _$EventApprovedImpl;

  String get message;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventApprovedImplCopyWith<_$EventApprovedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EventRejectedImplCopyWith<$Res> {
  factory _$$EventRejectedImplCopyWith(
          _$EventRejectedImpl value, $Res Function(_$EventRejectedImpl) then) =
      __$$EventRejectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$EventRejectedImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$EventRejectedImpl>
    implements _$$EventRejectedImplCopyWith<$Res> {
  __$$EventRejectedImplCopyWithImpl(
      _$EventRejectedImpl _value, $Res Function(_$EventRejectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$EventRejectedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EventRejectedImpl implements _EventRejected {
  const _$EventRejectedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminEventManagementState.eventRejected(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventRejectedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventRejectedImplCopyWith<_$EventRejectedImpl> get copyWith =>
      __$$EventRejectedImplCopyWithImpl<_$EventRejectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return eventRejected(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return eventRejected?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventRejected != null) {
      return eventRejected(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return eventRejected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return eventRejected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventRejected != null) {
      return eventRejected(this);
    }
    return orElse();
  }
}

abstract class _EventRejected implements AdminEventManagementState {
  const factory _EventRejected(final String message) = _$EventRejectedImpl;

  String get message;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventRejectedImplCopyWith<_$EventRejectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EventCreatedImplCopyWith<$Res> {
  factory _$$EventCreatedImplCopyWith(
          _$EventCreatedImpl value, $Res Function(_$EventCreatedImpl) then) =
      __$$EventCreatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$EventCreatedImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$EventCreatedImpl>
    implements _$$EventCreatedImplCopyWith<$Res> {
  __$$EventCreatedImplCopyWithImpl(
      _$EventCreatedImpl _value, $Res Function(_$EventCreatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$EventCreatedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EventCreatedImpl implements _EventCreated {
  const _$EventCreatedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminEventManagementState.eventCreated(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventCreatedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventCreatedImplCopyWith<_$EventCreatedImpl> get copyWith =>
      __$$EventCreatedImplCopyWithImpl<_$EventCreatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return eventCreated(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return eventCreated?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventCreated != null) {
      return eventCreated(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return eventCreated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return eventCreated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventCreated != null) {
      return eventCreated(this);
    }
    return orElse();
  }
}

abstract class _EventCreated implements AdminEventManagementState {
  const factory _EventCreated(final String message) = _$EventCreatedImpl;

  String get message;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventCreatedImplCopyWith<_$EventCreatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$EventUpdatedImplCopyWith<$Res> {
  factory _$$EventUpdatedImplCopyWith(
          _$EventUpdatedImpl value, $Res Function(_$EventUpdatedImpl) then) =
      __$$EventUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$$EventUpdatedImplCopyWithImpl<$Res>
    extends _$AdminEventManagementStateCopyWithImpl<$Res, _$EventUpdatedImpl>
    implements _$$EventUpdatedImplCopyWith<$Res> {
  __$$EventUpdatedImplCopyWithImpl(
      _$EventUpdatedImpl _value, $Res Function(_$EventUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
  }) {
    return _then(_$EventUpdatedImpl(
      null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$EventUpdatedImpl implements _EventUpdated {
  const _$EventUpdatedImpl(this.message);

  @override
  final String message;

  @override
  String toString() {
    return 'AdminEventManagementState.eventUpdated(message: $message)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EventUpdatedImpl &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$EventUpdatedImplCopyWith<_$EventUpdatedImpl> get copyWith =>
      __$$EventUpdatedImplCopyWithImpl<_$EventUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String message) error,
    required TResult Function(List<EventModel> events) loadedPendingEvents,
    required TResult Function(String message) eventApproved,
    required TResult Function(String message) eventRejected,
    required TResult Function(String message) eventCreated,
    required TResult Function(String message) eventUpdated,
  }) {
    return eventUpdated(message);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String message)? error,
    TResult? Function(List<EventModel> events)? loadedPendingEvents,
    TResult? Function(String message)? eventApproved,
    TResult? Function(String message)? eventRejected,
    TResult? Function(String message)? eventCreated,
    TResult? Function(String message)? eventUpdated,
  }) {
    return eventUpdated?.call(message);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String message)? error,
    TResult Function(List<EventModel> events)? loadedPendingEvents,
    TResult Function(String message)? eventApproved,
    TResult Function(String message)? eventRejected,
    TResult Function(String message)? eventCreated,
    TResult Function(String message)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventUpdated != null) {
      return eventUpdated(message);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Error value) error,
    required TResult Function(_LoadedPendingEvents value) loadedPendingEvents,
    required TResult Function(_EventApproved value) eventApproved,
    required TResult Function(_EventRejected value) eventRejected,
    required TResult Function(_EventCreated value) eventCreated,
    required TResult Function(_EventUpdated value) eventUpdated,
  }) {
    return eventUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Error value)? error,
    TResult? Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult? Function(_EventApproved value)? eventApproved,
    TResult? Function(_EventRejected value)? eventRejected,
    TResult? Function(_EventCreated value)? eventCreated,
    TResult? Function(_EventUpdated value)? eventUpdated,
  }) {
    return eventUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Error value)? error,
    TResult Function(_LoadedPendingEvents value)? loadedPendingEvents,
    TResult Function(_EventApproved value)? eventApproved,
    TResult Function(_EventRejected value)? eventRejected,
    TResult Function(_EventCreated value)? eventCreated,
    TResult Function(_EventUpdated value)? eventUpdated,
    required TResult orElse(),
  }) {
    if (eventUpdated != null) {
      return eventUpdated(this);
    }
    return orElse();
  }
}

abstract class _EventUpdated implements AdminEventManagementState {
  const factory _EventUpdated(final String message) = _$EventUpdatedImpl;

  String get message;

  /// Create a copy of AdminEventManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$EventUpdatedImplCopyWith<_$EventUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
