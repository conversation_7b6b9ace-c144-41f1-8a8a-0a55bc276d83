import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/technician.dart';

part 'technician_state.freezed.dart';

@freezed
class TechnicianState with _$TechnicianState {
  const factory TechnicianState.initial() = _Initial;
  const factory TechnicianState.loading() = _Loading;
  const factory TechnicianState.loadingMore(List<Technician> technicians) =
      _LoadingMore;
  const factory TechnicianState.loaded(List<Technician> technicians) = _Loaded;
  const factory TechnicianState.error(String message) = _Error;
}
