import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/providers/voice_assistant_provider.dart';
import '../../../../shared/providers/ai_assistant_provider.dart';
import '../../../../shared/services/voice/voice_assistant_service.dart';
import '../../../../shared/ui_components/voice/voice_assistant_indicator.dart';
import '../../../../shared/ui_components/voice/floating_3d_assistant.dart';
import '../../services/admin_voice_command_handler.dart';

/// An overlay widget that shows the voice assistant status and handles commands for admin app
class AdminVoiceAssistantOverlay extends ConsumerStatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Creates an [AdminVoiceAssistantOverlay]
  const AdminVoiceAssistantOverlay({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<AdminVoiceAssistantOverlay> createState() =>
      _AdminVoiceAssistantOverlayState();
}

class _AdminVoiceAssistantOverlayState
    extends ConsumerState<AdminVoiceAssistantOverlay> {
  late VoiceAssistantService _voiceAssistant;
  AdminVoiceCommandHandler? _commandHandler;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _voiceAssistant = ref.read(voiceAssistantProvider);
    _initializeVoiceAssistant();
  }

  Future<void> _initializeVoiceAssistant() async {
    final initialized = await _voiceAssistant.initialize();
    if (initialized) {
      setState(() {
        _isInitialized = true;
      });

      // Start listening for wake words
      await _voiceAssistant.startListeningForWakeWord();

      // Listen for commands
      _voiceAssistant.commandStream.listen(_handleCommand);
    }
  }

  void _handleCommand(String command) {
    _commandHandler ??= AdminVoiceCommandHandler(context, _voiceAssistant);
    _commandHandler!.processCommand(command);
  }

  void _toggleVoiceAssistant() async {
    if (!_isInitialized) {
      await _initializeVoiceAssistant();
      return;
    }

    final state = _voiceAssistant.state;
    if (state == VoiceAssistantState.idle) {
      await _voiceAssistant.startListeningForWakeWord();
    } else {
      await _voiceAssistant.stopListening();
    }
  }

  @override
  Widget build(BuildContext context) {
    final stateAsync = ref.watch(voiceAssistantStateProvider);
    final floatEnabledAsync = ref.watch(aiAssistantFloatEnabledProvider);

    return Stack(
      children: [
        // Main content
        widget.child,

        // Voice assistant indicator
        Positioned(
          top: MediaQuery.of(context).padding.top + 8,
          right: 16,
          child: VoiceAssistantIndicator(
            showLabel: false,
            onTap: _toggleVoiceAssistant,
          ),
        ),

        // Floating 3D AI assistant (only shown if enabled)
        if (floatEnabledAsync.value == true) const Floating3DAssistant(),

        // Listening overlay when actively listening for commands
        if (stateAsync.value == VoiceAssistantState.listeningForCommand)
          Positioned.fill(
            child: _buildListeningOverlay(),
          ),
      ],
    );
  }

  Widget _buildListeningOverlay() {
    return Material(
      color: Colors.black.withAlpha(77),
      child: Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildPulsatingMic(),
              const SizedBox(height: 16),
              const Text(
                'Listening...',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Speak your command',
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Try saying: "go to users", "open settings",\n"show analytics", or "open api keys"',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPulsatingMic() {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.2),
      duration: const Duration(milliseconds: 1000),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: const Icon(
            Icons.mic,
            color: Colors.blue,
            size: 48,
          ),
        );
      },
      child: const Icon(
        Icons.mic,
        color: Colors.blue,
        size: 48,
      ),
    );
  }

  @override
  void dispose() {
    _voiceAssistant.stopListening();
    super.dispose();
  }
}
