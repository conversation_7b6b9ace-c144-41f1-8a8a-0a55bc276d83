import 'package:flutter/material.dart';
import 'package:shivish/shared/models/technician.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/ui_components/text/text.dart';

class TechnicianDeleteDialog extends StatelessWidget {
  final Technician technician;
  final Function(String) onDelete;

  const TechnicianDeleteDialog({
    super.key,
    required this.technician,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const AppText(
        'Delete Technician',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            'Are you sure you want to delete ${technician.name}?',
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 8),
          const AppText(
            'This action cannot be undone.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.red,
            ),
          ),
        ],
      ),
      actions: [
        AppButton(
          onPressed: () => Navigator.of(context).pop(),
          label: 'Cancel',
          variant: AppButtonVariant.text,
        ),
        AppButton(
          onPressed: () {
            onDelete(technician.id);
            Navigator.of(context).pop();
          },
          label: 'Delete',
          variant: AppButtonVariant.secondary,
        ),
      ],
    );
  }
}
