import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_bloc.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_event.dart';
import 'package:shivish/apps/admin/bloc/backup/backup_state.dart';
import 'package:shivish/apps/admin/widgets/backup_list_item.dart';
import 'package:shivish/shared/ui_components/dialogs/backup_form_dialog.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/ui_components/states/empty_state.dart';

class BackupManagementScreen extends StatelessWidget {
  const BackupManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateDialog(context),
          ),
        ],
      ),
      body: BlocConsumer<BackupBloc, BackupState>(
        listener: (context, state) {
          state.maybeWhen(
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(message),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return state.when(
            initial: () {
              context.read<BackupBloc>().add(const BackupEvent.loadBackups());
              return const LoadingIndicator();
            },
            loading: () => const LoadingIndicator(),
            loaded: (backups) {
              if (backups.isEmpty) {
                return EmptyState(
                  message: 'No backups found',
                  icon: Icons.backup,
                  actionLabel: 'Create Backup',
                  onAction: () => _showCreateDialog(context),
                );
              }
              return RefreshIndicator(
                onRefresh: () async {
                  context
                      .read<BackupBloc>()
                      .add(const BackupEvent.loadBackups());
                },
                child: ListView.builder(
                  itemCount: backups.length,
                  itemBuilder: (context, index) {
                    return BackupListItem(backup: backups[index]);
                  },
                ),
              );
            },
            error: (message) => Center(
              child: ErrorMessage(message: message),
            ),
          );
        },
      ),
    );
  }

  Future<void> _showCreateDialog(BuildContext context) async {
    final result = await showDialog(
      context: context,
      builder: (context) => const BackupFormDialog(),
    );

    if (result != null && context.mounted) {
      context.read<BackupBloc>().add(BackupEvent.createBackup(result));
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: 'Backup created successfully'),
      );
    }
  }
}
