import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String name,
    required String email,
    String? photoUrl,
    @Default(false) bool isEmailVerified,
    @Default(false) bool isPhoneVerified,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    @Default(false) bool isActive,
    @Default(false) bool isBlocked,
    @Default([]) List<String> roles,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}
