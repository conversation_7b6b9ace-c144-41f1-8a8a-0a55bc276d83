import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/models/config/refund_config_model.dart';
import '../../../shared/providers/system_config_provider.dart';

class RefundConfigSection extends ConsumerWidget {
  final RefundConfigModel config;

  const RefundConfigSection({
    super.key,
    required this.config,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Refund Configuration',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Max Refund Days',
              config.maxRefundDays,
              (value) => _updateConfig(
                ref,
                config.copyWith(maxRefundDays: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildNumberField(
              context,
              ref,
              'Max Refund Percentage',
              (config.maxRefundPercentage * 100).toInt(),
              (value) => _updateConfig(
                ref,
                config.copyWith(maxRefundPercentage: value / 100),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Require Reason',
              config.requireReason,
              (value) => _updateConfig(
                ref,
                config.copyWith(requireReason: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchField(
              context,
              ref,
              'Require Approval',
              config.requireApproval,
              (value) => _updateConfig(
                ref,
                config.copyWith(requireApproval: value),
              ),
            ),
            const SizedBox(height: 16),
            _buildValidReasonsField(
              context,
              ref,
              config.validReasons,
              (value) => _updateConfig(
                ref,
                config.copyWith(validReasons: value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberField(
    BuildContext context,
    WidgetRef ref,
    String label,
    int value,
    Function(int) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        SizedBox(
          width: 100,
          child: TextFormField(
            initialValue: value.toString(),
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              final number = int.tryParse(value);
              if (number != null) {
                onChanged(number);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField(
    BuildContext context,
    WidgetRef ref,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      children: [
        Expanded(
          child: Text(label),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildValidReasonsField(
    BuildContext context,
    WidgetRef ref,
    List<String> reasons,
    Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Valid Reasons'),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: reasons.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: reasons[index],
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        final newReasons = List<String>.from(reasons);
                        newReasons[index] = value;
                        onChanged(newReasons);
                      },
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () {
                      final newReasons = List<String>.from(reasons);
                      newReasons.removeAt(index);
                      onChanged(newReasons);
                    },
                  ),
                ],
              ),
            );
          },
        ),
        ElevatedButton.icon(
          onPressed: () {
            final newReasons = List<String>.from(reasons);
            newReasons.add('');
            onChanged(newReasons);
          },
          icon: const Icon(Icons.add),
          label: const Text('Add Reason'),
        ),
      ],
    );
  }

  Future<void> _updateConfig(
    WidgetRef ref,
    RefundConfigModel newConfig,
  ) async {
    await ref
        .read(systemConfigStateProvider.notifier)
        .updateRefundConfig(newConfig);
  }
}
