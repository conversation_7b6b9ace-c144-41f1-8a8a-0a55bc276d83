import 'dart:io';

abstract class AdminMediaManagementEvent {
  const AdminMediaManagementEvent();
}

class LoadPendingMediaEvent extends AdminMediaManagementEvent {
  const LoadPendingMediaEvent();
}

class ApproveMediaEvent extends AdminMediaManagementEvent {
  final String mediaId;
  final String adminId;

  const ApproveMediaEvent({
    required this.mediaId,
    required this.adminId,
  });
}

class RejectMediaEvent extends AdminMediaManagementEvent {
  final String mediaId;
  final String adminId;
  final String reason;

  const RejectMediaEvent({
    required this.mediaId,
    required this.adminId,
    required this.reason,
  });
}

class UploadMediaEvent extends AdminMediaManagementEvent {
  final File file;
  final String title;
  final String description;
  final String adminId;

  const UploadMediaEvent({
    required this.file,
    required this.title,
    required this.description,
    required this.adminId,
  });
}

class UpdateMediaEvent extends AdminMediaManagementEvent {
  final String mediaId;
  final String title;
  final String description;
  final File? file;

  const UpdateMediaEvent({
    required this.mediaId,
    required this.title,
    required this.description,
    this.file,
  });
}
