import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/services/refund_service.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_event.dart';
import 'package:shivish/apps/admin/bloc/refund/refund_state.dart';

@injectable
class RefundBloc extends Bloc<RefundEvent, RefundState> {
  final RefundService _refundService;

  RefundBloc(this._refundService) : super(const RefundState.initial()) {
    on<LoadRefunds>(_onLoadRefunds);
    on<CreateRefund>(_onCreateRefund);
    on<UpdateRefund>(_onUpdateRefund);
    on<DeleteRefund>(_onDeleteRefund);
    on<ApproveRefund>(_onApproveRefund);
    on<RejectRefund>(_onRejectRefund);
    on<ProcessRefund>(_onProcessRefund);
    on<CreateDispute>(_onCreateDispute);
    on<ResolveDispute>(_onResolveDispute);
    on<UpdatePartialRefund>(_onUpdatePartialRefund);
    on<UpdateRefundMethod>(_onUpdateRefundMethod);
    on<UpdateTrackingInfo>(_onUpdateTrackingInfo);
    on<AddAuditLog>(_onAddAuditLog);
  }

  Future<void> _onLoadRefunds(
    LoadRefunds event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onCreateRefund(
    CreateRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.createRefund(event.refund);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onUpdateRefund(
    UpdateRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.updateRefund(event.refund);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onDeleteRefund(
    DeleteRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.deleteRefund(event.id);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onApproveRefund(
    ApproveRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.approveRefund(event.id, event.approvedBy);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onRejectRefund(
    RejectRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.rejectRefund(
        event.id,
        event.rejectedBy,
        event.rejectionReason,
      );
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onProcessRefund(
    ProcessRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.processRefund(
        event.id,
        event.processedBy,
        event.transactionId,
      );
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onCreateDispute(
    CreateDispute event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.createDispute(event.id, event.disputeReason);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onResolveDispute(
    ResolveDispute event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.resolveDispute(
        event.id,
        event.resolvedBy,
        event.resolution,
      );
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onUpdatePartialRefund(
    UpdatePartialRefund event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.updatePartialRefund(
        event.id,
        event.partialAmount,
        event.partialReason,
      );
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onUpdateRefundMethod(
    UpdateRefundMethod event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.updateRefundMethod(event.id, event.method);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onUpdateTrackingInfo(
    UpdateTrackingInfo event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.updateTrackingInfo(event.id, event.tracking);
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }

  Future<void> _onAddAuditLog(
    AddAuditLog event,
    Emitter<RefundState> emit,
  ) async {
    try {
      emit(const RefundState.loading());
      await _refundService.addAuditLog(
        event.id,
        event.action,
        event.performedBy,
        event.details,
      );
      final refunds = await _refundService.getRefunds();
      emit(RefundState.loaded(refunds));
    } catch (e) {
      emit(RefundState.error(e.toString()));
    }
  }
}
