import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';


part 'register_request.freezed.dart';
part 'register_request.g.dart';

@freezed
class RegisterRequest with _$RegisterRequest {
  const factory RegisterRequest({
    required String email,
    required String password,
    required String displayName,
    String? phoneNumber,
    required UserRole role,
    Map<String, dynamic>? metadata,
  }) = _RegisterRequest;

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);
}
