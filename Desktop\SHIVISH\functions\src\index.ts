/**
 * Firebase Cloud Functions for Shivish App
 */

import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import { ecomExpressWebhook } from './webhooks/ecom_express_webhook';

// Initialize Firebase Admin
admin.initializeApp();

// Export webhook handlers
export const webhooks = {
  ecomExpress: ecomExpressWebhook,
};

// Export other functions
export const helloWorld = functions.https.onRequest((request, response) => {
  functions.logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase!");
});
