import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:file_picker/file_picker.dart';
import 'package:video_player/video_player.dart';
import 'package:just_audio/just_audio.dart';
import 'dart:io';
import '../../../shared/models/media/media_model.dart';
import '../../../shared/services/auth/auth_service.dart';
import '../bloc/media_management/admin_media_management_bloc.dart';
import '../bloc/media_management/admin_media_management_event_simple.dart';
import '../bloc/media_management/admin_media_management_state.dart';
import '../../../shared/ui_components/buttons/app_button.dart';

class AdminMediaFormScreen extends StatefulWidget {
  final MediaModel? media;

  const AdminMediaFormScreen({
    super.key,
    this.media,
  });

  @override
  State<AdminMediaFormScreen> createState() => _AdminMediaFormScreenState();
}

class _AdminMediaFormScreenState extends State<AdminMediaFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  File? _selectedFile;
  bool _isLoading = false;
  String? _currentUserId;

  // Media type selection
  MediaType _selectedMediaType = MediaType.image;

  // Media preview controllers
  VideoPlayerController? _videoController;
  AudioPlayer? _audioPlayer;
  bool _isPreviewReady = false;

  @override
  void initState() {
    super.initState();
    if (widget.media != null) {
      _titleController.text = widget.media!.title;
      _descriptionController.text = widget.media!.description;
      _selectedMediaType = widget.media!.type;

      // Initialize preview for existing media
      if (widget.media!.type == MediaType.video) {
        _initVideoPreviewFromUrl(widget.media!.url);
      } else if (widget.media!.type == MediaType.audio) {
        _initAudioPreviewFromUrl(widget.media!.url);
      }
    }
    _loadCurrentUserId();
  }

  Future<void> _initVideoPreviewFromUrl(String url) async {
    _videoController = VideoPlayerController.networkUrl(Uri.parse(url));
    try {
      await _videoController!.initialize();
      if (mounted) {
        setState(() {
          _isPreviewReady = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing video preview: $e');
    }
  }

  Future<void> _initAudioPreviewFromUrl(String url) async {
    _audioPlayer = AudioPlayer();
    try {
      await _audioPlayer!.setUrl(url);
      if (mounted) {
        setState(() {
          _isPreviewReady = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing audio preview: $e');
    }
  }

  Future<void> _loadCurrentUserId() async {
    final authService = context.read<AuthService>();
    final user = await authService.getCurrentUser();
    if (user != null && mounted) {
      setState(() {
        _currentUserId = user.id;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _videoController?.dispose();
    _audioPlayer?.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    // Determine file type based on selected media type
    FileType fileType;
    List<String>? allowedExtensions;

    switch (_selectedMediaType) {
      case MediaType.image:
        fileType = FileType.image;
        break;
      case MediaType.video:
        fileType = FileType.custom;
        allowedExtensions = ['mp4', 'mov', 'avi'];
        break;
      case MediaType.audio:
        fileType = FileType.custom;
        allowedExtensions = ['mp3', 'wav', 'aac', 'm4a'];
        break;
    }

    try {
      final result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);

        // Clean up previous preview controllers
        _videoController?.dispose();
        _audioPlayer?.dispose();
        _videoController = null;
        _audioPlayer = null;
        _isPreviewReady = false;

        // Initialize preview for the new file
        if (_selectedMediaType == MediaType.video) {
          _initVideoPreviewFromFile(file.path);
        } else if (_selectedMediaType == MediaType.audio) {
          _initAudioPreviewFromFile(file.path);
        }

        setState(() {
          _selectedFile = file;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error picking file: $e')),
        );
      }
    }
  }

  // Initialize video preview for local file
  Future<void> _initVideoPreviewFromFile(String filePath) async {
    _videoController = VideoPlayerController.file(File(filePath));
    try {
      await _videoController!.initialize();
      if (mounted) {
        setState(() {
          _isPreviewReady = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing video preview: $e');
    }
  }

  // Initialize audio preview for local file
  Future<void> _initAudioPreviewFromFile(String filePath) async {
    _audioPlayer = AudioPlayer();
    try {
      await _audioPlayer!.setFilePath(filePath);
      if (mounted) {
        setState(() {
          _isPreviewReady = true;
        });
      }
    } catch (e) {
      debugPrint('Error initializing audio preview: $e');
    }
  }

  // Helper method to get icon for media type
  IconData _getMediaTypeIcon(MediaType type) {
    switch (type) {
      case MediaType.image:
        return Icons.image;
      case MediaType.video:
        return Icons.videocam;
      case MediaType.audio:
        return Icons.audiotrack;
    }
  }

  // Build preview for existing media
  Widget _buildMediaPreview(MediaModel media) {
    switch (media.type) {
      case MediaType.image:
        return Image.network(
          media.url,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => const Icon(Icons.error),
        );
      case MediaType.video:
        if (_videoController != null && _isPreviewReady) {
          return Stack(
            alignment: Alignment.center,
            children: [
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
              IconButton(
                icon: Icon(
                  _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  size: 48,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    if (_videoController!.value.isPlaying) {
                      _videoController!.pause();
                    } else {
                      _videoController!.play();
                    }
                  });
                },
              ),
            ],
          );
        } else {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.videocam, size: 48),
                SizedBox(height: 8),
                Text('Video preview loading...'),
              ],
            ),
          );
        }
      case MediaType.audio:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.audiotrack, size: 48),
              const SizedBox(height: 8),
              const Text('Audio file'),
              if (_audioPlayer != null && _isPreviewReady) ...[
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        _audioPlayer!.playing ? Icons.pause : Icons.play_arrow,
                      ),
                      onPressed: () {
                        if (_audioPlayer!.playing) {
                          _audioPlayer!.pause();
                        } else {
                          _audioPlayer!.play();
                        }
                        setState(() {});
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.stop),
                      onPressed: () {
                        _audioPlayer!.stop();
                        setState(() {});
                      },
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
    }
  }

  // Build preview for selected file
  Widget _buildSelectedFilePreview() {
    if (_selectedFile == null) {
      return const Center(child: Text('No file selected'));
    }

    switch (_selectedMediaType) {
      case MediaType.image:
        return Image.file(
          _selectedFile!,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => const Icon(Icons.error),
        );
      case MediaType.video:
        if (_videoController != null && _isPreviewReady) {
          return Stack(
            alignment: Alignment.center,
            children: [
              AspectRatio(
                aspectRatio: _videoController!.value.aspectRatio,
                child: VideoPlayer(_videoController!),
              ),
              IconButton(
                icon: Icon(
                  _videoController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
                  size: 48,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    if (_videoController!.value.isPlaying) {
                      _videoController!.pause();
                    } else {
                      _videoController!.play();
                    }
                  });
                },
              ),
            ],
          );
        } else {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.videocam, size: 48),
                SizedBox(height: 8),
                Text('Video preview loading...'),
              ],
            ),
          );
        }
      case MediaType.audio:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.audiotrack, size: 48),
              const SizedBox(height: 8),
              Text(
                _selectedFile!.path.split('/').last,
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
              if (_audioPlayer != null && _isPreviewReady) ...[
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: Icon(
                        _audioPlayer!.playing ? Icons.pause : Icons.play_arrow,
                      ),
                      onPressed: () {
                        if (_audioPlayer!.playing) {
                          _audioPlayer!.pause();
                        } else {
                          _audioPlayer!.play();
                        }
                        setState(() {});
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.stop),
                      onPressed: () {
                        _audioPlayer!.stop();
                        setState(() {});
                      },
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    if (widget.media == null && _selectedFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a file')),
      );
      return;
    }

    if (_currentUserId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please wait while we load your user information'),
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      if (widget.media != null) {
        // Update existing media
        context.read<AdminMediaManagementBloc>().add(
              UpdateMediaEvent(
                mediaId: widget.media!.id,
                title: _titleController.text,
                description: _descriptionController.text,
                file: _selectedFile,
              ),
            );
      } else {
        // Upload new media
        context.read<AdminMediaManagementBloc>().add(
              UploadMediaEvent(
                file: _selectedFile!,
                title: _titleController.text,
                description: _descriptionController.text,
                adminId: _currentUserId!,
              ),
            );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.media == null ? 'Upload Media' : 'Edit Media'),
      ),
      body: BlocConsumer<AdminMediaManagementBloc, AdminMediaManagementState>(
        listener: (context, state) {
          state.maybeWhen(
            error: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
            },
            mediaUploaded: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
              Navigator.pop(context);
            },
            mediaUpdated: (message) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(message)),
              );
              Navigator.pop(context);
            },
            orElse: () {},
          );
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Media type selection
                  if (widget.media == null) ...[
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Media Type',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<MediaType>(
                              value: _selectedMediaType,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              items: MediaType.values.map((type) {
                                String label;
                                IconData icon;

                                switch (type) {
                                  case MediaType.image:
                                    label = 'Image';
                                    icon = Icons.image;
                                    break;
                                  case MediaType.video:
                                    label = 'Video';
                                    icon = Icons.videocam;
                                    break;
                                  case MediaType.audio:
                                    label = 'Audio';
                                    icon = Icons.audiotrack;
                                    break;
                                }

                                return DropdownMenuItem<MediaType>(
                                  value: type,
                                  child: Row(
                                    children: [
                                      Icon(icon),
                                      const SizedBox(width: 8),
                                      Text(label),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  setState(() {
                                    _selectedMediaType = value;
                                    // Clear selected file when changing type
                                    _selectedFile = null;
                                    // Clean up controllers
                                    _videoController?.dispose();
                                    _audioPlayer?.dispose();
                                    _videoController = null;
                                    _audioPlayer = null;
                                    _isPreviewReady = false;
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Media preview for existing media
                  if (widget.media != null) ...[
                    AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: _buildMediaPreview(widget.media!),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // File picker and preview for new media
                  AspectRatio(
                    aspectRatio: 16 / 9,
                    child: InkWell(
                      onTap: _pickFile,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey),
                        ),
                        child: _selectedFile != null
                            ? _buildSelectedFilePreview()
                            : Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    _getMediaTypeIcon(_selectedMediaType),
                                    size: 48,
                                    color: Colors.grey[600],
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Select ${_selectedMediaType.toString().split('.').last} file',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // Title field
                  TextFormField(
                    controller: _titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a title';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  // Description field
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  // Submit button
                  AppButton(
                    onPressed: _isLoading ? null : _submitForm,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          )
                        : Text(widget.media == null ? 'Upload' : 'Update'),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
