import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:video_player/video_player.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path/path.dart' as path;

class FilePreviewWidget extends HookWidget {
  final String filePath;
  final String fileType;

  const FilePreviewWidget({
    super.key,
    required this.filePath,
    required this.fileType,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isVideo = fileType.startsWith('video/');
    final isAudio = fileType.startsWith('audio/');

    if (isVideo) {
      return _VideoPreview(filePath: filePath);
    } else if (isAudio) {
      return _AudioPreview(filePath: filePath);
    } else {
      return Center(
        child: Text(
          'Preview not available for ${path.extension(filePath)}',
          style: theme.textTheme.bodyLarge,
        ),
      );
    }
  }
}

class _VideoPreview extends HookWidget {
  final String filePath;

  const _VideoPreview({required this.filePath});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final controller = useMemoized(
      () => VideoPlayerController.file(File(filePath)),
      [filePath],
    );
    final isPlaying = useState(false);
    final position = useState(Duration.zero);
    final duration = useState(Duration.zero);

    useEffect(() {
      controller.initialize().then((_) {
        duration.value = controller.value.duration;
      });

      // Listen to position changes
      controller.addListener(() {
        position.value = controller.value.position;
        isPlaying.value = controller.value.isPlaying;
      });

      return () => controller.dispose();
    }, [controller]);

    return Column(
      children: [
        AspectRatio(
          aspectRatio: controller.value.aspectRatio,
          child: VideoPlayer(controller),
        ),
        const SizedBox(height: 16),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: theme.colorScheme.primary,
            inactiveTrackColor: theme.colorScheme.primary.withValues(alpha: 0.2),
            thumbColor: theme.colorScheme.primary,
            overlayColor: theme.colorScheme.primary.withValues(alpha: 0.1),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
          ),
          child: Slider(
            value: position.value.inMilliseconds.toDouble(),
            max: duration.value.inMilliseconds.toDouble(),
            onChanged: (value) {
              position.value = Duration(milliseconds: value.toInt());
              controller.seekTo(position.value);
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(
                isPlaying.value ? Icons.pause : Icons.play_arrow,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                if (isPlaying.value) {
                  controller.pause();
                } else {
                  controller.play();
                }
              },
            ),
            const SizedBox(width: 16),
            Text(
              '${position.value.inMinutes}:${(position.value.inSeconds % 60).toString().padLeft(2, '0')} / '
              '${duration.value.inMinutes}:${(duration.value.inSeconds % 60).toString().padLeft(2, '0')}',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }
}

class _AudioPreview extends HookWidget {
  final String filePath;

  const _AudioPreview({required this.filePath});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final player = useMemoized(() => AudioPlayer(), []);
    final isPlaying = useState(false);
    final position = useState(Duration.zero);
    final duration = useState(Duration.zero);

    useEffect(() {
      player.setFilePath(filePath);

      // Listen to duration changes
      player.durationStream.listen((d) {
        if (d != null) duration.value = d;
      });

      // Listen to position changes
      player.positionStream.listen((p) {
        position.value = p;
      });

      // Listen to player state changes
      player.playerStateStream.listen((state) {
        isPlaying.value = state.playing;
      });

      return () => player.dispose();
    }, [player, filePath]);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.audio_file,
          size: 64,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(height: 24),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: theme.colorScheme.primary,
            inactiveTrackColor: theme.colorScheme.primary.withValues(alpha: 0.2),
            thumbColor: theme.colorScheme.primary,
            overlayColor: theme.colorScheme.primary.withValues(alpha: 0.1),
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 12),
          ),
          child: Slider(
            value: position.value.inMilliseconds.toDouble(),
            max: duration.value.inMilliseconds.toDouble(),
            onChanged: (value) {
              position.value = Duration(milliseconds: value.toInt());
              player.seek(position.value);
            },
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(
                isPlaying.value ? Icons.pause : Icons.play_arrow,
                color: theme.colorScheme.primary,
              ),
              onPressed: () {
                if (isPlaying.value) {
                  player.pause();
                } else {
                  player.play();
                }
              },
            ),
            const SizedBox(width: 16),
            Text(
              '${position.value.inMinutes}:${(position.value.inSeconds % 60).toString().padLeft(2, '0')} / '
              '${duration.value.inMinutes}:${(duration.value.inSeconds % 60).toString().padLeft(2, '0')}',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ],
    );
  }
}
