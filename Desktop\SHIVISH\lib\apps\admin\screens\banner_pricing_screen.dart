import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shivish/shared/models/banner/banner_pricing.dart';
import 'package:shivish/shared/providers/banner_pricing_provider.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class BannerPricingScreen extends ConsumerStatefulWidget {
  const BannerPricingScreen({super.key});

  @override
  ConsumerState<BannerPricingScreen> createState() =>
      _BannerPricingScreenState();
}

class _BannerPricingScreenState extends ConsumerState<BannerPricingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _basePriceController = TextEditingController();
  final Map<String, TextEditingController> _priorityControllers = {};
  final Map<String, TextEditingController> _durationControllers = {};
  final Map<String, TextEditingController> _seasonalControllers = {};
  final Map<String, TextEditingController> _categoryControllers = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void dispose() {
    _basePriceController.dispose();
    for (var controller in _priorityControllers.values) {
      controller.dispose();
    }
    for (var controller in _durationControllers.values) {
      controller.dispose();
    }
    for (var controller in _seasonalControllers.values) {
      controller.dispose();
    }
    for (var controller in _categoryControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _initializeControllers() {
    // Priority controllers
    for (var priority in ['1', '5', '10']) {
      _priorityControllers[priority] = TextEditingController();
    }

    // Duration controllers
    for (var duration in ['7', '15', '30']) {
      _durationControllers[duration] = TextEditingController();
    }

    // Seasonal controllers
    for (var season in ['festival', 'holiday', 'regular']) {
      _seasonalControllers[season] = TextEditingController();
    }

    // Category controllers
    for (var category in ['electronics', 'fashion', 'food', 'services']) {
      _categoryControllers[category] = TextEditingController();
    }
  }

  void _loadPricingData(BannerPricing? pricing) {
    if (pricing == null) {
      // Set default values
      _basePriceController.text = '100.0';
      _priorityControllers.forEach((key, controller) {
        controller.text = key == '1'
            ? '1.0'
            : key == '5'
                ? '1.5'
                : '2.0';
      });
      _durationControllers.forEach((key, controller) {
        controller.text = key == '7'
            ? '600.0'
            : key == '15'
                ? '1000.0'
                : '1800.0';
      });
      _seasonalControllers.forEach((key, controller) {
        controller.text = key == 'festival'
            ? '1.5'
            : key == 'holiday'
                ? '1.3'
                : '1.0';
      });
      _categoryControllers.forEach((key, controller) {
        controller.text = key == 'electronics'
            ? '1.2'
            : key == 'fashion'
                ? '1.1'
                : key == 'food'
                    ? '1.0'
                    : '0.9';
      });
      return;
    }

    _basePriceController.text = pricing.basePrice.toString();

    pricing.priorityMultiplier.forEach((priority, multiplier) {
      _priorityControllers[priority]?.text = multiplier.toString();
    });

    pricing.durationPrices.forEach((duration, price) {
      _durationControllers[duration]?.text = price.toString();
    });

    pricing.seasonalMultipliers.forEach((season, multiplier) {
      _seasonalControllers[season]?.text = multiplier.toString();
    });

    pricing.categoryMultipliers.forEach((category, multiplier) {
      _categoryControllers[category]?.text = multiplier.toString();
    });
  }

  Future<void> _savePricing() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      final pricing = BannerPricing(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        basePrice: double.parse(_basePriceController.text),
        priorityMultiplier: Map.fromEntries(
          _priorityControllers.entries.map(
            (e) => MapEntry(e.key, double.parse(e.value.text)),
          ),
        ),
        durationPrices: Map.fromEntries(
          _durationControllers.entries.map(
            (e) => MapEntry(e.key, double.parse(e.value.text)),
          ),
        ),
        seasonalMultipliers: Map.fromEntries(
          _seasonalControllers.entries.map(
            (e) => MapEntry(e.key, double.parse(e.value.text)),
          ),
        ),
        categoryMultipliers: Map.fromEntries(
          _categoryControllers.entries.map(
            (e) => MapEntry(e.key, double.parse(e.value.text)),
          ),
        ),
        aiSuggestedPrice: 0.0,
        lastUpdated: DateTime.now(),
        isActive: true,
      );

      await ref.read(bannerPricingServiceProvider).savePricing(pricing);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Pricing updated successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating pricing: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final pricingAsync = ref.watch(currentBannerPricingProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Banner Pricing Settings'),
      ),
      body: pricingAsync.when(
        data: (pricing) {
          _loadPricingData(pricing);
          return _buildForm();
        },
        loading: () => const LoadingIndicator(),
        error: (error, stack) => ErrorMessage(message: error.toString()),
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Base Settings',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _basePriceController,
                      decoration: const InputDecoration(
                        labelText: 'Base Price',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: _validateNumber,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildCardSection(
              'Priority Multipliers',
              _priorityControllers.entries.map((e) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: e.value,
                    decoration: InputDecoration(
                      labelText: 'Priority ${e.key} Multiplier',
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: _validateNumber,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            _buildCardSection(
              'Duration Prices',
              _durationControllers.entries.map((e) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: e.value,
                    decoration: InputDecoration(
                      labelText: '${e.key} Days Price',
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: _validateNumber,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            _buildCardSection(
              'Seasonal Multipliers',
              _seasonalControllers.entries.map((e) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: e.value,
                    decoration: InputDecoration(
                      labelText: '${e.key.toUpperCase()} Multiplier',
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: _validateNumber,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),
            _buildCardSection(
              'Category Multipliers',
              _categoryControllers.entries.map((e) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: e.value,
                    decoration: InputDecoration(
                      labelText: '${e.key.toUpperCase()} Multiplier',
                      border: const OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    validator: _validateNumber,
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _savePricing,
                child: const Text('Save Pricing Settings'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  String? _validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'This field is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    return null;
  }
}
