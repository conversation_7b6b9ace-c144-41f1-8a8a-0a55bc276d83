import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/shared/models/api_key.dart';
import 'package:shivish/shared/services/api_key/api_key_service.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_event.dart';
import 'package:shivish/apps/admin/bloc/api_key/api_key_state.dart';

@injectable
class ApiKeyBloc extends Bloc<ApiKeyEvent, ApiKeyState> {
  final ApiKeyService _apiKeyService;

  ApiKeyBloc(this._apiKeyService) : super(const ApiKeyState.initial()) {
    on<LoadApiKeys>(_onLoadApiKeys);
    on<CreateApiKey>(_onCreateApiKey);
    on<UpdateApiKey>(_onUpdateApiKey);
    on<DeleteApiKey>(_onDeleteApiKey);
    on<ToggleApiKeyStatus>(_onToggleApiKeyStatus);
    on<UpdateApiKeyRateLimit>(_onUpdateApiKeyRateLimit);
    on<UpdateApiKeyPermissions>(_onUpdateApiKeyPermissions);
    on<UpdateApiKeyMetadata>(_onUpdateApiKeyMetadata);
    on<UpdateApiKeyExpiryDate>(_onUpdateApiKeyExpiryDate);
  }

  Future<void> _onLoadApiKeys(
    LoadApiKeys event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onCreateApiKey(
    CreateApiKey event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      await _apiKeyService.createApiKey(
        name: event.apiKey.name,
        permissions: event.apiKey.permissions,
        rateLimit: event.apiKey.rateLimit,
        metadata: event.apiKey.metadata,
        expiresAt: event.apiKey.expiresAt,
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onUpdateApiKey(
    UpdateApiKey event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      await _apiKeyService.updateApiKey(event.apiKey);
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onDeleteApiKey(
    DeleteApiKey event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      await _apiKeyService.deleteApiKey(event.id);
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onToggleApiKeyStatus(
    ToggleApiKeyStatus event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      await _apiKeyService.updateApiKey(
        ApiKey(
          id: event.id,
          name: '', // Will be updated from server
          key: '', // Will be updated from server
          isActive: event.isActive,
          permissions: [], // Will be updated from server
          metadata: {}, // Will be updated from server
          rateLimit: {}, // Will be updated from server
          createdAt: DateTime.now(), // Will be updated from server
          updatedAt: DateTime.now(),
        ),
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onUpdateApiKeyRateLimit(
    UpdateApiKeyRateLimit event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      final currentApiKey = (await _apiKeyService.getApiKeys())
          .firstWhere((k) => k.id == event.id);
      await _apiKeyService.updateApiKey(
        currentApiKey.copyWith(
          rateLimit: event.rateLimit,
          updatedAt: DateTime.now(),
        ),
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onUpdateApiKeyPermissions(
    UpdateApiKeyPermissions event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      final currentApiKey = (await _apiKeyService.getApiKeys())
          .firstWhere((k) => k.id == event.id);
      await _apiKeyService.updateApiKey(
        currentApiKey.copyWith(
          permissions: event.permissions,
          updatedAt: DateTime.now(),
        ),
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onUpdateApiKeyMetadata(
    UpdateApiKeyMetadata event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      final currentApiKey = (await _apiKeyService.getApiKeys())
          .firstWhere((k) => k.id == event.id);
      await _apiKeyService.updateApiKey(
        currentApiKey.copyWith(
          metadata: event.metadata,
          updatedAt: DateTime.now(),
        ),
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }

  Future<void> _onUpdateApiKeyExpiryDate(
    UpdateApiKeyExpiryDate event,
    Emitter<ApiKeyState> emit,
  ) async {
    try {
      emit(const ApiKeyState.loading());
      final currentApiKey = (await _apiKeyService.getApiKeys())
          .firstWhere((k) => k.id == event.id);
      await _apiKeyService.updateApiKey(
        currentApiKey.copyWith(
          expiresAt: event.expiryDate,
          updatedAt: DateTime.now(),
        ),
      );
      final apiKeys = await _apiKeyService.getApiKeys();
      emit(ApiKeyState.loaded(apiKeys));
    } catch (e) {
      emit(ApiKeyState.error(e.toString()));
    }
  }
}
