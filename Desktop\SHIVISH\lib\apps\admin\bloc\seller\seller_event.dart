part of 'seller_bloc.dart';

@freezed
class SellerEvent with _$SellerEvent {
  const factory SellerEvent.loadSellers() = LoadSellers;
  const factory SellerEvent.loadMoreSellers() = LoadMoreSellers;
  const factory SellerEvent.updateSellerStatus({
    required Seller seller,
    required bool isActive,
    required bool isSuspended,
  }) = UpdateSellerStatus;
  const factory SellerEvent.updateSellerPerformance({
    required Seller seller,
    required double rating,
    required int totalReviews,
    required int totalOrders,
    required int totalProducts,
    required double totalRevenue,
  }) = UpdateSellerPerformance;
  const factory SellerEvent.deleteSeller({
    required Seller seller,
  }) = DeleteSeller;
  const factory SellerEvent.createSeller({
    required Seller seller,
  }) = CreateSeller;
  const factory SellerEvent.startRealtimeUpdates() = StartRealtimeUpdates;
  const factory SellerEvent.stopRealtimeUpdates() = StopRealtimeUpdates;
}
