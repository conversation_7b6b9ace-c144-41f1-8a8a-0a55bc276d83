import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../shared/models/media/media_model.dart';

part 'admin_media_management_state.freezed.dart';

@freezed
class AdminMediaManagementState with _$AdminMediaManagementState {
  const factory AdminMediaManagementState.initial() = _Initial;
  const factory AdminMediaManagementState.loading() = _Loading;
  const factory AdminMediaManagementState.error(String message) = _Error;
  const factory AdminMediaManagementState.loadedPendingMedia(
      List<MediaModel> mediaList) = _LoadedPendingMedia;
  const factory AdminMediaManagementState.mediaApproved(String message) =
      _MediaApproved;
  const factory AdminMediaManagementState.mediaRejected(String message) =
      _MediaRejected;
  const factory AdminMediaManagementState.mediaUploaded(String message) =
      _MediaUploaded;
  const factory AdminMediaManagementState.mediaUpdated(String message) =
      _MediaUpdated;
}
