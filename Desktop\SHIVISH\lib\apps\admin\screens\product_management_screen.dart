import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/admin/bloc/product/product_bloc.dart';
import 'package:shivish/apps/admin/bloc/product/product_event.dart';
import 'package:shivish/apps/admin/bloc/product/product_state.dart';
import 'package:shivish/apps/admin/widgets/product_list_item.dart';
import 'package:shivish/apps/admin/widgets/product_filter_dialog.dart';
import 'package:shivish/shared/core/service_locator.dart';
import 'package:shivish/shared/models/product/product_model.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class ProductManagementScreen extends StatelessWidget {
  const ProductManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          serviceLocator<ProductBloc>()..add(const ProductEvent.loadProducts()),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Product Management'),
          actions: [
            BlocBuilder<ProductBloc, ProductState>(
              builder: (context, state) {
                return IconButton(
                  icon: const Icon(Icons.filter_list),
                  onPressed: () => _showFilterDialog(context),
                );
              },
            ),
          ],
        ),
        body: BlocBuilder<ProductBloc, ProductState>(
          builder: (context, state) {
            return state.when(
              initial: () => const LoadingIndicator(),
              loading: () => const LoadingIndicator(),
              loadingMore: (products) => _buildProductList(
                context,
                products,
                isLoadingMore: true,
              ),
              loaded: (products) => _buildProductList(context, products),
              error: (message) => ErrorMessage(message: message),
            );
          },
        ),
      ),
    );
  }

  Future<void> _showFilterDialog(BuildContext context) async {
    final bloc = context.read<ProductBloc>();
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => ProductFilterDialog(
        currentFilters: bloc.filters,
      ),
    );

    if (result != null) {
      bloc.add(ProductEvent.applyFilters(result));
    }
  }

  Widget _buildProductList(
    BuildContext context,
    List<ProductModel> products, {
    bool isLoadingMore = false,
  }) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        if (notification is ScrollEndNotification &&
            notification.metrics.extentAfter == 0) {
          context
              .read<ProductBloc>()
              .add(const ProductEvent.loadMoreProducts());
        }
        return false;
      },
      child: RefreshIndicator(
        onRefresh: () async {
          context.read<ProductBloc>().add(const ProductEvent.loadProducts());
        },
        child: ListView.builder(
          itemCount: products.length + (isLoadingMore ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == products.length) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(8.0),
                  child: CircularProgressIndicator(),
                ),
              );
            }

            final product = products[index];
            return ProductListItem(
              product: product,
              onStatusUpdate: (product, data) {
                context.read<ProductBloc>().add(
                      ProductEvent.updateProductStatus(
                        product,
                        data['status'].toString(),
                        data['verificationNotes'] as String?,
                        data['isApproved'] as bool,
                      ),
                    );
              },
              onDelete: (product) {
                context
                    .read<ProductBloc>()
                    .add(ProductEvent.deleteProduct(product.id));
              },
            );
          },
        ),
      ),
    );
  }
}
