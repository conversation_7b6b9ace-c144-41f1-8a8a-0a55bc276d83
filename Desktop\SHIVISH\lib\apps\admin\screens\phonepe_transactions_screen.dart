import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/payment/phonepe_transaction_model.dart';
import '../../../shared/services/payment/payment_service_provider.dart';

/// Provider for PhonePe transactions
final phonePeTransactionsProvider = FutureProvider<List<PhonePeTransactionModel>>((ref) async {
  final transactionService = ref.watch(phonePeTransactionServiceProvider);
  return await transactionService.getUserTransactions();
});

/// Screen for viewing PhonePe transaction history
class PhonePeTransactionsScreen extends ConsumerWidget {
  const PhonePeTransactionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(phonePeTransactionsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('PhonePe Transactions'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: transactionsAsync.when(
        data: (transactions) {
          if (transactions.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.payment,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'No Transactions',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'You have not made any PhonePe transactions yet.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: transactions.length,
            itemBuilder: (context, index) {
              final transaction = transactions[index];
              return _buildTransactionCard(context, transaction);
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => Center(
          child: Text('Error loading transactions: $error'),
        ),
      ),
    );
  }

  /// Builds a transaction card
  Widget _buildTransactionCard(BuildContext context, PhonePeTransactionModel transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _showTransactionDetails(context, transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with amount and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '₹${(transaction.amount / 100).toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  _buildStatusChip(transaction.status),
                ],
              ),

              const SizedBox(height: 12),

              // Transaction ID
              Text(
                'Transaction ID: ${transaction.transactionId}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 8),

              // Order ID
              if (transaction.orderId != null)
                Text(
                  'Order ID: ${transaction.orderId}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),

              const SizedBox(height: 8),

              // Date and time
              Text(
                'Date: ${_formatDate(transaction.createdAt)}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 8),

              // Payment method
              Row(
                children: [
                  Icon(
                    Icons.payment,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    transaction.paymentInstrumentType ?? 'PhonePe',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds a status chip
  Widget _buildStatusChip(PhonePeTransactionStatus status) {
    Color color;
    String text;

    switch (status) {
      case PhonePeTransactionStatus.success:
        color = Colors.green;
        text = 'Successful';
        break;
      case PhonePeTransactionStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case PhonePeTransactionStatus.failed:
        color = Colors.red;
        text = 'Failed';
        break;
      case PhonePeTransactionStatus.cancelled:
        color = Colors.grey;
        text = 'Cancelled';
        break;
      case PhonePeTransactionStatus.unknown:
        color = Colors.grey;
        text = 'Unknown';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withAlpha(128),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: color,
        ),
      ),
    );
  }

  /// Shows transaction details
  void _showTransactionDetails(BuildContext context, PhonePeTransactionModel transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          controller: scrollController,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Center(
                  child: Container(
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Title
                const Text(
                  'Transaction Details',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 24),

                // Status
                Row(
                  children: [
                    _buildStatusChip(transaction.status),
                    const SizedBox(width: 12),
                    Text(
                      transaction.responseMessage ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Details
                _buildDetailItem('Amount', '₹${(transaction.amount / 100).toStringAsFixed(2)}'),
                _buildDetailItem('Transaction ID', transaction.transactionId),
                _buildDetailItem('Merchant Transaction ID', transaction.merchantTransactionId),
                if (transaction.orderId != null)
                  _buildDetailItem('Order ID', transaction.orderId!),
                _buildDetailItem('Date', _formatDate(transaction.createdAt)),
                _buildDetailItem('Time', _formatTime(transaction.createdAt)),
                _buildDetailItem('Payment Method', transaction.paymentInstrumentType ?? 'PhonePe'),
                if (transaction.customerPhone != null)
                  _buildDetailItem('Phone Number', transaction.customerPhone!),
                if (transaction.responseCode != null)
                  _buildDetailItem('Response Code', transaction.responseCode!),

                const SizedBox(height: 24),

                // Actions
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // View order button
                    if (transaction.orderId != null)
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          context.go('/admin/orders/${transaction.orderId}');
                        },
                        icon: const Icon(Icons.shopping_bag),
                        label: const Text('View Order'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),

                    // Close button
                    OutlinedButton.icon(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      label: const Text('Close'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds a detail item
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Formats a date
  String _formatDate(DateTime date) {
    final formatter = DateFormat('dd MMM yyyy');
    return formatter.format(date);
  }

  /// Formats a time
  String _formatTime(DateTime date) {
    final formatter = DateFormat('hh:mm a');
    return formatter.format(date);
  }
}
