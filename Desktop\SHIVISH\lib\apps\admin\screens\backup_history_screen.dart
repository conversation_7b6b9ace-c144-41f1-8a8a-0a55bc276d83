import 'package:flutter/material.dart';
import 'package:shivish/shared/models/backup.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shivish/shared/services/backup_service.dart';

class BackupHistoryScreen extends StatelessWidget {
  final List<Backup> backups;
  final BackupService _backupService =
      BackupService(FirebaseFirestore.instance);

  BackupHistoryScreen({
    super.key,
    required this.backups,
  });

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildStorageAccountsList(
      BuildContext context, List<StorageAccount> accounts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: accounts.map((account) {
        final syncStatus = account.syncStatus ?? 'Not synced';
        final lastSync = account.lastSyncAt != null
            ? _formatDate(account.lastSyncAt!)
            : 'Never';

        Color statusColor;
        switch (syncStatus.toLowerCase()) {
          case 'synced':
            statusColor = Colors.green;
            break;
          case 'syncing':
            statusColor = Colors.orange;
            break;
          case 'failed':
            statusColor = Colors.red;
            break;
          default:
            statusColor = Colors.grey;
        }

        return ListTile(
          leading: Icon(
            account.type == 'google_drive' ? Icons.cloud : Icons.folder,
            color: Theme.of(context).primaryColor,
          ),
          title: Text(account.email),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Last sync: $lastSync'),
              Row(
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                    margin: const EdgeInsets.only(right: 4),
                  ),
                  Text(
                    syncStatus,
                    style: TextStyle(color: statusColor),
                  ),
                ],
              ),
            ],
          ),
          trailing: IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => _refreshSync(account),
            tooltip: 'Refresh sync status',
          ),
        );
      }).toList(),
    );
  }

  Future<void> _refreshSync(StorageAccount account) async {
    try {
      await _backupService.updateAccountSyncStatus('default', account.id);
    } catch (e) {
      debugPrint('Error refreshing sync status: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup History'),
      ),
      body: ListView.builder(
        itemCount: backups.length,
        itemBuilder: (context, index) {
          final backup = backups[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: ExpansionTile(
              title: Text(backup.name),
              subtitle: Text('Created: ${_formatDate(backup.createdAt)}'),
              trailing: _buildStatusChip(backup.status),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Storage Destinations',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildStorageAccountsList(
                          context, backup.storageAccounts),
                      const Divider(),
                      _buildBackupDetails(backup),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    IconData icon;

    switch (status.toLowerCase()) {
      case 'completed':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'failed':
        color = Colors.red;
        icon = Icons.error;
        break;
      case 'pending':
        color = Colors.orange;
        icon = Icons.pending;
        break;
      case 'backing_up':
        color = Colors.blue;
        icon = Icons.backup;
        break;
      default:
        color = Colors.grey;
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            status,
            style: TextStyle(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupDetails(Backup backup) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('Status', backup.status),
        if (backup.lastBackupAt != null)
          _buildDetailRow('Last Backup', _formatDate(backup.lastBackupAt!)),
        if (backup.lastRestoredAt != null)
          _buildDetailRow('Last Restored', _formatDate(backup.lastRestoredAt!)),
        if (backup.lastValidatedAt != null)
          _buildDetailRow(
              'Last Validated', _formatDate(backup.lastValidatedAt!)),
        const SizedBox(height: 8),
        _buildDetailRow('Encryption',
            backup.settings.encryptionEnabled ? 'Enabled' : 'Disabled'),
        _buildDetailRow('Compression',
            backup.settings.compressionEnabled ? 'Enabled' : 'Disabled'),
        _buildDetailRow('Validation',
            backup.settings.validationEnabled ? 'Enabled' : 'Disabled'),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
