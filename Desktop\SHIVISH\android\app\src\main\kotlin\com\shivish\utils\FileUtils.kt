package com.shivish.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.nio.channels.FileChannel

/**
 * Utility class for handling file operations safely
 */
object FileUtils {
    private const val TAG = "FileUtils"
    
    /**
     * Safely create a directory if it doesn't exist
     */
    fun createDirectoryIfNotExists(directory: File): Boolean {
        return try {
            if (!directory.exists()) {
                val result = directory.mkdirs()
                if (result) {
                    Log.d(TAG, "Directory created: ${directory.absolutePath}")
                } else {
                    Log.e(TAG, "Failed to create directory: ${directory.absolutePath}")
                }
                result
            } else {
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating directory: ${e.message}")
            false
        }
    }
    
    /**
     * Safely copy a file
     */
    fun copyFile(sourceFile: File, destFile: File): <PERSON><PERSON>an {
        return try {
            if (!sourceFile.exists()) {
                Log.e(TAG, "Source file does not exist: ${sourceFile.absolutePath}")
                return false
            }
            
            // Create parent directory if it doesn't exist
            val parentDir = destFile.parentFile
            if (parentDir != null && !createDirectoryIfNotExists(parentDir)) {
                return false
            }
            
            // Copy the file
            FileInputStream(sourceFile).use { source ->
                FileOutputStream(destFile).use { dest ->
                    val sourceChannel = source.channel
                    val destChannel = dest.channel
                    destChannel.transferFrom(sourceChannel, 0, sourceChannel.size())
                }
            }
            
            Log.d(TAG, "File copied from ${sourceFile.absolutePath} to ${destFile.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error copying file: ${e.message}")
            false
        }
    }
    
    /**
     * Safely delete a file
     */
    fun deleteFile(file: File): Boolean {
        return try {
            if (file.exists()) {
                val result = file.delete()
                if (result) {
                    Log.d(TAG, "File deleted: ${file.absolutePath}")
                } else {
                    Log.e(TAG, "Failed to delete file: ${file.absolutePath}")
                }
                result
            } else {
                Log.d(TAG, "File does not exist: ${file.absolutePath}")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting file: ${e.message}")
            false
        }
    }
    
    /**
     * Safely write text to a file
     */
    fun writeTextToFile(file: File, text: String): Boolean {
        return try {
            // Create parent directory if it doesn't exist
            val parentDir = file.parentFile
            if (parentDir != null && !createDirectoryIfNotExists(parentDir)) {
                return false
            }
            
            // Write the text to the file
            FileOutputStream(file).use { outputStream ->
                outputStream.write(text.toByteArray())
            }
            
            Log.d(TAG, "Text written to file: ${file.absolutePath}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error writing text to file: ${e.message}")
            false
        }
    }
    
    /**
     * Safely read text from a file
     */
    fun readTextFromFile(file: File): String? {
        return try {
            if (!file.exists()) {
                Log.e(TAG, "File does not exist: ${file.absolutePath}")
                return null
            }
            
            // Read the text from the file
            val text = FileInputStream(file).use { inputStream ->
                inputStream.bufferedReader().use { reader ->
                    reader.readText()
                }
            }
            
            Log.d(TAG, "Text read from file: ${file.absolutePath}")
            text
        } catch (e: Exception) {
            Log.e(TAG, "Error reading text from file: ${e.message}")
            null
        }
    }
    
    /**
     * Get the app's private directory
     */
    fun getAppPrivateDir(context: Context): File {
        return context.filesDir
    }
    
    /**
     * Get the app's external directory
     */
    fun getAppExternalDir(context: Context): File? {
        return if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            context.getExternalFilesDir(null)
        } else {
            Log.w(TAG, "External storage is not available")
            null
        }
    }
    
    /**
     * Get the app's cache directory
     */
    fun getAppCacheDir(context: Context): File {
        return context.cacheDir
    }
    
    /**
     * Clear the app's cache directory
     */
    fun clearAppCache(context: Context): Boolean {
        return try {
            val cacheDir = context.cacheDir
            if (cacheDir.exists()) {
                val files = cacheDir.listFiles()
                if (files != null) {
                    for (file in files) {
                        deleteFile(file)
                    }
                }
                Log.d(TAG, "App cache cleared")
                true
            } else {
                Log.d(TAG, "App cache directory does not exist")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing app cache: ${e.message}")
            false
        }
    }
} 