// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'seller_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SellerEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerEventCopyWith<$Res> {
  factory $SellerEventCopyWith(
          SellerEvent value, $Res Function(SellerEvent) then) =
      _$SellerEventCopyWithImpl<$Res, SellerEvent>;
}

/// @nodoc
class _$SellerEventCopyWithImpl<$Res, $Val extends SellerEvent>
    implements $SellerEventCopyWith<$Res> {
  _$SellerEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadSellersImplCopyWith<$Res> {
  factory _$$LoadSellersImplCopyWith(
          _$LoadSellersImpl value, $Res Function(_$LoadSellersImpl) then) =
      __$$LoadSellersImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadSellersImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$LoadSellersImpl>
    implements _$$LoadSellersImplCopyWith<$Res> {
  __$$LoadSellersImplCopyWithImpl(
      _$LoadSellersImpl _value, $Res Function(_$LoadSellersImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadSellersImpl implements LoadSellers {
  const _$LoadSellersImpl();

  @override
  String toString() {
    return 'SellerEvent.loadSellers()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadSellersImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadSellers();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadSellers?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadSellers != null) {
      return loadSellers();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadSellers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadSellers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadSellers != null) {
      return loadSellers(this);
    }
    return orElse();
  }
}

abstract class LoadSellers implements SellerEvent {
  const factory LoadSellers() = _$LoadSellersImpl;
}

/// @nodoc
abstract class _$$LoadMoreSellersImplCopyWith<$Res> {
  factory _$$LoadMoreSellersImplCopyWith(_$LoadMoreSellersImpl value,
          $Res Function(_$LoadMoreSellersImpl) then) =
      __$$LoadMoreSellersImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadMoreSellersImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$LoadMoreSellersImpl>
    implements _$$LoadMoreSellersImplCopyWith<$Res> {
  __$$LoadMoreSellersImplCopyWithImpl(
      _$LoadMoreSellersImpl _value, $Res Function(_$LoadMoreSellersImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadMoreSellersImpl implements LoadMoreSellers {
  const _$LoadMoreSellersImpl();

  @override
  String toString() {
    return 'SellerEvent.loadMoreSellers()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadMoreSellersImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return loadMoreSellers();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return loadMoreSellers?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMoreSellers != null) {
      return loadMoreSellers();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return loadMoreSellers(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return loadMoreSellers?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (loadMoreSellers != null) {
      return loadMoreSellers(this);
    }
    return orElse();
  }
}

abstract class LoadMoreSellers implements SellerEvent {
  const factory LoadMoreSellers() = _$LoadMoreSellersImpl;
}

/// @nodoc
abstract class _$$UpdateSellerStatusImplCopyWith<$Res> {
  factory _$$UpdateSellerStatusImplCopyWith(_$UpdateSellerStatusImpl value,
          $Res Function(_$UpdateSellerStatusImpl) then) =
      __$$UpdateSellerStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Seller seller, bool isActive, bool isSuspended});

  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class __$$UpdateSellerStatusImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$UpdateSellerStatusImpl>
    implements _$$UpdateSellerStatusImplCopyWith<$Res> {
  __$$UpdateSellerStatusImplCopyWithImpl(_$UpdateSellerStatusImpl _value,
      $Res Function(_$UpdateSellerStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seller = null,
    Object? isActive = null,
    Object? isSuspended = null,
  }) {
    return _then(_$UpdateSellerStatusImpl(
      seller: null == seller
          ? _value.seller
          : seller // ignore: cast_nullable_to_non_nullable
              as Seller,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      isSuspended: null == isSuspended
          ? _value.isSuspended
          : isSuspended // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SellerCopyWith<$Res> get seller {
    return $SellerCopyWith<$Res>(_value.seller, (value) {
      return _then(_value.copyWith(seller: value));
    });
  }
}

/// @nodoc

class _$UpdateSellerStatusImpl implements UpdateSellerStatus {
  const _$UpdateSellerStatusImpl(
      {required this.seller,
      required this.isActive,
      required this.isSuspended});

  @override
  final Seller seller;
  @override
  final bool isActive;
  @override
  final bool isSuspended;

  @override
  String toString() {
    return 'SellerEvent.updateSellerStatus(seller: $seller, isActive: $isActive, isSuspended: $isSuspended)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateSellerStatusImpl &&
            (identical(other.seller, seller) || other.seller == seller) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.isSuspended, isSuspended) ||
                other.isSuspended == isSuspended));
  }

  @override
  int get hashCode => Object.hash(runtimeType, seller, isActive, isSuspended);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateSellerStatusImplCopyWith<_$UpdateSellerStatusImpl> get copyWith =>
      __$$UpdateSellerStatusImplCopyWithImpl<_$UpdateSellerStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updateSellerStatus(seller, isActive, isSuspended);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updateSellerStatus?.call(seller, isActive, isSuspended);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateSellerStatus != null) {
      return updateSellerStatus(seller, isActive, isSuspended);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updateSellerStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updateSellerStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateSellerStatus != null) {
      return updateSellerStatus(this);
    }
    return orElse();
  }
}

abstract class UpdateSellerStatus implements SellerEvent {
  const factory UpdateSellerStatus(
      {required final Seller seller,
      required final bool isActive,
      required final bool isSuspended}) = _$UpdateSellerStatusImpl;

  Seller get seller;
  bool get isActive;
  bool get isSuspended;

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateSellerStatusImplCopyWith<_$UpdateSellerStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateSellerPerformanceImplCopyWith<$Res> {
  factory _$$UpdateSellerPerformanceImplCopyWith(
          _$UpdateSellerPerformanceImpl value,
          $Res Function(_$UpdateSellerPerformanceImpl) then) =
      __$$UpdateSellerPerformanceImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {Seller seller,
      double rating,
      int totalReviews,
      int totalOrders,
      int totalProducts,
      double totalRevenue});

  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class __$$UpdateSellerPerformanceImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$UpdateSellerPerformanceImpl>
    implements _$$UpdateSellerPerformanceImplCopyWith<$Res> {
  __$$UpdateSellerPerformanceImplCopyWithImpl(
      _$UpdateSellerPerformanceImpl _value,
      $Res Function(_$UpdateSellerPerformanceImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seller = null,
    Object? rating = null,
    Object? totalReviews = null,
    Object? totalOrders = null,
    Object? totalProducts = null,
    Object? totalRevenue = null,
  }) {
    return _then(_$UpdateSellerPerformanceImpl(
      seller: null == seller
          ? _value.seller
          : seller // ignore: cast_nullable_to_non_nullable
              as Seller,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double,
      totalReviews: null == totalReviews
          ? _value.totalReviews
          : totalReviews // ignore: cast_nullable_to_non_nullable
              as int,
      totalOrders: null == totalOrders
          ? _value.totalOrders
          : totalOrders // ignore: cast_nullable_to_non_nullable
              as int,
      totalProducts: null == totalProducts
          ? _value.totalProducts
          : totalProducts // ignore: cast_nullable_to_non_nullable
              as int,
      totalRevenue: null == totalRevenue
          ? _value.totalRevenue
          : totalRevenue // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SellerCopyWith<$Res> get seller {
    return $SellerCopyWith<$Res>(_value.seller, (value) {
      return _then(_value.copyWith(seller: value));
    });
  }
}

/// @nodoc

class _$UpdateSellerPerformanceImpl implements UpdateSellerPerformance {
  const _$UpdateSellerPerformanceImpl(
      {required this.seller,
      required this.rating,
      required this.totalReviews,
      required this.totalOrders,
      required this.totalProducts,
      required this.totalRevenue});

  @override
  final Seller seller;
  @override
  final double rating;
  @override
  final int totalReviews;
  @override
  final int totalOrders;
  @override
  final int totalProducts;
  @override
  final double totalRevenue;

  @override
  String toString() {
    return 'SellerEvent.updateSellerPerformance(seller: $seller, rating: $rating, totalReviews: $totalReviews, totalOrders: $totalOrders, totalProducts: $totalProducts, totalRevenue: $totalRevenue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateSellerPerformanceImpl &&
            (identical(other.seller, seller) || other.seller == seller) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.totalReviews, totalReviews) ||
                other.totalReviews == totalReviews) &&
            (identical(other.totalOrders, totalOrders) ||
                other.totalOrders == totalOrders) &&
            (identical(other.totalProducts, totalProducts) ||
                other.totalProducts == totalProducts) &&
            (identical(other.totalRevenue, totalRevenue) ||
                other.totalRevenue == totalRevenue));
  }

  @override
  int get hashCode => Object.hash(runtimeType, seller, rating, totalReviews,
      totalOrders, totalProducts, totalRevenue);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateSellerPerformanceImplCopyWith<_$UpdateSellerPerformanceImpl>
      get copyWith => __$$UpdateSellerPerformanceImplCopyWithImpl<
          _$UpdateSellerPerformanceImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return updateSellerPerformance(
        seller, rating, totalReviews, totalOrders, totalProducts, totalRevenue);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return updateSellerPerformance?.call(
        seller, rating, totalReviews, totalOrders, totalProducts, totalRevenue);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateSellerPerformance != null) {
      return updateSellerPerformance(seller, rating, totalReviews, totalOrders,
          totalProducts, totalRevenue);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return updateSellerPerformance(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return updateSellerPerformance?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (updateSellerPerformance != null) {
      return updateSellerPerformance(this);
    }
    return orElse();
  }
}

abstract class UpdateSellerPerformance implements SellerEvent {
  const factory UpdateSellerPerformance(
      {required final Seller seller,
      required final double rating,
      required final int totalReviews,
      required final int totalOrders,
      required final int totalProducts,
      required final double totalRevenue}) = _$UpdateSellerPerformanceImpl;

  Seller get seller;
  double get rating;
  int get totalReviews;
  int get totalOrders;
  int get totalProducts;
  double get totalRevenue;

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateSellerPerformanceImplCopyWith<_$UpdateSellerPerformanceImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteSellerImplCopyWith<$Res> {
  factory _$$DeleteSellerImplCopyWith(
          _$DeleteSellerImpl value, $Res Function(_$DeleteSellerImpl) then) =
      __$$DeleteSellerImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Seller seller});

  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class __$$DeleteSellerImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$DeleteSellerImpl>
    implements _$$DeleteSellerImplCopyWith<$Res> {
  __$$DeleteSellerImplCopyWithImpl(
      _$DeleteSellerImpl _value, $Res Function(_$DeleteSellerImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seller = null,
  }) {
    return _then(_$DeleteSellerImpl(
      seller: null == seller
          ? _value.seller
          : seller // ignore: cast_nullable_to_non_nullable
              as Seller,
    ));
  }

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SellerCopyWith<$Res> get seller {
    return $SellerCopyWith<$Res>(_value.seller, (value) {
      return _then(_value.copyWith(seller: value));
    });
  }
}

/// @nodoc

class _$DeleteSellerImpl implements DeleteSeller {
  const _$DeleteSellerImpl({required this.seller});

  @override
  final Seller seller;

  @override
  String toString() {
    return 'SellerEvent.deleteSeller(seller: $seller)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteSellerImpl &&
            (identical(other.seller, seller) || other.seller == seller));
  }

  @override
  int get hashCode => Object.hash(runtimeType, seller);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteSellerImplCopyWith<_$DeleteSellerImpl> get copyWith =>
      __$$DeleteSellerImplCopyWithImpl<_$DeleteSellerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return deleteSeller(seller);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return deleteSeller?.call(seller);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deleteSeller != null) {
      return deleteSeller(seller);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return deleteSeller(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return deleteSeller?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (deleteSeller != null) {
      return deleteSeller(this);
    }
    return orElse();
  }
}

abstract class DeleteSeller implements SellerEvent {
  const factory DeleteSeller({required final Seller seller}) =
      _$DeleteSellerImpl;

  Seller get seller;

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteSellerImplCopyWith<_$DeleteSellerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CreateSellerImplCopyWith<$Res> {
  factory _$$CreateSellerImplCopyWith(
          _$CreateSellerImpl value, $Res Function(_$CreateSellerImpl) then) =
      __$$CreateSellerImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Seller seller});

  $SellerCopyWith<$Res> get seller;
}

/// @nodoc
class __$$CreateSellerImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$CreateSellerImpl>
    implements _$$CreateSellerImplCopyWith<$Res> {
  __$$CreateSellerImplCopyWithImpl(
      _$CreateSellerImpl _value, $Res Function(_$CreateSellerImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? seller = null,
  }) {
    return _then(_$CreateSellerImpl(
      seller: null == seller
          ? _value.seller
          : seller // ignore: cast_nullable_to_non_nullable
              as Seller,
    ));
  }

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SellerCopyWith<$Res> get seller {
    return $SellerCopyWith<$Res>(_value.seller, (value) {
      return _then(_value.copyWith(seller: value));
    });
  }
}

/// @nodoc

class _$CreateSellerImpl implements CreateSeller {
  const _$CreateSellerImpl({required this.seller});

  @override
  final Seller seller;

  @override
  String toString() {
    return 'SellerEvent.createSeller(seller: $seller)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateSellerImpl &&
            (identical(other.seller, seller) || other.seller == seller));
  }

  @override
  int get hashCode => Object.hash(runtimeType, seller);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateSellerImplCopyWith<_$CreateSellerImpl> get copyWith =>
      __$$CreateSellerImplCopyWithImpl<_$CreateSellerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return createSeller(seller);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return createSeller?.call(seller);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (createSeller != null) {
      return createSeller(seller);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return createSeller(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return createSeller?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (createSeller != null) {
      return createSeller(this);
    }
    return orElse();
  }
}

abstract class CreateSeller implements SellerEvent {
  const factory CreateSeller({required final Seller seller}) =
      _$CreateSellerImpl;

  Seller get seller;

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateSellerImplCopyWith<_$CreateSellerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StartRealtimeUpdatesImplCopyWith(_$StartRealtimeUpdatesImpl value,
          $Res Function(_$StartRealtimeUpdatesImpl) then) =
      __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$StartRealtimeUpdatesImpl>
    implements _$$StartRealtimeUpdatesImplCopyWith<$Res> {
  __$$StartRealtimeUpdatesImplCopyWithImpl(_$StartRealtimeUpdatesImpl _value,
      $Res Function(_$StartRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartRealtimeUpdatesImpl implements StartRealtimeUpdates {
  const _$StartRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'SellerEvent.startRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return startRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (startRealtimeUpdates != null) {
      return startRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class StartRealtimeUpdates implements SellerEvent {
  const factory StartRealtimeUpdates() = _$StartRealtimeUpdatesImpl;
}

/// @nodoc
abstract class _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  factory _$$StopRealtimeUpdatesImplCopyWith(_$StopRealtimeUpdatesImpl value,
          $Res Function(_$StopRealtimeUpdatesImpl) then) =
      __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StopRealtimeUpdatesImplCopyWithImpl<$Res>
    extends _$SellerEventCopyWithImpl<$Res, _$StopRealtimeUpdatesImpl>
    implements _$$StopRealtimeUpdatesImplCopyWith<$Res> {
  __$$StopRealtimeUpdatesImplCopyWithImpl(_$StopRealtimeUpdatesImpl _value,
      $Res Function(_$StopRealtimeUpdatesImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StopRealtimeUpdatesImpl implements StopRealtimeUpdates {
  const _$StopRealtimeUpdatesImpl();

  @override
  String toString() {
    return 'SellerEvent.stopRealtimeUpdates()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StopRealtimeUpdatesImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSellers,
    required TResult Function() loadMoreSellers,
    required TResult Function(Seller seller, bool isActive, bool isSuspended)
        updateSellerStatus,
    required TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)
        updateSellerPerformance,
    required TResult Function(Seller seller) deleteSeller,
    required TResult Function(Seller seller) createSeller,
    required TResult Function() startRealtimeUpdates,
    required TResult Function() stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSellers,
    TResult? Function()? loadMoreSellers,
    TResult? Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult? Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult? Function(Seller seller)? deleteSeller,
    TResult? Function(Seller seller)? createSeller,
    TResult? Function()? startRealtimeUpdates,
    TResult? Function()? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSellers,
    TResult Function()? loadMoreSellers,
    TResult Function(Seller seller, bool isActive, bool isSuspended)?
        updateSellerStatus,
    TResult Function(Seller seller, double rating, int totalReviews,
            int totalOrders, int totalProducts, double totalRevenue)?
        updateSellerPerformance,
    TResult Function(Seller seller)? deleteSeller,
    TResult Function(Seller seller)? createSeller,
    TResult Function()? startRealtimeUpdates,
    TResult Function()? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadSellers value) loadSellers,
    required TResult Function(LoadMoreSellers value) loadMoreSellers,
    required TResult Function(UpdateSellerStatus value) updateSellerStatus,
    required TResult Function(UpdateSellerPerformance value)
        updateSellerPerformance,
    required TResult Function(DeleteSeller value) deleteSeller,
    required TResult Function(CreateSeller value) createSeller,
    required TResult Function(StartRealtimeUpdates value) startRealtimeUpdates,
    required TResult Function(StopRealtimeUpdates value) stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadSellers value)? loadSellers,
    TResult? Function(LoadMoreSellers value)? loadMoreSellers,
    TResult? Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult? Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult? Function(DeleteSeller value)? deleteSeller,
    TResult? Function(CreateSeller value)? createSeller,
    TResult? Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult? Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
  }) {
    return stopRealtimeUpdates?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadSellers value)? loadSellers,
    TResult Function(LoadMoreSellers value)? loadMoreSellers,
    TResult Function(UpdateSellerStatus value)? updateSellerStatus,
    TResult Function(UpdateSellerPerformance value)? updateSellerPerformance,
    TResult Function(DeleteSeller value)? deleteSeller,
    TResult Function(CreateSeller value)? createSeller,
    TResult Function(StartRealtimeUpdates value)? startRealtimeUpdates,
    TResult Function(StopRealtimeUpdates value)? stopRealtimeUpdates,
    required TResult orElse(),
  }) {
    if (stopRealtimeUpdates != null) {
      return stopRealtimeUpdates(this);
    }
    return orElse();
  }
}

abstract class StopRealtimeUpdates implements SellerEvent {
  const factory StopRealtimeUpdates() = _$StopRealtimeUpdatesImpl;
}

/// @nodoc
mixin _$SellerState {
  List<Seller> get sellers => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get hasMore => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of SellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SellerStateCopyWith<SellerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SellerStateCopyWith<$Res> {
  factory $SellerStateCopyWith(
          SellerState value, $Res Function(SellerState) then) =
      _$SellerStateCopyWithImpl<$Res, SellerState>;
  @useResult
  $Res call(
      {List<Seller> sellers, bool isLoading, bool hasMore, String? error});
}

/// @nodoc
class _$SellerStateCopyWithImpl<$Res, $Val extends SellerState>
    implements $SellerStateCopyWith<$Res> {
  _$SellerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sellers = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      sellers: null == sellers
          ? _value.sellers
          : sellers // ignore: cast_nullable_to_non_nullable
              as List<Seller>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LoadedImplCopyWith<$Res>
    implements $SellerStateCopyWith<$Res> {
  factory _$$LoadedImplCopyWith(
          _$LoadedImpl value, $Res Function(_$LoadedImpl) then) =
      __$$LoadedImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<Seller> sellers, bool isLoading, bool hasMore, String? error});
}

/// @nodoc
class __$$LoadedImplCopyWithImpl<$Res>
    extends _$SellerStateCopyWithImpl<$Res, _$LoadedImpl>
    implements _$$LoadedImplCopyWith<$Res> {
  __$$LoadedImplCopyWithImpl(
      _$LoadedImpl _value, $Res Function(_$LoadedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SellerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sellers = null,
    Object? isLoading = null,
    Object? hasMore = null,
    Object? error = freezed,
  }) {
    return _then(_$LoadedImpl(
      sellers: null == sellers
          ? _value._sellers
          : sellers // ignore: cast_nullable_to_non_nullable
              as List<Seller>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _value.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$LoadedImpl implements Loaded {
  const _$LoadedImpl(
      {final List<Seller> sellers = const [],
      this.isLoading = false,
      this.hasMore = false,
      this.error})
      : _sellers = sellers;

  final List<Seller> _sellers;
  @override
  @JsonKey()
  List<Seller> get sellers {
    if (_sellers is EqualUnmodifiableListView) return _sellers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sellers);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool hasMore;
  @override
  final String? error;

  @override
  String toString() {
    return 'SellerState(sellers: $sellers, isLoading: $isLoading, hasMore: $hasMore, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LoadedImpl &&
            const DeepCollectionEquality().equals(other._sellers, _sellers) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_sellers), isLoading, hasMore, error);

  /// Create a copy of SellerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      __$$LoadedImplCopyWithImpl<_$LoadedImpl>(this, _$identity);
}

abstract class Loaded implements SellerState {
  const factory Loaded(
      {final List<Seller> sellers,
      final bool isLoading,
      final bool hasMore,
      final String? error}) = _$LoadedImpl;

  @override
  List<Seller> get sellers;
  @override
  bool get isLoading;
  @override
  bool get hasMore;
  @override
  String? get error;

  /// Create a copy of SellerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LoadedImplCopyWith<_$LoadedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
