import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../shared/controllers/backup_controller.dart';
import '../../../shared/models/backup_config.dart';
import '../../../shared/models/backup_metadata.dart';
import '../../../shared/services/backup/google_drive_manager.dart';
import '../../../shared/services/storage/hive_manager.dart';
import '../../../shared/widgets/backup/backup_list_widget.dart';
import '../../../shared/widgets/backup/backup_progress_widget.dart';

/// Admin screen for backup management
class BackupAdminScreen extends StatefulWidget {
  const BackupAdminScreen({super.key});

  @override
  State<BackupAdminScreen> createState() => _BackupAdminScreenState();
}

class _BackupAdminScreenState extends State<BackupAdminScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _availableSpace = 0;
  int _usedSpace = 0;
  bool _isLoadingSpace = false;
  final GoogleDriveManager _driveManager = GoogleDriveManager();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Load available backups when the screen is first shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final controller = Provider.of<BackupController>(context, listen: false);
      controller.loadAvailableBackups();
      _loadStorageInfo();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoadingSpace = true;
    });

    try {
      // Get local storage info
      final localSize = await HiveManager.getTotalDataSize();

      // Get Google Drive storage info
      final availableSpace = await _driveManager.getAvailableSpace();

      setState(() {
        _usedSpace = localSize;
        _availableSpace = availableSpace;
        _isLoadingSpace = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSpace = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load storage info: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Backups'),
            Tab(text: 'Settings'),
            Tab(text: 'Storage'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final controller =
                  Provider.of<BackupController>(context, listen: false);
              controller.loadAvailableBackups();
              _loadStorageInfo();
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBackupsTab(),
          _buildSettingsTab(),
          _buildStorageTab(),
        ],
      ),
      floatingActionButton: Consumer<BackupController>(
        builder: (context, controller, child) {
          return FloatingActionButton(
            onPressed: controller.isOperationInProgress
                ? null
                : controller.performBackupNow,
            tooltip: 'Create Backup',
            backgroundColor: controller.isOperationInProgress
                ? Colors.grey
                : Theme.of(context).primaryColor,
            child: const Icon(Icons.backup),
          );
        },
      ),
    );
  }

  Widget _buildBackupsTab() {
    return Consumer<BackupController>(
      builder: (context, controller, child) {
        return Column(
          children: [
            // Progress widget
            if (controller.isOperationInProgress)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: BackupProgressWidget(
                  progress: controller.progress,
                  onCancel: controller.cancelCurrentOperation,
                  isRestore: controller.isRestoreInProgress,
                ),
              ),

            // Error message
            if (controller.lastError != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  color: Colors.red[100],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      children: [
                        const Icon(Icons.error, color: Colors.red),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            controller.lastError!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

            // Backup list
            Expanded(
              child: controller.availableBackups == null
                  ? const Center(child: CircularProgressIndicator())
                  : BackupListWidget(
                      backups: controller.availableBackups!,
                      onRestore: (backup) =>
                          _showRestoreConfirmation(context, backup, controller),
                      onDelete: (backup) =>
                          _showDeleteConfirmation(context, backup, controller),
                      isLoading: controller.isLoadingBackups,
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSettingsTab() {
    return Consumer<BackupController>(
      builder: (context, controller, child) {
        final config = controller.currentConfig ?? const BackupConfig();

        return ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Backup Settings',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    // Auto backup
                    _buildSwitchSetting(
                      context,
                      'Auto Backup',
                      'Automatically backup data on a schedule',
                      config.enableAutoBackup,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableAutoBackup: value),
                      ),
                    ),

                    // Backup frequency
                    if (config.enableAutoBackup)
                      _buildDropdownSetting<String>(
                        context,
                        'Backup Frequency',
                        'How often to create backups',
                        config.backupFrequency,
                        {
                          'hourly': 'Every hour',
                          'daily': 'Once a day',
                          'weekly': 'Once a week',
                          'monthly': 'Once a month',
                        },
                        (value) => _updateConfig(
                          controller,
                          config.copyWith(backupFrequency: value),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Backup Locations',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    // Local backup
                    _buildSwitchSetting(
                      context,
                      'Local Backup',
                      'Store backups on this device',
                      config.enableLocalBackup,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableLocalBackup: value),
                      ),
                    ),

                    // Cloud backup
                    _buildSwitchSetting(
                      context,
                      'Cloud Backup',
                      'Store backups in Google Drive',
                      config.enableCloudBackup,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableCloudBackup: value),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Backup Options',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),

                    // Encryption
                    _buildSwitchSetting(
                      context,
                      'Encryption',
                      'Encrypt backup data for security',
                      config.enableEncryption,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableEncryption: value),
                      ),
                    ),

                    // Compression
                    _buildSwitchSetting(
                      context,
                      'Compression',
                      'Compress backup data to save space',
                      config.enableCompression,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableCompression: value),
                      ),
                    ),

                    // Validation
                    _buildSwitchSetting(
                      context,
                      'Validation',
                      'Add validation hash to verify backup integrity',
                      config.enableValidation,
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(enableValidation: value),
                      ),
                    ),

                    // Retention period
                    _buildDropdownSetting<int>(
                      context,
                      'Retention Period',
                      'How long to keep old backups',
                      config.retentionDays,
                      {
                        7: '1 week',
                        30: '1 month',
                        90: '3 months',
                        365: '1 year',
                      },
                      (value) => _updateConfig(
                        controller,
                        config.copyWith(retentionDays: value),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStorageTab() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Card(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Local Storage',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                _isLoadingSpace
                    ? const Center(child: CircularProgressIndicator())
                    : Column(
                        children: [
                          LinearProgressIndicator(
                            value: _usedSpace > 0
                                ? 0.5
                                : 0, // Placeholder value until we have total space
                            backgroundColor: Colors.grey[200],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                            minHeight: 10,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Used: ${HiveManager.formatSize(_usedSpace)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              Text(
                                'Free: Unknown',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ],
                      ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton.icon(
                      onPressed: _loadStorageInfo,
                      icon: const Icon(Icons.refresh),
                      label: const Text('Refresh'),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _showClearDataConfirmation(context),
                      icon: const Icon(Icons.delete),
                      label: const Text('Clear All Data'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor: Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Google Drive Storage',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                _isLoadingSpace
                    ? const Center(child: CircularProgressIndicator())
                    : Column(
                        children: [
                          LinearProgressIndicator(
                            value: _availableSpace > 0
                                ? 0.3
                                : 0, // Placeholder value
                            backgroundColor: Colors.grey[200],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                            minHeight: 10,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Used: Unknown',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              Text(
                                'Available: ${HiveManager.formatSize(_availableSpace)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ],
                      ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    OutlinedButton.icon(
                      onPressed: () => _showDriveAccountsDialog(context),
                      icon: const Icon(Icons.account_circle),
                      label: const Text('Manage Accounts'),
                    ),
                    ElevatedButton.icon(
                      onPressed: () => _createFolderStructure(context),
                      icon: const Icon(Icons.create_new_folder),
                      label: const Text('Create Folder Structure'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 4,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Backup Monitoring',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                Consumer<BackupController>(
                  builder: (context, controller, child) {
                    final backups = controller.availableBackups ?? [];
                    final totalBackups = backups.length;
                    final successfulBackups = backups
                        .where((b) => b.type == BackupType.regular)
                        .length;
                    final failedBackups = backups
                        .where((b) => b.type == BackupType.emergency)
                        .length;

                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem(
                                'Total', totalBackups.toString(), Icons.backup),
                            _buildStatItem(
                                'Successful',
                                successfulBackups.toString(),
                                Icons.check_circle,
                                color: Colors.green),
                            _buildStatItem(
                                'Failed', failedBackups.toString(), Icons.error,
                                color: Colors.red),
                          ],
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            // Navigate to detailed monitoring screen
                          },
                          icon: const Icon(Icons.analytics),
                          label: const Text('View Detailed Statistics'),
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchSetting(
    BuildContext context,
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildDropdownSetting<T>(
    BuildContext context,
    String title,
    String subtitle,
    T value,
    Map<T, String> options,
    ValueChanged<T> onChanged,
  ) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<T>(
        value: value,
        items: options.entries.map((entry) {
          return DropdownMenuItem<T>(
            value: entry.key,
            child: Text(entry.value),
          );
        }).toList(),
        onChanged: (newValue) {
          if (newValue != null) {
            onChanged(newValue);
          }
        },
      ),
    );
  }

  void _updateConfig(BackupController controller, BackupConfig newConfig) {
    controller.updateConfig(newConfig);
  }

  Future<void> _showRestoreConfirmation(
    BuildContext context,
    BackupMetadata backup,
    BackupController controller,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Restore Backup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to restore this backup? This will replace all current data.',
            ),
            const SizedBox(height: 16),
            Text('Date: ${backup.formattedDate}'),
            Text('Size: ${backup.formattedSize}'),
            Text(
                'Source: ${backup.source == BackupSource.local ? 'Local Storage' : 'Cloud Storage'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
            child: const Text('RESTORE'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await controller.restoreBackup(backup);
    }
  }

  Future<void> _showDeleteConfirmation(
    BuildContext context,
    BackupMetadata backup,
    BackupController controller,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Backup'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Are you sure you want to delete this backup? This action cannot be undone.',
            ),
            const SizedBox(height: 16),
            Text('Date: ${backup.formattedDate}'),
            Text('Size: ${backup.formattedSize}'),
            Text(
                'Source: ${backup.source == BackupSource.local ? 'Local Storage' : 'Cloud Storage'}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await controller.deleteBackup(backup);
    }
  }

  Future<void> _showClearDataConfirmation(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'Are you sure you want to clear all data? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('CLEAR'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await HiveManager.clearAllData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('All data cleared successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }

        _loadStorageInfo();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to clear data: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _createFolderStructure(BuildContext context) async {
    try {
      final folderIds = await _driveManager.createFolderStructure();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Folder structure created: ${folderIds.length} folders'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create folder structure: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildStatItem(String label, String value, IconData icon,
      {Color color = Colors.blue}) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(label, style: TextStyle(color: Colors.grey[600])),
      ],
    );
  }

  Future<void> _showDriveAccountsDialog(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Google Drive Accounts'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: Column(
            children: [
              const ListTile(
                leading: Icon(Icons.account_circle),
                title: Text('<EMAIL>'),
                subtitle: Text('Primary Account'),
                trailing: Icon(Icons.check_circle, color: Colors.green),
              ),
              const Divider(),
              ListTile(
                leading: const Icon(Icons.add_circle),
                title: const Text('Add Account'),
                onTap: () {
                  Navigator.pop(context);
                  // Implement add account functionality
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
