import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/api_key.dart';

part 'api_key_event.freezed.dart';

@freezed
class ApiKeyEvent with _$ApiKeyEvent {
  const factory ApiKeyEvent.load() = LoadApiKeys;

  const factory ApiKeyEvent.create(ApiKey apiKey) = CreateApiKey;

  const factory ApiKeyEvent.update(ApiKey apiKey) = UpdateApiKey;

  const factory ApiKeyEvent.delete(String id) = DeleteApiKey;

  const factory ApiKeyEvent.toggleStatus(String id, bool isActive) =
      ToggleApiKeyStatus;

  const factory ApiKeyEvent.updateRateLimit(
      String id, Map<String, int> rateLimit) = UpdateApiKeyRateLimit;

  const factory ApiKeyEvent.updatePermissions(
      String id, List<String> permissions) = UpdateApiKeyPermissions;

  const factory ApiKeyEvent.updateMetadata(
      String id, Map<String, dynamic> metadata) = UpdateApiKeyMetadata;

  const factory ApiKeyEvent.updateExpiryDate(String id, DateTime? expiryDate) =
      UpdateApiKeyExpiryDate;
}
