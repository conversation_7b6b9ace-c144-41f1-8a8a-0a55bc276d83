import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/services/event/event_service.dart';
import 'admin_event_management_event.dart';
import 'admin_event_management_state.dart';

class AdminEventManagementBloc
    extends Bloc<AdminEventManagementEvent, AdminEventManagementState> {
  final EventService eventService;

  AdminEventManagementBloc({
    required this.eventService,
  }) : super(const AdminEventManagementState.initial()) {
    on<LoadPendingEvents>(_onLoadPendingEvents);
    on<ApproveEvent>(_onApproveEvent);
    on<RejectEvent>(_onRejectEvent);
    on<CreateEvent>(_onCreateEvent);
    on<UpdateEvent>(_onUpdateEvent);
  }

  Future<void> _onLoadPendingEvents(
    LoadPendingEvents event,
    Emitter<AdminEventManagementState> emit,
  ) async {
    emit(const AdminEventManagementState.loading());
    try {
      final events = await eventService.getPublicEvents();
      emit(AdminEventManagementState.loadedPendingEvents(events));
    } catch (e) {
      emit(AdminEventManagementState.error(e.toString()));
    }
  }

  Future<void> _onApproveEvent(
    ApproveEvent event,
    Emitter<AdminEventManagementState> emit,
  ) async {
    emit(const AdminEventManagementState.loading());
    try {
      await eventService.approveEvent(event.eventId, event.adminId);
      emit(const AdminEventManagementState.eventApproved(
          'Event approved successfully'));
    } catch (e) {
      emit(AdminEventManagementState.error(e.toString()));
    }
  }

  Future<void> _onRejectEvent(
    RejectEvent event,
    Emitter<AdminEventManagementState> emit,
  ) async {
    emit(const AdminEventManagementState.loading());
    try {
      await eventService.rejectEvent(
        event.eventId,
        event.adminId,
        event.reason,
      );
      emit(const AdminEventManagementState.eventRejected(
          'Event rejected successfully'));
    } catch (e) {
      emit(AdminEventManagementState.error(e.toString()));
    }
  }

  Future<void> _onCreateEvent(
    CreateEvent event,
    Emitter<AdminEventManagementState> emit,
  ) async {
    emit(const AdminEventManagementState.loading());
    try {
      await eventService.createEvent(event.event);
      emit(const AdminEventManagementState.eventCreated(
          'Event created successfully'));
    } catch (e) {
      emit(AdminEventManagementState.error(e.toString()));
    }
  }

  Future<void> _onUpdateEvent(
    UpdateEvent event,
    Emitter<AdminEventManagementState> emit,
  ) async {
    emit(const AdminEventManagementState.loading());
    try {
      await eventService.updateEvent(event.event);
      emit(const AdminEventManagementState.eventUpdated(
          'Event updated successfully'));
    } catch (e) {
      emit(AdminEventManagementState.error(e.toString()));
    }
  }
}
