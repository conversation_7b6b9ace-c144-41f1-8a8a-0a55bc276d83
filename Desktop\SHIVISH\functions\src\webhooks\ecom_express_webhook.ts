import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import * as crypto from 'crypto';

/**
 * Handles webhooks from Ecom Express
 * 
 * This function receives webhook notifications from Ecom Express
 * and updates the order status accordingly.
 */
export const ecomExpressWebhook = functions.https.onRequest(async (request, response) => {
  // Only allow POST requests
  if (request.method !== 'POST') {
    response.status(405).send('Method Not Allowed');
    return;
  }

  try {
    // Get the webhook payload
    const payload = request.body;
    
    // Get the signature from the headers
    const signature = request.headers['x-ecomexpress-signature'] as string;
    
    if (!signature) {
      functions.logger.warn('Missing signature in Ecom Express webhook');
      response.status(401).send('Unauthorized: Missing signature');
      return;
    }
    
    // Get the webhook secret from Firestore
    const providerDoc = await admin.firestore()
      .collection('delivery_providers')
      .doc('ecom_express')
      .get();
    
    if (!providerDoc.exists) {
      functions.logger.error('Ecom Express provider configuration not found');
      response.status(500).send('Internal Server Error');
      return;
    }
    
    const providerData = providerDoc.data();
    const webhookSecret = providerData?.config?.webhook_secret;
    
    if (!webhookSecret) {
      functions.logger.error('Webhook secret not configured for Ecom Express');
      response.status(500).send('Internal Server Error');
      return;
    }
    
    // Verify the signature
    const isValid = verifySignature(
      JSON.stringify(payload),
      signature,
      webhookSecret
    );
    
    if (!isValid) {
      functions.logger.warn('Invalid signature in Ecom Express webhook');
      response.status(401).send('Unauthorized: Invalid signature');
      return;
    }
    
    // Extract tracking number and status
    const trackingNumber = payload.awb_number;
    const status = payload.status;
    
    if (!trackingNumber || !status) {
      functions.logger.warn('Missing tracking number or status in Ecom Express webhook');
      response.status(400).send('Bad Request: Missing tracking number or status');
      return;
    }
    
    // Find the order associated with this tracking number
    const orderQuery = await admin.firestore()
      .collection('orders')
      .where('shippingDetails.trackingNumber', '==', trackingNumber)
      .limit(1)
      .get();
    
    if (orderQuery.empty) {
      functions.logger.warn(`No order found for tracking number: ${trackingNumber}`);
      response.status(404).send('Not Found: No order found for tracking number');
      return;
    }
    
    const orderDoc = orderQuery.docs[0];
    const orderId = orderDoc.id;
    const orderData = orderDoc.data();
    
    // Map Ecom Express status to OrderStatus
    const orderStatus = mapEcomStatusToOrderStatus(status);
    
    // Update order status
    await admin.firestore()
      .collection('orders')
      .doc(orderId)
      .update({
        'status': orderStatus,
        'updatedAt': admin.firestore.FieldValue.serverTimestamp(),
      });
    
    // Add status update to order history
    await admin.firestore()
      .collection('orders')
      .doc(orderId)
      .collection('status_updates')
      .add({
        'status': orderStatus,
        'previousStatus': orderData.status,
        'timestamp': admin.firestore.FieldValue.serverTimestamp(),
        'source': 'ecom_express_webhook',
        'details': payload,
      });
    
    // Store webhook notification for reference
    await admin.firestore()
      .collection('ecom_express_webhooks')
      .add({
        'orderId': orderId,
        'trackingNumber': trackingNumber,
        'status': status,
        'payload': payload,
        'receivedAt': admin.firestore.FieldValue.serverTimestamp(),
        'processed': true,
      });
    
    functions.logger.info(`Processed Ecom Express webhook for order ${orderId}: ${status}`);
    
    // Send success response
    response.status(200).send('OK');
  } catch (error) {
    functions.logger.error('Error processing Ecom Express webhook:', error);
    
    // Store failed webhook
    try {
      await admin.firestore()
        .collection('ecom_express_webhooks')
        .add({
          'error': error.toString(),
          'payload': request.body,
          'headers': request.headers,
          'receivedAt': admin.firestore.FieldValue.serverTimestamp(),
          'processed': false,
        });
    } catch (storeError) {
      functions.logger.error('Error storing failed webhook:', storeError);
    }
    
    response.status(500).send('Internal Server Error');
  }
});

/**
 * Verify webhook signature
 * 
 * @param payload - The webhook payload as a string
 * @param signature - The signature from the webhook headers
 * @param secret - The webhook secret
 * @returns Whether the signature is valid
 */
function verifySignature(payload: string, signature: string, secret: string): boolean {
  try {
    const hmac = crypto.createHmac('sha256', secret);
    const digest = hmac.update(payload).digest('hex');
    return crypto.timingSafeEqual(
      Buffer.from(digest),
      Buffer.from(signature)
    );
  } catch (error) {
    functions.logger.error('Error verifying signature:', error);
    return false;
  }
}

/**
 * Map Ecom Express status to OrderStatus
 * 
 * @param ecomStatus - The status from Ecom Express
 * @returns The corresponding OrderStatus
 */
function mapEcomStatusToOrderStatus(ecomStatus: string): string {
  const status = ecomStatus.toUpperCase();
  
  if (status === 'PICKUP SCHEDULED' || status === 'PICKUP ASSIGNED') {
    return 'processing';
  }
  
  if (status === 'PICKUP COMPLETE' || status === 'SHIPMENT PICKED UP') {
    return 'inTransit';
  }
  
  if (status === 'IN TRANSIT' || status === 'SHIPPED') {
    return 'inTransit';
  }
  
  if (status === 'OUT FOR DELIVERY') {
    return 'outForDelivery';
  }
  
  if (status === 'DELIVERED') {
    return 'delivered';
  }
  
  if (status === 'CANCELLED' || status === 'RTO INITIATED' || status === 'RTO DELIVERED') {
    return 'cancelled';
  }
  
  return 'processing';
}
