import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:shivish/shared/models/ai_settings.dart';

part 'ai_settings_event.freezed.dart';

@freezed
class AISettingsEvent with _$AISettingsEvent {
  const factory AISettingsEvent.loadAISettings() = LoadAISettings;
  const factory AISettingsEvent.createAISettings(AISettings settings) =
      CreateAISettings;
  const factory AISettingsEvent.updateAISettings(AISettings settings) =
      UpdateAISettings;
  const factory AISettingsEvent.deleteAISettings(String id) = DeleteAISettings;
  const factory AISettingsEvent.updateVoiceCommandSettings(
    String id,
    Map<String, dynamic> settings,
  ) = UpdateVoiceCommandSettings;
  const factory AISettingsEvent.updateChatbotSettings(
    String id,
    Map<String, dynamic> settings,
  ) = UpdateChatbotSettings;
  const factory AISettingsEvent.updateLanguageModelSettings(
    String id,
    Map<String, dynamic> settings,
  ) = UpdateLanguageModelSettings;
  const factory AISettingsEvent.updateResponseCustomization(
    String id,
    Map<String, dynamic> settings,
  ) = UpdateResponseCustomization;
  const factory AISettingsEvent.updateVoiceRecognitionSettings(
    String id,
    Map<String, dynamic> settings,
  ) = UpdateVoiceRecognitionSettings;
}
