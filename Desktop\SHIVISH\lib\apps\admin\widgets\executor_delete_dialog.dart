import 'package:flutter/material.dart';
import '../../../../shared/models/executor.dart';

class ExecutorDeleteDialog extends StatelessWidget {
  final Executor executor;

  const ExecutorDeleteDialog({
    super.key,
    required this.executor,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Executor'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Are you sure you want to delete ${executor.name}?'),
          const SizedBox(height: 8),
          Text('Email: ${executor.email}'),
          Text('Phone: ${executor.phone}'),
          Text('Status: ${executor.status}'),
          const SizedBox(height: 16),
          const Text(
            'This action cannot be undone. The executor will be marked as deleted.',
            style: TextStyle(color: Colors.red),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context, false),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () => Navigator.pop(context, true),
          style: FilledButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
          child: const Text('Delete'),
        ),
      ],
    );
  }
}
