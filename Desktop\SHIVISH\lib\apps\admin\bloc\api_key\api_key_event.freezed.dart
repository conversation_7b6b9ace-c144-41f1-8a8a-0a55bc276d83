// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_key_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ApiKeyEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ApiKeyEventCopyWith<$Res> {
  factory $ApiKeyEventCopyWith(
          ApiKeyEvent value, $Res Function(ApiKeyEvent) then) =
      _$ApiKeyEventCopyWithImpl<$Res, ApiKeyEvent>;
}

/// @nodoc
class _$ApiKeyEventCopyWithImpl<$Res, $Val extends ApiKeyEvent>
    implements $ApiKeyEventCopyWith<$Res> {
  _$ApiKeyEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadApiKeysImplCopyWith<$Res> {
  factory _$$LoadApiKeysImplCopyWith(
          _$LoadApiKeysImpl value, $Res Function(_$LoadApiKeysImpl) then) =
      __$$LoadApiKeysImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadApiKeysImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$LoadApiKeysImpl>
    implements _$$LoadApiKeysImplCopyWith<$Res> {
  __$$LoadApiKeysImplCopyWithImpl(
      _$LoadApiKeysImpl _value, $Res Function(_$LoadApiKeysImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadApiKeysImpl implements LoadApiKeys {
  const _$LoadApiKeysImpl();

  @override
  String toString() {
    return 'ApiKeyEvent.load()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadApiKeysImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return load();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return load?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (load != null) {
      return load();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return load(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return load?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (load != null) {
      return load(this);
    }
    return orElse();
  }
}

abstract class LoadApiKeys implements ApiKeyEvent {
  const factory LoadApiKeys() = _$LoadApiKeysImpl;
}

/// @nodoc
abstract class _$$CreateApiKeyImplCopyWith<$Res> {
  factory _$$CreateApiKeyImplCopyWith(
          _$CreateApiKeyImpl value, $Res Function(_$CreateApiKeyImpl) then) =
      __$$CreateApiKeyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ApiKey apiKey});

  $ApiKeyCopyWith<$Res> get apiKey;
}

/// @nodoc
class __$$CreateApiKeyImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$CreateApiKeyImpl>
    implements _$$CreateApiKeyImplCopyWith<$Res> {
  __$$CreateApiKeyImplCopyWithImpl(
      _$CreateApiKeyImpl _value, $Res Function(_$CreateApiKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiKey = null,
  }) {
    return _then(_$CreateApiKeyImpl(
      null == apiKey
          ? _value.apiKey
          : apiKey // ignore: cast_nullable_to_non_nullable
              as ApiKey,
    ));
  }

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApiKeyCopyWith<$Res> get apiKey {
    return $ApiKeyCopyWith<$Res>(_value.apiKey, (value) {
      return _then(_value.copyWith(apiKey: value));
    });
  }
}

/// @nodoc

class _$CreateApiKeyImpl implements CreateApiKey {
  const _$CreateApiKeyImpl(this.apiKey);

  @override
  final ApiKey apiKey;

  @override
  String toString() {
    return 'ApiKeyEvent.create(apiKey: $apiKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CreateApiKeyImpl &&
            (identical(other.apiKey, apiKey) || other.apiKey == apiKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, apiKey);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CreateApiKeyImplCopyWith<_$CreateApiKeyImpl> get copyWith =>
      __$$CreateApiKeyImplCopyWithImpl<_$CreateApiKeyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return create(apiKey);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return create?.call(apiKey);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (create != null) {
      return create(apiKey);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return create(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return create?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (create != null) {
      return create(this);
    }
    return orElse();
  }
}

abstract class CreateApiKey implements ApiKeyEvent {
  const factory CreateApiKey(final ApiKey apiKey) = _$CreateApiKeyImpl;

  ApiKey get apiKey;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CreateApiKeyImplCopyWith<_$CreateApiKeyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateApiKeyImplCopyWith<$Res> {
  factory _$$UpdateApiKeyImplCopyWith(
          _$UpdateApiKeyImpl value, $Res Function(_$UpdateApiKeyImpl) then) =
      __$$UpdateApiKeyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({ApiKey apiKey});

  $ApiKeyCopyWith<$Res> get apiKey;
}

/// @nodoc
class __$$UpdateApiKeyImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$UpdateApiKeyImpl>
    implements _$$UpdateApiKeyImplCopyWith<$Res> {
  __$$UpdateApiKeyImplCopyWithImpl(
      _$UpdateApiKeyImpl _value, $Res Function(_$UpdateApiKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? apiKey = null,
  }) {
    return _then(_$UpdateApiKeyImpl(
      null == apiKey
          ? _value.apiKey
          : apiKey // ignore: cast_nullable_to_non_nullable
              as ApiKey,
    ));
  }

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ApiKeyCopyWith<$Res> get apiKey {
    return $ApiKeyCopyWith<$Res>(_value.apiKey, (value) {
      return _then(_value.copyWith(apiKey: value));
    });
  }
}

/// @nodoc

class _$UpdateApiKeyImpl implements UpdateApiKey {
  const _$UpdateApiKeyImpl(this.apiKey);

  @override
  final ApiKey apiKey;

  @override
  String toString() {
    return 'ApiKeyEvent.update(apiKey: $apiKey)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateApiKeyImpl &&
            (identical(other.apiKey, apiKey) || other.apiKey == apiKey));
  }

  @override
  int get hashCode => Object.hash(runtimeType, apiKey);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateApiKeyImplCopyWith<_$UpdateApiKeyImpl> get copyWith =>
      __$$UpdateApiKeyImplCopyWithImpl<_$UpdateApiKeyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return update(apiKey);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return update?.call(apiKey);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(apiKey);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return update(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return update?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (update != null) {
      return update(this);
    }
    return orElse();
  }
}

abstract class UpdateApiKey implements ApiKeyEvent {
  const factory UpdateApiKey(final ApiKey apiKey) = _$UpdateApiKeyImpl;

  ApiKey get apiKey;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateApiKeyImplCopyWith<_$UpdateApiKeyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteApiKeyImplCopyWith<$Res> {
  factory _$$DeleteApiKeyImplCopyWith(
          _$DeleteApiKeyImpl value, $Res Function(_$DeleteApiKeyImpl) then) =
      __$$DeleteApiKeyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id});
}

/// @nodoc
class __$$DeleteApiKeyImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$DeleteApiKeyImpl>
    implements _$$DeleteApiKeyImplCopyWith<$Res> {
  __$$DeleteApiKeyImplCopyWithImpl(
      _$DeleteApiKeyImpl _value, $Res Function(_$DeleteApiKeyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
  }) {
    return _then(_$DeleteApiKeyImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$DeleteApiKeyImpl implements DeleteApiKey {
  const _$DeleteApiKeyImpl(this.id);

  @override
  final String id;

  @override
  String toString() {
    return 'ApiKeyEvent.delete(id: $id)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteApiKeyImpl &&
            (identical(other.id, id) || other.id == id));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteApiKeyImplCopyWith<_$DeleteApiKeyImpl> get copyWith =>
      __$$DeleteApiKeyImplCopyWithImpl<_$DeleteApiKeyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return delete(id);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return delete?.call(id);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (delete != null) {
      return delete(id);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return delete(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return delete?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (delete != null) {
      return delete(this);
    }
    return orElse();
  }
}

abstract class DeleteApiKey implements ApiKeyEvent {
  const factory DeleteApiKey(final String id) = _$DeleteApiKeyImpl;

  String get id;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteApiKeyImplCopyWith<_$DeleteApiKeyImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ToggleApiKeyStatusImplCopyWith<$Res> {
  factory _$$ToggleApiKeyStatusImplCopyWith(_$ToggleApiKeyStatusImpl value,
          $Res Function(_$ToggleApiKeyStatusImpl) then) =
      __$$ToggleApiKeyStatusImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, bool isActive});
}

/// @nodoc
class __$$ToggleApiKeyStatusImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$ToggleApiKeyStatusImpl>
    implements _$$ToggleApiKeyStatusImplCopyWith<$Res> {
  __$$ToggleApiKeyStatusImplCopyWithImpl(_$ToggleApiKeyStatusImpl _value,
      $Res Function(_$ToggleApiKeyStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? isActive = null,
  }) {
    return _then(_$ToggleApiKeyStatusImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ToggleApiKeyStatusImpl implements ToggleApiKeyStatus {
  const _$ToggleApiKeyStatusImpl(this.id, this.isActive);

  @override
  final String id;
  @override
  final bool isActive;

  @override
  String toString() {
    return 'ApiKeyEvent.toggleStatus(id: $id, isActive: $isActive)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ToggleApiKeyStatusImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, isActive);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ToggleApiKeyStatusImplCopyWith<_$ToggleApiKeyStatusImpl> get copyWith =>
      __$$ToggleApiKeyStatusImplCopyWithImpl<_$ToggleApiKeyStatusImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return toggleStatus(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return toggleStatus?.call(id, isActive);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (toggleStatus != null) {
      return toggleStatus(id, isActive);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return toggleStatus(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return toggleStatus?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (toggleStatus != null) {
      return toggleStatus(this);
    }
    return orElse();
  }
}

abstract class ToggleApiKeyStatus implements ApiKeyEvent {
  const factory ToggleApiKeyStatus(final String id, final bool isActive) =
      _$ToggleApiKeyStatusImpl;

  String get id;
  bool get isActive;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ToggleApiKeyStatusImplCopyWith<_$ToggleApiKeyStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateApiKeyRateLimitImplCopyWith<$Res> {
  factory _$$UpdateApiKeyRateLimitImplCopyWith(
          _$UpdateApiKeyRateLimitImpl value,
          $Res Function(_$UpdateApiKeyRateLimitImpl) then) =
      __$$UpdateApiKeyRateLimitImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, int> rateLimit});
}

/// @nodoc
class __$$UpdateApiKeyRateLimitImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$UpdateApiKeyRateLimitImpl>
    implements _$$UpdateApiKeyRateLimitImplCopyWith<$Res> {
  __$$UpdateApiKeyRateLimitImplCopyWithImpl(_$UpdateApiKeyRateLimitImpl _value,
      $Res Function(_$UpdateApiKeyRateLimitImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? rateLimit = null,
  }) {
    return _then(_$UpdateApiKeyRateLimitImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == rateLimit
          ? _value._rateLimit
          : rateLimit // ignore: cast_nullable_to_non_nullable
              as Map<String, int>,
    ));
  }
}

/// @nodoc

class _$UpdateApiKeyRateLimitImpl implements UpdateApiKeyRateLimit {
  const _$UpdateApiKeyRateLimitImpl(this.id, final Map<String, int> rateLimit)
      : _rateLimit = rateLimit;

  @override
  final String id;
  final Map<String, int> _rateLimit;
  @override
  Map<String, int> get rateLimit {
    if (_rateLimit is EqualUnmodifiableMapView) return _rateLimit;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_rateLimit);
  }

  @override
  String toString() {
    return 'ApiKeyEvent.updateRateLimit(id: $id, rateLimit: $rateLimit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateApiKeyRateLimitImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._rateLimit, _rateLimit));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_rateLimit));

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateApiKeyRateLimitImplCopyWith<_$UpdateApiKeyRateLimitImpl>
      get copyWith => __$$UpdateApiKeyRateLimitImplCopyWithImpl<
          _$UpdateApiKeyRateLimitImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return updateRateLimit(id, rateLimit);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return updateRateLimit?.call(id, rateLimit);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateRateLimit != null) {
      return updateRateLimit(id, rateLimit);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return updateRateLimit(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return updateRateLimit?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateRateLimit != null) {
      return updateRateLimit(this);
    }
    return orElse();
  }
}

abstract class UpdateApiKeyRateLimit implements ApiKeyEvent {
  const factory UpdateApiKeyRateLimit(
          final String id, final Map<String, int> rateLimit) =
      _$UpdateApiKeyRateLimitImpl;

  String get id;
  Map<String, int> get rateLimit;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateApiKeyRateLimitImplCopyWith<_$UpdateApiKeyRateLimitImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateApiKeyPermissionsImplCopyWith<$Res> {
  factory _$$UpdateApiKeyPermissionsImplCopyWith(
          _$UpdateApiKeyPermissionsImpl value,
          $Res Function(_$UpdateApiKeyPermissionsImpl) then) =
      __$$UpdateApiKeyPermissionsImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, List<String> permissions});
}

/// @nodoc
class __$$UpdateApiKeyPermissionsImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$UpdateApiKeyPermissionsImpl>
    implements _$$UpdateApiKeyPermissionsImplCopyWith<$Res> {
  __$$UpdateApiKeyPermissionsImplCopyWithImpl(
      _$UpdateApiKeyPermissionsImpl _value,
      $Res Function(_$UpdateApiKeyPermissionsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? permissions = null,
  }) {
    return _then(_$UpdateApiKeyPermissionsImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == permissions
          ? _value._permissions
          : permissions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _$UpdateApiKeyPermissionsImpl implements UpdateApiKeyPermissions {
  const _$UpdateApiKeyPermissionsImpl(this.id, final List<String> permissions)
      : _permissions = permissions;

  @override
  final String id;
  final List<String> _permissions;
  @override
  List<String> get permissions {
    if (_permissions is EqualUnmodifiableListView) return _permissions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_permissions);
  }

  @override
  String toString() {
    return 'ApiKeyEvent.updatePermissions(id: $id, permissions: $permissions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateApiKeyPermissionsImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality()
                .equals(other._permissions, _permissions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_permissions));

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateApiKeyPermissionsImplCopyWith<_$UpdateApiKeyPermissionsImpl>
      get copyWith => __$$UpdateApiKeyPermissionsImplCopyWithImpl<
          _$UpdateApiKeyPermissionsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return updatePermissions(id, permissions);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return updatePermissions?.call(id, permissions);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updatePermissions != null) {
      return updatePermissions(id, permissions);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return updatePermissions(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return updatePermissions?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updatePermissions != null) {
      return updatePermissions(this);
    }
    return orElse();
  }
}

abstract class UpdateApiKeyPermissions implements ApiKeyEvent {
  const factory UpdateApiKeyPermissions(
          final String id, final List<String> permissions) =
      _$UpdateApiKeyPermissionsImpl;

  String get id;
  List<String> get permissions;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateApiKeyPermissionsImplCopyWith<_$UpdateApiKeyPermissionsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateApiKeyMetadataImplCopyWith<$Res> {
  factory _$$UpdateApiKeyMetadataImplCopyWith(_$UpdateApiKeyMetadataImpl value,
          $Res Function(_$UpdateApiKeyMetadataImpl) then) =
      __$$UpdateApiKeyMetadataImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, Map<String, dynamic> metadata});
}

/// @nodoc
class __$$UpdateApiKeyMetadataImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$UpdateApiKeyMetadataImpl>
    implements _$$UpdateApiKeyMetadataImplCopyWith<$Res> {
  __$$UpdateApiKeyMetadataImplCopyWithImpl(_$UpdateApiKeyMetadataImpl _value,
      $Res Function(_$UpdateApiKeyMetadataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? metadata = null,
  }) {
    return _then(_$UpdateApiKeyMetadataImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc

class _$UpdateApiKeyMetadataImpl implements UpdateApiKeyMetadata {
  const _$UpdateApiKeyMetadataImpl(this.id, final Map<String, dynamic> metadata)
      : _metadata = metadata;

  @override
  final String id;
  final Map<String, dynamic> _metadata;
  @override
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'ApiKeyEvent.updateMetadata(id: $id, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateApiKeyMetadataImpl &&
            (identical(other.id, id) || other.id == id) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, id, const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateApiKeyMetadataImplCopyWith<_$UpdateApiKeyMetadataImpl>
      get copyWith =>
          __$$UpdateApiKeyMetadataImplCopyWithImpl<_$UpdateApiKeyMetadataImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return updateMetadata(id, metadata);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return updateMetadata?.call(id, metadata);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateMetadata != null) {
      return updateMetadata(id, metadata);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return updateMetadata(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return updateMetadata?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateMetadata != null) {
      return updateMetadata(this);
    }
    return orElse();
  }
}

abstract class UpdateApiKeyMetadata implements ApiKeyEvent {
  const factory UpdateApiKeyMetadata(
          final String id, final Map<String, dynamic> metadata) =
      _$UpdateApiKeyMetadataImpl;

  String get id;
  Map<String, dynamic> get metadata;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateApiKeyMetadataImplCopyWith<_$UpdateApiKeyMetadataImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UpdateApiKeyExpiryDateImplCopyWith<$Res> {
  factory _$$UpdateApiKeyExpiryDateImplCopyWith(
          _$UpdateApiKeyExpiryDateImpl value,
          $Res Function(_$UpdateApiKeyExpiryDateImpl) then) =
      __$$UpdateApiKeyExpiryDateImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String id, DateTime? expiryDate});
}

/// @nodoc
class __$$UpdateApiKeyExpiryDateImplCopyWithImpl<$Res>
    extends _$ApiKeyEventCopyWithImpl<$Res, _$UpdateApiKeyExpiryDateImpl>
    implements _$$UpdateApiKeyExpiryDateImplCopyWith<$Res> {
  __$$UpdateApiKeyExpiryDateImplCopyWithImpl(
      _$UpdateApiKeyExpiryDateImpl _value,
      $Res Function(_$UpdateApiKeyExpiryDateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? expiryDate = freezed,
  }) {
    return _then(_$UpdateApiKeyExpiryDateImpl(
      null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      freezed == expiryDate
          ? _value.expiryDate
          : expiryDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$UpdateApiKeyExpiryDateImpl implements UpdateApiKeyExpiryDate {
  const _$UpdateApiKeyExpiryDateImpl(this.id, this.expiryDate);

  @override
  final String id;
  @override
  final DateTime? expiryDate;

  @override
  String toString() {
    return 'ApiKeyEvent.updateExpiryDate(id: $id, expiryDate: $expiryDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateApiKeyExpiryDateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.expiryDate, expiryDate) ||
                other.expiryDate == expiryDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, id, expiryDate);

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateApiKeyExpiryDateImplCopyWith<_$UpdateApiKeyExpiryDateImpl>
      get copyWith => __$$UpdateApiKeyExpiryDateImplCopyWithImpl<
          _$UpdateApiKeyExpiryDateImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() load,
    required TResult Function(ApiKey apiKey) create,
    required TResult Function(ApiKey apiKey) update,
    required TResult Function(String id) delete,
    required TResult Function(String id, bool isActive) toggleStatus,
    required TResult Function(String id, Map<String, int> rateLimit)
        updateRateLimit,
    required TResult Function(String id, List<String> permissions)
        updatePermissions,
    required TResult Function(String id, Map<String, dynamic> metadata)
        updateMetadata,
    required TResult Function(String id, DateTime? expiryDate) updateExpiryDate,
  }) {
    return updateExpiryDate(id, expiryDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? load,
    TResult? Function(ApiKey apiKey)? create,
    TResult? Function(ApiKey apiKey)? update,
    TResult? Function(String id)? delete,
    TResult? Function(String id, bool isActive)? toggleStatus,
    TResult? Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult? Function(String id, List<String> permissions)? updatePermissions,
    TResult? Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult? Function(String id, DateTime? expiryDate)? updateExpiryDate,
  }) {
    return updateExpiryDate?.call(id, expiryDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? load,
    TResult Function(ApiKey apiKey)? create,
    TResult Function(ApiKey apiKey)? update,
    TResult Function(String id)? delete,
    TResult Function(String id, bool isActive)? toggleStatus,
    TResult Function(String id, Map<String, int> rateLimit)? updateRateLimit,
    TResult Function(String id, List<String> permissions)? updatePermissions,
    TResult Function(String id, Map<String, dynamic> metadata)? updateMetadata,
    TResult Function(String id, DateTime? expiryDate)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateExpiryDate != null) {
      return updateExpiryDate(id, expiryDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(LoadApiKeys value) load,
    required TResult Function(CreateApiKey value) create,
    required TResult Function(UpdateApiKey value) update,
    required TResult Function(DeleteApiKey value) delete,
    required TResult Function(ToggleApiKeyStatus value) toggleStatus,
    required TResult Function(UpdateApiKeyRateLimit value) updateRateLimit,
    required TResult Function(UpdateApiKeyPermissions value) updatePermissions,
    required TResult Function(UpdateApiKeyMetadata value) updateMetadata,
    required TResult Function(UpdateApiKeyExpiryDate value) updateExpiryDate,
  }) {
    return updateExpiryDate(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(LoadApiKeys value)? load,
    TResult? Function(CreateApiKey value)? create,
    TResult? Function(UpdateApiKey value)? update,
    TResult? Function(DeleteApiKey value)? delete,
    TResult? Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult? Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult? Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult? Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult? Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
  }) {
    return updateExpiryDate?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(LoadApiKeys value)? load,
    TResult Function(CreateApiKey value)? create,
    TResult Function(UpdateApiKey value)? update,
    TResult Function(DeleteApiKey value)? delete,
    TResult Function(ToggleApiKeyStatus value)? toggleStatus,
    TResult Function(UpdateApiKeyRateLimit value)? updateRateLimit,
    TResult Function(UpdateApiKeyPermissions value)? updatePermissions,
    TResult Function(UpdateApiKeyMetadata value)? updateMetadata,
    TResult Function(UpdateApiKeyExpiryDate value)? updateExpiryDate,
    required TResult orElse(),
  }) {
    if (updateExpiryDate != null) {
      return updateExpiryDate(this);
    }
    return orElse();
  }
}

abstract class UpdateApiKeyExpiryDate implements ApiKeyEvent {
  const factory UpdateApiKeyExpiryDate(
          final String id, final DateTime? expiryDate) =
      _$UpdateApiKeyExpiryDateImpl;

  String get id;
  DateTime? get expiryDate;

  /// Create a copy of ApiKeyEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateApiKeyExpiryDateImplCopyWith<_$UpdateApiKeyExpiryDateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
