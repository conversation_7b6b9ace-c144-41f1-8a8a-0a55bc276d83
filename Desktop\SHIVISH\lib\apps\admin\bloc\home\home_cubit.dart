import 'dart:async';
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:shivish/apps/admin/bloc/home/<USER>';
import 'package:shivish/shared/services/notification_service.dart';
import 'package:shivish/shared/models/notification/notification_status.dart';
import 'package:shivish/shared/models/notification/notification_model.dart';

@injectable
class HomeCubit extends Cubit<HomeState> {
  final NotificationService? _notificationService;

  HomeCubit(this._notificationService) : super(const HomeState());

  Stream<List<NotificationModel>> getUserNotifications() {
    // Use a StreamController to handle errors properly
    final controller = StreamController<List<NotificationModel>>();

    try {
      // Check if notification service is available
      if (_notificationService == null) {
        debugPrint('NotificationService is null, returning empty list');
        controller.add([]);
        controller.close();
        return controller.stream;
      }

      // Try to get notifications but handle errors
      _notificationService.getUserNotifications('admin').listen(
        (notifications) {
          // Add notifications to the stream
          controller.add(notifications);
        },
        onError: (error) {
          // If there's an error, just return an empty list
          debugPrint('Error getting notifications: $error');
          controller.add([]);
        },
        onDone: () {
          controller.close();
        },
      );
    } catch (e) {
      // If there's an exception, return an empty list
      debugPrint('Exception getting notifications: $e');
      controller.add([]);
      controller.close();
    }

    return controller.stream;
  }

  Future<void> markNotificationAsRead(String notificationId) async {
    try {
      // Check if notification service is available
      if (_notificationService == null) {
        debugPrint(
            'NotificationService is null, cannot mark notification as read');
        return;
      }

      try {
        await _notificationService.updateNotificationStatus(
          notificationId,
          NotificationStatus.read,
        );
      } catch (updateError) {
        // Just log the error
        debugPrint('Error marking notification as read: $updateError');
      }

      // Always refresh the dashboard data
      await loadDashboardData().catchError((error) {
        debugPrint('Error refreshing dashboard data: $error');
      });
    } catch (e) {
      debugPrint('Error in markNotificationAsRead: $e');
      // Don't show error to user
    }
  }

  Future<void> loadDashboardData() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      // If notification service is not available, just show 0 unread notifications
      if (_notificationService == null) {
        debugPrint(
            'NotificationService is null, showing 0 unread notifications');
        emit(state.copyWith(
          isLoading: false,
          unreadNotificationsCount: 0,
        ));
        return;
      }

      // Create a completer to handle the async operation
      final completer = Completer<int>();

      // Try to get notifications but handle errors
      try {
        // Set a timeout to prevent hanging
        Timer(const Duration(seconds: 5), () {
          if (!completer.isCompleted) {
            debugPrint('Notification count request timed out');
            completer.complete(0);
          }
        });

        // Try to get the first batch of notifications
        _notificationService.getUserNotifications('admin').listen(
          (notifications) {
            if (!completer.isCompleted) {
              final unreadCount = notifications
                  .where((notification) =>
                      notification.status == NotificationStatus.unread)
                  .length;
              completer.complete(unreadCount);
            }
          },
          onError: (error) {
            debugPrint('Error getting notification count: $error');
            if (!completer.isCompleted) {
              completer.complete(0);
            }
          },
          onDone: () {
            if (!completer.isCompleted) {
              completer.complete(0);
            }
          },
        );
      } catch (notificationError) {
        debugPrint('Exception getting notification count: $notificationError');
        if (!completer.isCompleted) {
          completer.complete(0);
        }
      }

      // Wait for the notification count
      final unreadCount = await completer.future;

      // Update the state with the count
      emit(state.copyWith(
        isLoading: false,
        unreadNotificationsCount: unreadCount,
      ));
    } catch (e) {
      debugPrint('Error in loadDashboardData: $e');
      emit(state.copyWith(
        isLoading: false,
        error: null, // Don't show error to user
        unreadNotificationsCount: 0,
      ));
    }
  }

  Future<void> markNotificationsAsRead() async {
    try {
      emit(state.copyWith(isLoading: true, error: null));

      // If notification service is not available, just update the UI
      if (_notificationService == null) {
        debugPrint(
            'NotificationService is null, cannot mark notifications as read');
        emit(state.copyWith(
          isLoading: false,
          unreadNotificationsCount: 0,
        ));
        return;
      }

      try {
        // Get all notifications
        final notifications = await _notificationService
            .getUserNotifications('admin') // Admin user ID
            .first;

        // Mark all unread notifications as read
        for (final notification in notifications) {
          if (notification.status == NotificationStatus.unread) {
            try {
              await _notificationService.updateNotificationStatus(
                notification.id,
                NotificationStatus.read,
              );
            } catch (updateError) {
              // Just log the error and continue
              debugPrint('Error marking notification as read: $updateError');
            }
          }
        }
      } catch (notificationError) {
        // Just log the error
        debugPrint('Error getting notifications: $notificationError');
      }

      // Always update the UI to show 0 unread notifications
      emit(state.copyWith(
        isLoading: false,
        unreadNotificationsCount: 0,
      ));
    } catch (e) {
      debugPrint('Error in markNotificationsAsRead: $e');
      emit(state.copyWith(
        isLoading: false,
        error: null, // Don't show error to user
        unreadNotificationsCount: 0,
      ));
    }
  }
}
